import requests
from PIL import Image
import os
import json
import re


class ImageDownloader:
    def __init__(self, url, img_path, img_name):
        self.url = url
        self.img_path = img_path
        self.img_name = self.sanitize_filename(img_name)

    def sanitize_filename(self, filename):
        # 去除制表符和换行符
        sanitized = re.sub(r'[\t\n]', '', filename)
        return sanitized

    def download(self):
        name = self.img_name
        path = os.path.join(self.img_path, name + '.jpg')

        # 确保路径存在
        os.makedirs(self.img_path, exist_ok=True)

        # 发送HTTP请求获取图片数据
        response = requests.get(self.url, stream=True)
        response.raise_for_status()  # 如果响应状态码不是200，会抛出异常

        # 将图片数据写入本地文件
        with open(path, 'wb') as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)

        print(f"图片已成功下载并保存到 {path}")
        img_info = self.get_image_info(path)
        return img_info

    def get_image_info(self, path):
        # 打开图片
        with Image.open(path) as img:
            # 获取图片的宽度和高度
            width, height = img.size

            # 获取图片的文件大小
            file_size = os.path.getsize(path)

            # 打印信息
            img_info = {
                'img_name': self.img_name,
                'img_url': self.url,
                'img_size': file_size,
                'width': width,
                'height': height,
                'resolution': f'{width} × {height} px',
            }

            # print(json.dumps(img_info, indent=4))
            return img_info


# 示例使用
if __name__ == "__main__":
    image_url = "https://sxd-tx-1315371622.cos.ap-nanjing.myqcloud.com/cloud/policy/1690340987437_iZiKpCPM.jpg?imageMogr2/format/webp/ignore-error/1"  # 替换为实际的图片URL
    save_path = "F:/MongoDB/museum/"  # 替换为希望保存的本地路径
    img_name = '商青铜虎形饰\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n'
    downloader = ImageDownloader(image_url, save_path, img_name)
    img_info = downloader.download()
    if img_info:
        print(json.dumps(img_info, indent=4))