import csv
import functools

from DrissionPage import ChromiumPage
from DrissionPage import WebPage
from DrissionPage._elements.chromium_element import ChromiumElement
from DrissionPage._pages.chromium_tab import ChromiumTab
import json
import time
import re
import os
import uuid
from typing import List, Dict, Optional

from websocket import continuous_frame

from mysql_tools import MySQLDBTool
from minio_tools import MinioFileUploader
from img_download import ImageDownloader
from logger import LoggerManager
from urllib.parse import urlparse

# 获取logger实例
logger = LoggerManager.setup_logger('XiechengCrawler', 'xiecheng_crawler.log')


def log_execution(func):
    """函数执行日志装饰器"""

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger.info(f'开始执行: {func.__name__}')
        try:
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            logger.error(f'执行 {func.__name__} 时发生错误: {str(e)}')
            raise

    return wrapper


class XiechengCrawler:
    """携程旅游景点爬虫类

    该类用于爬取携程旅游网站的景点信息，包括景点基本信息、图片、评论、周边景点等数据。

    Attributes:
        browser (ChromiumPage): 浏览器实例
        base_url (str): 携程旅游网站基础URL
        page (WebPage): 网页实例
        WAIT_TIME (dict): 等待时间配置
        MINIO_CONFIG (dict): MinIO配置信息
    """

    def __init__(self, base_url: str):
        """初始化爬虫实例

        Args:
            base_url: 携程旅游网站基础URL
        """
        self.browser = ChromiumPage()
        self.base_url = base_url
        self.page = WebPage('s')

        # 等待时间配置
        self.WAIT_TIME = {
            'SHORT': 0.05,
            'MEDIUM': 2,
            'LONG': 5,
            'EXTRA_LONG': 10
        }

        # MinIO配置
        self.MINIO_CONFIG = {
            'endpoint': '*************:9000',
            'access_key': 'trip_admin',
            'secret_key': 'trip_admin_scyd@lab1234',
            'secure': False
        }

        self.save_path = "F:/MinIO/Xiecheng"

        # 创建图片下载器实例
        self.downloader = None

    # @log_execution
    def sanitize_filename(self, filename: str) -> str:
        """清理文件名中的特殊字符

        Args:
            filename: 原始文件名

        Returns:
            清理后的文件名
        """
        # 移除特殊字符
        filename = re.sub(r'[\t\n\r\f\v]', '', filename)

        # 替换不支持的字符为下划线
        filename = re.sub(r'[\\/:*?"<>|]', '_', filename)

        # 移除前后空格
        filename = filename.strip()

        # 如果需要，这里可以添加中文转拼音的逻辑
        # 但目前看来 MinIO 是支持中文的，只要正确编码就可以

        return filename

    @log_execution
    def click_page(self, initial_page: int, page: ChromiumTab) -> None:
        """点击翻页按钮

        Args:
            initial_page: 目标页码
            page: 当前页面实例
        """
        try:
            input_element = page.ele('.ant-input')
            time.sleep(self.WAIT_TIME['MEDIUM'])
            input_element.input(str(initial_page))
            time.sleep(self.WAIT_TIME['MEDIUM'])
            button_element = page.ele('.ant-btn')
            button_element.click()
            time.sleep(self.WAIT_TIME['MEDIUM'])
        except Exception as e:
            logger.error(f'翻页失败: {str(e)}')

    @log_execution
    def download_and_upload_image(self, img_url: str, name: str, img_suffix: str = '') -> str:
        """下载并上传图片到MinIO

        Args:
            img_url: 图片URL
            name: 图片名称 (包含完整的文件名)
            img_suffix: 图片类型 (first/comment/more_img)

        Returns:
            MinIO中的存储URL
        """
        try:
            # 从完整文件名中提取景点名称
            spot_name = name.split('_')[0]  # 获取第一个下划线前的内容作为景点名称

            # 创建带后缀的目录路径
            dir_path = os.path.join(self.save_path, img_suffix)

            # 检查并创建目录
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
            else:
                # 清空目录
                for file in os.listdir(dir_path):
                    file_path = os.path.join(dir_path, file)
                    try:
                        if os.path.isfile(file_path):
                            os.unlink(file_path)
                    except Exception as e:
                        logger.error(f'清理目录失败: {str(e)}')

            # 生成安全的文件名
            safe_name = self.sanitize_filename(name)
            img_name = f"{safe_name}"
            path = os.path.join(dir_path, f"{img_name}.jpg")

            # 构建 MinIO 对象名称，使用景点名称作为主目录
            spot_dir = self.sanitize_filename(spot_name)
            object_name = f"crawl_pic/xiecheng_pic/{spot_dir}/{img_suffix}/{img_name}.jpg"
            object_name = object_name.replace('//', '/')  # 删除重复的斜杠

            # 如果downloader实例不存在，创建一个新的实例
            if self.downloader is None:
                self.downloader = ImageDownloader(img_url, dir_path, img_name)
            else:
                # 更新现有downloader的参数
                self.downloader.img_url = img_url
                self.downloader.save_path = dir_path
                self.downloader.img_name = img_name

            try:
                self.page.download(img_url, dir_path, img_name, 'jpg', show_msg=True)
                img_json = self.downloader.get_image_info(path)
            except Exception as e:
                logger.warning(f'使用备选方法下载图片: {str(e)}')
                img_json = self.downloader.download()

            # 上传到 MinIO
            uploader = MinioFileUploader(**self.MINIO_CONFIG)
            if os.path.exists(path):
                try:
                    result = uploader.upload_file('trip-data', object_name, path)
                    if result:
                        logger.info(f"Successfully uploaded {object_name}")
                        return f'trip-data/{object_name}'
                    else:
                        logger.error("Upload failed with no exception")
                        return ''
                except Exception as e:
                    logger.error(f'上传图片到MinIO失败: {str(e)}')
                    return ''
            else:
                logger.error(f'图片文件不存在: {path}')
                return ''

        except Exception as e:
            logger.error(f'图片处理失败: {str(e)}')
            return ''

    @log_execution
    def get_spot_details(self, element: str) -> Dict:
        """获取景点详细信息

        Args:
            element: 景点页面URL

        Returns:
            景点详细信息字典
        """
        try:

            self.browser.get(element)
            time.sleep(self.WAIT_TIME['MEDIUM'])

            name = self.browser.ele(".titleView").ele("tag:h1").text
            time.sleep(self.WAIT_TIME['SHORT'])

            div = self.browser.ele(".baseInfoContent").eles(".baseInfoItem")
            time.sleep(self.WAIT_TIME['SHORT'])
            try:
                div2 = self.browser.ele(".detailModule normalModule").eles(".moduleContent")
                time.sleep(self.WAIT_TIME['SHORT'])
            except Exception as e:
                logger.error(f'获取景点详情失败: {str(e)}')
                div2 = []

            # 获取基本信息
            details = {
                'name': name,
                'url': element,
                'hot': self._get_element_text('.heatView', chain_selectors=[".heatScoreView", ".heatScoreText"]),
                'level': self._get_element_text('.titleTips', chain_selectors=["tag:span"]),
                'site': self._get_div_element_text(div, 0, chain_selectors=[".baseInfoText"]),
                'open_time': self._get_div_element_text(div, 1, chain_selectors=[
                    ".baseInfoText cursor openTimeText"]) + '\n' + self._get_div_element_text(div2, 1,
                                                                                              chain_selectors=[]),
                'phone_num': self._get_div_element_text(div, 2, chain_selectors=[".baseInfoText"]),
                'score': self._get_element_text('.commentScore', chain_selectors=["tag:p"]) + '/5分',
                'intro': self._get_div_element_text(div2, 0, chain_selectors=[])[:-3],
                'preferential_policies': self._get_div_element_text(div2, 2, chain_selectors=[]),
                'service': self._get_div_element_text(div2, 3, chain_selectors=[]),
                'comments_num': self._get_element_text('.commentModule normalModule', chain_selectors=[".moduleTitle"]),
                'postive_review_num': self._get_element_text('.commentModule normalModule',
                                                             chain_selectors=[".hotTag"]),
                'comments': self._get_comments(name, element)
            }

            # 获取周边景点
            details.update({'surround_spot': self._get_surrounding_spots()})

            # 获取用户问答
            details.update({'User_QA': self._get_user_qa()})

            # 获取图片信息
            dic_img = self._get_all_images(name)

            details.update({'first_img_oss': dic_img['first_img_oss'], 'first_img_ori': dic_img['first_img_ori']})
            details.update({'img_oss': dic_img['img_oss'], 'img_ori': dic_img['img_ori']})

            return details

        except Exception as e:
            logger.error(f'获取景点详情失败: {str(e)}')
            return {}

    @log_execution
    def crawl(self, province_name: str = '甘肃') -> None:
        """执行爬虫主程序

        爬取指定省份的所有城市景点信息，并存储到MySQL数据库中。
        """
        try:
            mysql_tool = MySQLDBTool(
                username='root',
                password='123456',
                database='wenlv'
            )

            self.browser.get(self.base_url)
            time.sleep(self.WAIT_TIME['MEDIUM'])

            # 获取省份列表
            province_list = self._get_province_list()
            if not province_list:
                logger.error('获取省份列表失败')
                return

            # 当前爬取甘肃省
            # province = "甘肃"
            # province_index = 22
            province_dict = {province_element.text.split()[0]: province_index for province_index, province_element in
                             enumerate(province_list)}
            province_index = province_dict[province_name]
            # 获取城市列表
            city_list = self._get_city_list(province_list[province_index])

            # 遍历城市列表
            # city_index用于表示索引, [0:]表示从索引为0的城市开始，如果要从第二个开始需要将后面索引一起修改
            for city_index, city_url in enumerate(city_list['urls'][0:], 0):
                city_name = city_list['names'][city_index]
                logger.info(f'开始爬取城市: {city_name}')

                spot_urls = self._get_spot_urls(city_url)

                # 遍历景点URL
                for spot_url in spot_urls:
                    try:
                        spot_data = self.get_spot_details(spot_url)
                        spot_data.update({
                            'province': province_name,
                            'city': city_name
                        })

                        # 处理数据并存入数据库
                        self._save_to_database(mysql_tool, spot_data)
                        logger.info(f'成功爬取景点: {spot_data["name"]}')

                    except Exception as e:
                        logger.error(f'处理景点数据失败: {str(e)}')
                        continue

        except Exception as e:
            logger.error(f'爬虫运行失败: {str(e)}')

    # @log_execution
    def _get_element_text(self, selector: str, default: str = '', chain_selectors: List[str] = None) -> str:
        """获取元素文本

        Args:
            selector: 基础CSS选择器
            default: 默认返回值
            chain_selectors: 链式选择器列表，用于多层级元素选择

        Returns:
            元素文本或默认值
        """
        try:
            element = self.browser.ele(selector)
            time.sleep(self.WAIT_TIME['SHORT'])
            if chain_selectors:
                for chain_selector in chain_selectors:
                    element = element.ele(chain_selector)
                    time.sleep(self.WAIT_TIME['SHORT'])
            return element.text
        except Exception:
            return default

    @log_execution
    def _get_element_list_text(self, list_selector: str, index: int, chain_selectors: List[str] = None,
                               default: str = '') -> str:
        """获取元素列表中指定索引元素的文本

        Args:
            list_selector: 用于获取元素列表的CSS选择器
            index: 元素列表中的索引
            chain_selectors: 链式选择器列表，用于多层级元素选择
            default: 默认返回值

        Returns:
            指定元素的文本或默认值
        """
        try:
            # 获取元素列表
            elements = self.browser.ele(list_selector).eles(list_selector)
            time.sleep(self.WAIT_TIME['SHORT'])
            # 获取指定索引的元素
            element = elements[index]

            # 链式处理子元素
            if chain_selectors:
                for chain_selector in chain_selectors:
                    element = element.ele(chain_selector)
                    time.sleep(self.WAIT_TIME['SHORT'])

            return element.text
        except Exception as e:
            logger.warning(
                f'获取元素文本失败 - 列表选择器: {list_selector}, 索引: {index}, 链式选择器: {chain_selectors}, 错误: {str(e)}')
            return default

    # @log_execution
    def _get_div_element_text(self, div_elements: List[ChromiumElement], index: int, chain_selectors: List[str] = None,
                              default: str = '') -> str:
        """获取div元素列表中指定索引元素的文本

        Args:
            div_elements: 已获取的元素列表
            index: 元素列表中的索引
            chain_selectors: 链式选择器列表，用于多层级元素选择
            default: 默认返回值

        Returns:
            指定元素的文本或默认值
        """
        try:
            # 获取指定索引的元素
            element = div_elements[index]

            # 链式处理子元素
            for chain_selector in chain_selectors:
                element = element.ele(chain_selector)

            return element.text
        except Exception as e:
            logger.warning(f'获取div元素文本失败 - 索引: {index}, 链式选择器: {chain_selectors}, 错误: {str(e)}')
            return default

    @log_execution
    def _save_to_database(self, mysql_tool: MySQLDBTool, data: Dict) -> None:
        """保存数据到数据库

        Args:
            mysql_tool: MySQL工具实例
            data: 待保存的数据
        """
        try:
            # 处理列表和字典类型的数据为字符串
            processed_data = {}
            for key, value in data.items():
                if isinstance(value, (list, dict)):
                    processed_data[key] = json.dumps(value, ensure_ascii=False)
                else:
                    processed_data[key] = value

            # 检查景点URL是否存在
            existing_record = mysql_tool.check_record_exists('ods_trip_origin_scenic_spots_ctrip', {'url': data['url']})

            if existing_record:
                # 更新现有记录
                updated = mysql_tool.update_record('ods_trip_origin_scenic_spots_ctrip',
                                                   {'url': data['url']},
                                                   processed_data)
                if updated:
                    logger.info(f"数据更新成功，URL: {data['url']}")
                else:
                    logger.error(f"数据更新失败，URL: {data['url']}")
            else:
                # 插入新记录
                inserted_id = mysql_tool.insert_one('ods_trip_origin_scenic_spots_ctrip', processed_data)
                if inserted_id:
                    logger.info(f"数据插入成功，ID: {inserted_id}")
                else:
                    logger.error("数据插入失败")

        except Exception as e:
            logger.error(f"数据库操作失败: {str(e)}")

    @log_execution
    def _get_province_list(self) -> List[ChromiumElement]:
        """获取省份列表

        Returns:
            省份URL列表
        """
        try:
            province_elements = self.browser.eles('.city-selector-tab')[1].ele('.city-selector-tab-main').eles(
                '.city-selector-tab-main-city')
            return [element for element in province_elements]
        except Exception as e:
            logger.error(f'获取省份列表失败: {str(e)}')
            return []

    @log_execution
    def _get_city_list(self, province: ChromiumElement) -> Dict:
        """获取城市列表

        Args:
            province: 省份的页面位置

        Returns:
            城市信息字典，包含名称和URL
        """
        try:
            city_elements = province.ele('.city-selector-tab-main-city-list').eles('tag:a')
            return {
                'names': [ele.text for ele in city_elements],
                'urls': [ele.attr('href') for ele in city_elements]
            }
        except Exception as e:
            logger.error(f'获取城市列表失败: {str(e)}')
            return {'names': [], 'urls': []}

    @log_execution
    def _get_spot_urls(self, city_url: str) -> List[str]:
        """获取景点URL列表

        Args:
            city_url: 城市页面URL

        Returns:
            景点URL列表
        """
        try:
            self.browser.get(city_url)
            time.sleep(self.WAIT_TIME['MEDIUM'])

            col_list = self.browser.ele('.entry-container').eles('.entry-item')
            # 点击打开景点列表
            col_list[0].click()
            time.sleep(self.WAIT_TIME['MEDIUM'])
            # 获取新打开的页面的tab，tabs[0]为新打开网页


            url  = self.browser.url
            logger.info(f'<origin url>: {url}')
            transformed_url =None
            if 'place' in url :
                parsed_url = urlparse(url)
                path = parsed_url.path  # 获取路径部分，例如 '/place/suihua856.html'
                path_segments = path.split('/')  # 按 '/' 分割路径
                if path_segments:
                    last_segment = path_segments[-1]  # 获取最后一个部分，例如 'suihua856.html'
                    transformed_url = f'https://you.ctrip.com/sight/{last_segment}'
                    logger.info(f'<transformed_url>: {transformed_url}')
            if not transformed_url:
                return []

            self.browser.new_tab(transformed_url)
            tabs = self.browser.get_tabs()
            page = tabs[0]
            if 'sight' not in  page.url: #判断是否是景点的 tab
                return []

            time.sleep(self.WAIT_TIME['SHORT'])
            current_city_spots = page.ele('.quickFilter_quickFilterBox__K_wf_').ele(
                '.quickFilter_checkBox__ECg2F false')
            current_city_spots.click()

            time.sleep(self.WAIT_TIME['SHORT'])
            # 起始页数，目标页数
            initial_page = 1
            target_page = 11
            all_spot_urls = []
            while initial_page < target_page:
                spot_list = page.ele('.cardListBox_box__lMuWz').eles('.sightItemCard_box__2FUEj ')
                time.sleep(self.WAIT_TIME['SHORT'])

                all_spot_urls.extend(
                    [ele.ele('.titleModule_name__Li4Tv').ele('tag:a').attr('href') for ele in spot_list])

                initial_page += 1
                self.click_page(initial_page, page)
                time.sleep(self.WAIT_TIME['MEDIUM'])

            page.close()
            return list(set(all_spot_urls))
        except Exception as e:
            logger.error(f'获取景点URL列表失败: {str(e)}')
            return []

    @log_execution
    def _get_comments(self, spot_name: str, spot_url: str) -> List[Dict]:
        """获取评论信息

        Args:
            spot_name: 景点名称

        Returns:
            评论信息列表
        """
        try:
            comments = []
            comment_elements = self.browser.ele(".commentModuleRef").ele(".commentList").eles(".commentItem")

            for element in comment_elements[:1]:
                uid = str(uuid.uuid4())
                comment = {
                    'name': spot_name,
                    'scenic_url': spot_url,
                    'user_id': uid,
                    'comment_text': element.ele(".contentInfo").ele(".commentDetail").text,
                    'score': element.ele(".contentInfo").ele(".averageScore").text,
                    'img': [],
                    'comment_time': element.ele(".commentFooter").ele(".commentTime").text,
                }

                # 获取评论图片
                img_elements = element.ele(".commentImgList").eles("tag:a")
                img_suffix = 0
                for img in img_elements[:1]:
                    img_url = img.attr('href')
                    if img_url:
                        stored_url = self.download_and_upload_image(
                            img_url,
                            f"{spot_name}_comment_{uid}_{img_suffix}"
                        )
                        if stored_url:
                            comment['img'].append(stored_url)
                    img_suffix += 1
                comments.append(comment)

            return comments
        except Exception as e:
            logger.error(f'获取评论信息失败: {str(e)}')
            return []

    @log_execution
    def _get_all_images(self, name: str) -> Dict:
        """获取景点所有图片信息

        Args:
            name: 景点名称

        Returns:
            包含首页图片和更多图片信息的字典
        """
        result = {
            'first_img_oss': [],
            'img_oss': [],
            'first_img_ori': [],
            'img_ori': []
        }

        # 获取首页轮播图
        try:
            first_img_list = self.browser.ele(".swiper-wrapper").eles("tag:div")

            for f in first_img_list[:5]:
                style = f.attr('style')
                if style:
                    start = style.find('url("') + len('url("')
                    end = style.find('")', start)
                    if start != -1 and end != -1:
                        img_url = style[start:end]
                        result['first_img_ori'].append(img_url)
                        stored_url = self.download_and_upload_image(
                            img_url,
                            f"{name}_first_{len(result['first_img_oss'])}"
                        )
                        if stored_url:
                            result['first_img_oss'].append(stored_url)
        except Exception as e:
            logger.error(f'获取首页图片失败: {str(e)}')

        # 获取更多图片
        try:
            more_img_url = self.browser.ele(
                ".swiperMain swiper-container swiper-container-initialized swiper-container-horizontal").ele(
                ".totalCont cursor").ele("tag:a").attr("href")
            self.browser.get(more_img_url)
            time.sleep(self.WAIT_TIME['MEDIUM'])
            more_img_list = self.browser.ele(".content photolist_wrap cf").eles(".item")

            for index, im in enumerate(more_img_list[:5]):
                img_url = im.ele(".itempic").ele("tag:img").attr('src')
                result['img_ori'].append(img_url)
                stored_url = self.download_and_upload_image(
                    img_url,
                    f"{name}_more_img_{index}"
                )
                if stored_url:
                    result['img_oss'].append(stored_url)
        except Exception as e:
            logger.error(f'获取更多图片失败: {str(e)}')

        return result

    @log_execution
    def _get_surrounding_spots(self) -> List[Dict]:
        """获取周边景点信息

        Returns:
            周边景点列表，包含景点名称、URL、距离和评分
        """
        surround_spot = []
        try:
            around_div = self.browser.ele(".nearbyModule normalModule").eles(".nearbyList")

            # 处理第一部分景点（带链接）
            spots_list = around_div[0].eles(".moduleitem")
            for spot_element in spots_list[:5]:
                spot_name = spot_element.ele(".contentTitle").text

                # 获取景点URL
                spot_url = ''
                try:
                    spot_element.click()
                    spot_tabs = self.browser.get_tabs()
                    spot_url = spot_tabs[0].url
                    spot_tabs[0].close()
                    time.sleep(self.WAIT_TIME['SHORT'])
                except Exception as e:
                    logger.warning(f'获取景点URL失败 - 景点: {spot_name}, 错误: {str(e)}')

                # 获取景点评分
                spot_score = ''
                try:
                    spot_score = spot_element.ele(".commentScore").ele("tag:b").text
                except Exception as e:
                    logger.warning(f'获取景点评分失败 - 景点: {spot_name}, 错误: {str(e)}')

                # 获取景点距离
                spot_distance = ''
                try:
                    spot_distance = spot_element.ele(".distanceDes").text
                except Exception as e:
                    logger.warning(f'获取景点距离失败 - 景点: {spot_name}, 错误: {str(e)}')

                surround_spot.append({
                    'name': spot_name,
                    'url': spot_url,
                    'distance': spot_distance,
                    'score': spot_score,
                })

            # 处理第二部分景点（不带链接）
            try:
                spot_list = around_div[1].eles(".moduleitem")
                for s_element in spot_list:
                    spot_name = s_element.ele(".contentTitle").text

                    # 获取景点评分
                    spot_score = ''
                    try:
                        spot_score = s_element.ele(".commentScore").ele("tag:b").text
                    except Exception as e:
                        logger.warning(f'获取景点评分失败 - 景点: {spot_name}, 错误: {str(e)}')

                    # 获取景点距离
                    spot_distance = ''
                    try:
                        spot_distance = s_element.ele(".distanceDes").text
                    except Exception as e:
                        logger.warning(f'获取景点距离失败 - 景点: {spot_name}, 错误: {str(e)}')

                    surround_spot.append({
                        'name': spot_name,
                        'url': '',
                        'distance': spot_distance,
                        'score': spot_score,
                    })
            except Exception as e:
                logger.warning(f'获取第二部分景点列表失败: {str(e)}')

            return surround_spot

        except Exception as e:
            logger.error(f'获取周边景点失败: {str(e)}')
            return []

    @log_execution
    def _get_user_qa(self) -> List[Dict]:
        """获取用户问答信息

        Returns:
            用户问答列表，包含问题ID、问题内容、关键词和回答
        """
        User_QA = []
        qa_page = None

        try:
            # 点击进入问答页面
            QA = self.browser.ele(".askModuleRef").ele(".moreUrl")
            time.sleep(self.WAIT_TIME['SHORT'])
            QA.click()
            time.sleep(self.WAIT_TIME['MEDIUM'])
            tabs = self.browser.get_tabs()
            qa_page = tabs[0]

            # 获取问答列表
            QA_list = qa_page.ele(".asktag_conlist").ele(".asklist").eles(".cf")
            time.sleep(self.WAIT_TIME['SHORT'])
            qa_list = [q.attr('href') for q in QA_list]

            # 处理前5个问答
            for qa in qa_list[:5]:
                qa_page.get(qa)
                time.sleep(self.WAIT_TIME['MEDIUM'])

                # 获取问题内容
                Question = qa_page.ele(".detailmain").ele(".ask_title").text
                time.sleep(self.WAIT_TIME['SHORT'])

                # 获取关键词
                key_word = qa_page.ele(".ask_tagline cf").eles(".asktag_item")
                time.sleep(self.WAIT_TIME['SHORT'])
                keyword_list = [k.text for k in key_word]

                # 获取回答列表
                Answer_list = []
                ans = qa_page.ele(".otheranswer_con").ele(".otheranswer_con").eles("tag:li")
                time.sleep(self.WAIT_TIME['SHORT'])

                # 获取每个回答，最多重试3次
                for user_ans in ans:
                    retries = 0
                    while retries < 3:
                        try:
                            Answer_list.append(user_ans.ele(".answer_text").text)
                            time.sleep(self.WAIT_TIME['SHORT'])
                            break
                        except Exception as e:
                            if retries < 2:
                                logger.warning(f'获取用户回答重试第{retries + 1}次')
                                retries += 1
                                time.sleep(self.WAIT_TIME['MEDIUM'])
                            else:
                                logger.error(f'获取用户回答失败 - URL: {qa}, 错误: {str(e)}')
                                break

                # 添加问答信息
                User_QA.append({
                    'question_id': str(uuid.uuid4()),
                    'question': Question,
                    'question_keyword': keyword_list,
                    'answer': Answer_list,
                })

        except Exception as e:
            logger.error(f'获取用户问答失败: {str(e)}')

        finally:
            # 关闭问答页面
            if qa_page:
                try:
                    qa_page.close()
                except Exception as e:
                    logger.error(f'关闭问答页面失败: {str(e)}')

            return User_QA

    @log_execution
    def export_provinces_cities_to_csv(self, output_file: str = 'provinces_cities.csv') -> None:
        """遍历并获取所有省市的清单，并写入到CSV文件

        Args:
            output_file: 输出CSV文件的路径
        """
        try:
            # 访问基础URL
            self.browser.get(self.base_url)
            time.sleep(self.WAIT_TIME['MEDIUM'])

            # 获取省份列表
            province_list = self._get_province_list()
            if not province_list:
                logger.error('获取省份列表失败')
                return

            # 准备CSV数据
            csv_data = []

            # 遍历所有省份
            for province_index, province_element in enumerate(province_list):
                try:
                    # 获取省份名称
                    province_name = province_element.text.split()[0]
                    logger.info(f'正在处理省份: {province_name} ({province_index + 1}/{len(province_list)})')

                    # 获取该省份的城市列表
                    city_list = self._get_city_list(province_element)

                    # 遍历城市列表
                    for city_index, city_url in enumerate(city_list['urls']):
                        city_name = city_list['names'][city_index]
                        # 添加到CSV数据
                        csv_data.append({
                            '省份': province_name,
                            '城市': city_name,
                            'url': city_url
                        })
                        logger.info(f'添加城市: {province_name} - {city_name}')
                except Exception as e:
                    logger.error(f'处理省份 {province_index} 时出错: {str(e)}')
                    continue

            # 写入CSV文件
            with open(output_file, 'w', newline='', encoding='utf-8-sig') as f:
                if csv_data:
                    fieldnames = ['省份', '城市', 'url']
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(csv_data)
                    logger.info(f'成功将 {len(csv_data)} 条省市数据写入 {output_file}')
                else:
                    logger.warning('没有数据可写入CSV文件')

        except Exception as e:
            logger.error(f'导出省市清单失败: {str(e)}')


if __name__ == "__main__":
    crawler = XiechengCrawler('https://you.ctrip.com/')
    crawler.crawl(province_name='甘肃')