from minio import Minio
from minio.error import S3Error
import logging

logger = logging.getLogger(__name__)


# Minio文件上传工具类
class MinioFileUploader:
    def __init__(self, endpoint, access_key, secret_key, secure=False):
        self.minio_client = Minio(
            endpoint,
            access_key=access_key,
            secret_key=secret_key,
            secure=secure
        )

    def upload_file(self, bucket_name, object_name, file_path):
        try:
            # 确保桶存在
            try:
                if not self.minio_client.bucket_exists(bucket_name):
                    self.minio_client.make_bucket(bucket_name)
                    logger.info(f"Successfully created bucket: {bucket_name}")
            except Exception as e:
                logger.error(f"检查/创建 bucket 失败: {str(e)}")
                return None

            # 上传文件
            try:
                self.minio_client.fput_object(bucket_name, object_name, file_path)
                logger.info(f"Successfully uploaded {object_name}")
                return object_name
            except S3Error as e:
                logger.error(f"上传文件失败: {str(e)}")
                if "AccessDenied" in str(e):
                    logger.error("权限被拒绝，请检查 MinIO 配置和权限设置")
                return None

        except Exception as e:
            logger.error(f"上传过程中发生错误: {str(e)}")
            return None

    def check_object_exists(self, bucket_name: str, object_name: str) -> bool:
        """检查对象是否存在于 MinIO 中

        Args:
            bucket_name: 桶名称
            object_name: 对象名称

        Returns:
            bool: 对象是否存在
        """
        try:
            self.minio_client.stat_object(bucket_name, object_name)
            return True
        except Exception as e:
            if "NoSuchKey" in str(e) or "Not Found" in str(e):
                return False
            logger.error(f"检查对象是否存在时出错: {str(e)}")
            return False


# 示例使用
if __name__ == "__main__":
    uploader = MinioFileUploader(
        endpoint="192.168.34.46:9000",
        access_key="minioadmin",
        secret_key="minioadmin",
        secure=False
    )

    bucket_name = "wenlv"
    object_name = "museum/collection_img"  # 例如 "images/example.jpg"
    file_path = "F:/MongoDB/museum/商青铜虎形饰.jpg"

    share_url = uploader.upload_file(bucket_name, object_name, file_path)