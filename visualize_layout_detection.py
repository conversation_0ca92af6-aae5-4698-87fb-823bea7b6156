#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布局识别可视化脚本
生成图片显示识别的布局情况
"""

import os
import sys
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle
import json

def visualize_layout_detection(dxf_file):
    """可视化布局检测结果"""
    print("🎨 生成布局识别可视化")
    print("=" * 40)
    
    try:
        # 导入解析器
        sys.path.append('data_process/cad')
        from dxf_parser_structured_v4 import DXFStructuredParserV4
        
        # 创建解析器并进行布局分析
        parser = DXFStructuredParserV4(dxf_file)
        print("🔄 开始布局分析...")
        result = parser.analyze_layout_with_ocr()
        
        # 获取布局分析结果
        layout_info = result.get('版面分析', {})
        pages = layout_info.get('pages', [])
        
        if not pages:
            print("❌ 没有检测到页面布局")
            return None
        
        print(f"✅ 检测到 {len(pages)} 个页面")
        
        # 获取PDF路径用于转换图像
        pdf_path = parser.layout_analyzer.pdf_path
        if not pdf_path or not os.path.exists(pdf_path):
            print("❌ PDF文件不存在，无法生成可视化")
            return None
        
        # 转换PDF到图像
        print("🔄 转换PDF到图像...")
        images = parser.layout_analyzer.pdf_to_image(pdf_path)
        if not images:
            print("❌ PDF转图像失败")
            return None
        
        # 为每个页面生成可视化
        visualization_files = []
        for page_idx, (image, page_layout) in enumerate(zip(images, pages)):
            print(f"🎨 生成第{page_idx + 1}页可视化...")
            
            vis_file = create_page_visualization(image, page_layout, page_idx + 1)
            if vis_file:
                visualization_files.append(vis_file)
        
        # 生成总结图
        summary_file = create_summary_visualization(layout_info, len(images))
        if summary_file:
            visualization_files.append(summary_file)
        
        return visualization_files
        
    except Exception as e:
        print(f"❌ 布局可视化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_page_visualization(image, page_layout, page_num):
    """为单个页面创建可视化"""
    try:
        # 创建图形
        fig, ax = plt.subplots(figsize=(16, 12))
        
        # 显示原始图像
        ax.imshow(image)
        ax.set_title(f'第{page_num}页 - 布局识别结果', fontsize=16, fontweight='bold')
        
        # 获取页面信息
        main_drawing = page_layout.get('main_drawing')
        tables = page_layout.get('tables', [])
        legends = page_layout.get('legends', [])
        
        # 绘制主图区域
        if main_drawing and main_drawing.get('bbox'):
            bbox = main_drawing['bbox']
            rect = Rectangle(
                (bbox['x_min'], bbox['y_min']),
                bbox['x_max'] - bbox['x_min'],
                bbox['y_max'] - bbox['y_min'],
                linewidth=3, edgecolor='red', facecolor='none',
                linestyle='-'
            )
            ax.add_patch(rect)
            ax.text(bbox['x_min'], bbox['y_min'] - 20, '主图区域', 
                   fontsize=12, color='red', fontweight='bold')
        
        # 绘制表格区域
        for i, table in enumerate(tables):
            if table.get('bbox'):
                bbox = table['bbox']
                rect = Rectangle(
                    (bbox['x_min'], bbox['y_min']),
                    bbox['x_max'] - bbox['x_min'],
                    bbox['y_max'] - bbox['y_min'],
                    linewidth=2, edgecolor='blue', facecolor='none',
                    linestyle='--'
                )
                ax.add_patch(rect)
                ax.text(bbox['x_min'], bbox['y_min'] - 20, f'表格{i+1}', 
                       fontsize=10, color='blue', fontweight='bold')
        
        # 绘制图例区域
        for i, legend in enumerate(legends):
            if legend.get('bbox'):
                bbox = legend['bbox']
                rect = Rectangle(
                    (bbox['x_min'], bbox['y_min']),
                    bbox['x_max'] - bbox['x_min'],
                    bbox['y_max'] - bbox['y_min'],
                    linewidth=2, edgecolor='green', facecolor='none',
                    linestyle=':'
                )
                ax.add_patch(rect)
                ax.text(bbox['x_min'], bbox['y_min'] - 20, f'图例{i+1}', 
                       fontsize=10, color='green', fontweight='bold')
        
        # 添加图例说明
        legend_elements = [
            plt.Line2D([0], [0], color='red', lw=3, label='主图区域'),
            plt.Line2D([0], [0], color='blue', lw=2, linestyle='--', label='表格区域'),
            plt.Line2D([0], [0], color='green', lw=2, linestyle=':', label='图例区域')
        ]
        ax.legend(handles=legend_elements, loc='upper right', fontsize=10)
        
        # 设置坐标轴
        ax.set_xlim(0, image.shape[1])
        ax.set_ylim(image.shape[0], 0)  # 翻转Y轴
        
        # 保存图像
        output_file = f'layout_visualization_page_{page_num}.png'
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 第{page_num}页可视化已保存: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ 第{page_num}页可视化失败: {e}")
        return None

def create_summary_visualization(layout_info, total_pages):
    """创建布局识别总结可视化"""
    try:
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 统计信息
        pages = layout_info.get('pages', [])
        total_main_drawings = sum(1 for p in pages if p.get('main_drawing'))
        total_tables = sum(len(p.get('tables', [])) for p in pages)
        total_legends = sum(len(p.get('legends', [])) for p in pages)
        
        # 创建统计图表
        categories = ['页面数', '主图', '表格', '图例']
        values = [total_pages, total_main_drawings, total_tables, total_legends]
        colors = ['skyblue', 'red', 'blue', 'green']
        
        bars = ax.bar(categories, values, color=colors, alpha=0.7)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                   f'{value}', ha='center', va='bottom', fontsize=12, fontweight='bold')
        
        ax.set_title('布局识别统计结果', fontsize=16, fontweight='bold')
        ax.set_ylabel('数量', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 添加详细信息文本
        info_text = f"""
布局识别详情:
• 总页面数: {total_pages}
• 主图区域: {total_main_drawings}
• 表格区域: {total_tables}
• 图例区域: {total_legends}
• 分析方法: {layout_info.get('analysis_method', '未知')}
        """
        
        ax.text(0.02, 0.98, info_text.strip(), transform=ax.transAxes,
               fontsize=10, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # 保存图像
        output_file = 'layout_summary.png'
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 布局总结已保存: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ 布局总结可视化失败: {e}")
        return None

def main():
    """主函数"""
    dxf_file = "test_drawing.dxf"
    
    if not os.path.exists(dxf_file):
        print(f"❌ DXF文件不存在: {dxf_file}")
        return
    
    print("🚀 开始布局识别可视化")
    
    # 生成可视化
    vis_files = visualize_layout_detection(dxf_file)
    
    if vis_files:
        print(f"\n🎉 可视化完成！生成了 {len(vis_files)} 个文件:")
        for file in vis_files:
            if os.path.exists(file):
                size = os.path.getsize(file) / 1024
                print(f"   📁 {file} ({size:.1f} KB)")
        
        print(f"\n💡 在macOS上查看图片:")
        for file in vis_files:
            print(f"   open {file}")
    else:
        print("❌ 可视化失败")

if __name__ == "__main__":
    main()
