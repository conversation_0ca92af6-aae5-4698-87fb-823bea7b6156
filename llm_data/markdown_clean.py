import os
import json
import argparse

import os
import json
import re
import argparse


def process_markdown_content(content: str) -> str:
    """
    Processes markdown content using regular expressions for robustness and efficiency.
    - Formats H1 title.
    - Finds and refactors the '周边相关' JSON block.
    - Removes blank lines.

    Args:
        content: The string content of the markdown file.

    Returns:
        The processed markdown string.
    """

    # 1. 格式化主标题 (e.g., "# Attraction" -> "# 景点名: Attraction")
    # 使用 re.MULTILINE 确保 ^ 匹配每一行的开始
    content = re.sub(
        r'^(#\s)(?!#)(.*)',
        r'# 景点名: \2',
        content,
        flags=re.MULTILINE
    )

    # 2. 定义一个回调函数来处理 "周边相关" 的JSON区块
    def process_related_section(match):
        # match.group(0) 是整个匹配到的字符串
        # match.group(1) 是 "## 周边相关\n"
        # match.group(2) 是 " [...] " 这个JSON字符串
        json_block = match.group(2)

        # 2.1 使用正则表达式移除 "href" 键值对。
        # 这个表达式会查找 "href" 键值对，以及它前面或后面的逗号，并一起移除，以确保JSON格式正确。
        # 模式解释:
        #   ,\s* # 匹配前面的逗号和空格
        #   "href"    # 匹配 "href"
        #   \s*:\s* # 匹配冒号和周围的空格
        #   "[^"]*"  # 匹配整个URL字符串
        #   |         # OR
        #   "href"...\s*,? # 匹配在开头的 "href" 和它后面的逗号
        pattern_href = r',\s*"href"\s*:\s*"[^"]*"|(?<=\{)\s*"href"\s*:\s*"[^"]*"\s*,?'
        processed_block = re.sub(pattern_href, '', json_block)

        # 2.2 使用简单的正则替换关键字
        processed_block = re.sub(r'"recommend"', '"景点"', processed_block)
        processed_block = re.sub(r'"distance"', '"距离"', processed_block)

        # 返回处理过的区块，保持标题部分不变
        return match.group(1) + processed_block

    # 3. 使用re.sub和回调函数来处理整个"周边相关"区块
    # 模式解释:
    #   (## 周边相关\s*\n)  # 捕获组1: 匹配标题行
    #   (\[.*?\])          # 捕获组2: 懒惰匹配从 [ 到 ] 的所有内容
    #   flags=re.DOTALL   # 确保 . 可以匹配换行符，处理多行JSON
    content = re.sub(
        r'(## 周边相关\s*\n)(\[.*?\])',
        process_related_section,
        content,
        flags=re.DOTALL
    )

    # 4. 移除所有空白行
    # 先用正则将多个连续换行符合并为一个，然后按行清理
    content = re.sub(r'(\n\s*){2,}', '\n', content).strip()
    lines = content.splitlines()
    non_blank_lines = [line for line in lines if line.strip()]

    return "\n".join(non_blank_lines)


def process_directory(input_dir: str, output_dir: str):
    """
    Processes all markdown files in the input directory and saves them to the
    output directory. (This function remains unchanged)
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created output directory: {output_dir}")

    for filename in os.listdir(input_dir):
        if filename.endswith(".md"):
            input_file_path = os.path.join(input_dir, filename)
            output_file_path = os.path.join(output_dir, filename)

            try:
                with open(input_file_path, 'r', encoding='utf-8') as f_in:
                    original_content = f_in.read()

                processed_content = process_markdown_content(original_content)

                with open(output_file_path, 'w', encoding='utf-8') as f_out:
                    f_out.write(processed_content)

                print(f"Successfully processed '{filename}' -> '{output_file_path}'")

            except Exception as e:
                print(f"Error processing file {filename}: {e}")
if __name__ == "__main__":
    # Set up argument parser for command-line usage
    # parser = argparse.ArgumentParser(
    #     description="Clean and refine markdown files for AI analysis.",
    #     formatter_class=argparse.RawTextHelpFormatter
    # )
    # parser.add_argument(
    #     'input_dir',
    #     type=str,
    #     help="The directory containing the markdown files to process."
    # )
    # parser.add_argument(
    #     'output_dir',
    #     type=str,
    #     help="The directory where the cleaned files will be saved."
    # )
    #
    # args = parser.parse_args()

    # Run the processing function with the provided directories
    input_dir='/Users/<USER>/PycharmProjects/crawl/llm_data/markdown_export'
    output_dir='/Users/<USER>/PycharmProjects/crawl/llm_data/markdown_export_clean'
    process_directory(input_dir, output_dir)