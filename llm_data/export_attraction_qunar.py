import pymysql
import os
import re

# --- 数据库连接配置 ---
# TODO: 请根据你的实际情况修改以下数据库连接信息
DB_CONFIG = {
    'host': '**************',   # 数据库主机地址
    'user': 'root', # 数据库用户名
    'password': '123456', # 数据库密码
    'database': 'wenlv', # 数据库名称
    'charset': 'utf8mb4' # 字符集，确保支持中文
}

TABLE_NAME = 'ods_trip_origin_china_attraction_qunar'
OUTPUT_DIR = 'markdown_export' # 生成的 Markdown 文件存放目录
ROWS_PER_FILE = 5 # 每个文件包含的行数

# 字段名到中文 comment 的映射
# 这是根据你提供的 DDL 手动创建的映射
# 'attraction_name' 将用在 Level 1 标题，不作为 Level 2 标题
# '_id' 没有 comment，使用字段名
COLUMN_MAPPING = {
    # '_id': '_id',
    # 'attraction_link': '景点链接',
    'attraction_name': '景点名称', # 仅用于一级标题
    'city': '城市',
    'overview': '简介',
    'province': '省份',
    'recommend_around': '周边相关',
    'scorebox': '评分',
    'summary': '信息汇总',
    'tickets': '票价',
    'tips': '小贴士',
    'transport_guide': '景点交通',
    # 'transport_image': '景点图片'
}

# 定义哪些字段需要作为二级标题导出 (排除 attraction_name)
COLUMNS_FOR_LEVEL2 = {col: comment for col, comment in COLUMN_MAPPING.items() if col != 'attraction_name'}


# --- 数据清洗函数 ---
def clean_text(text):
    """
    清洗文本数据，去除换行符、制表符等，避免 Markdown 格式混乱。
    """
    if text is None:
        return ""
    # 将数据转换为字符串
    text = str(text)
    # 替换各种换行符和制表符为空格
    text = text.replace('\r\n', ' ').replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
    # 移除可能影响 Markdown 格式的特殊字符，例如反引号 ``
    text = text.replace('`', '')
    # 移除多余的空格，并去除首尾空格
    text = re.sub(r'\s+', ' ', text).strip()
    return text

# --- 主要导出逻辑 ---
def export_data_to_markdown():
    """
    从数据库导出数据并生成 Markdown 文件。
    """
    # 创建输出目录
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(f"已创建输出目录: {OUTPUT_DIR}")

    conn = None
    cursor = None
    try:
        # 连接数据库
        print("正在连接数据库...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor) # 使用 DictCursor 可以按字段名访问数据

        # 查询数据
        # sql = f"-- SELECT * FROM {TABLE_NAME}"

        sql = f"""
                  SELECT
                        _id,
                        attraction_link,
                        attraction_name,
                        city,
                        overview,
                        province,
                        recommend_around,
                        scorebox,
                        summary,
                        tickets,
                        tips,
                        transport_guide,
                        transport_image
                    FROM (
                        SELECT
                            *,
                            ROW_NUMBER() OVER(PARTITION BY attraction_name ORDER BY _id ASC) as rn -- 按attraction_name 分组，组内按 _id 升序排序，分配行号
                        FROM ods_trip_origin_china_attraction_qunar
                    ) AS ranked_data
                    WHERE rn = 1;
               """
        print(f"正在执行查询: {sql}")
        cursor.execute(sql)
        rows = cursor.fetchall()
        print(f"共查询到 {len(rows)} 条数据。")

        if not rows:
            print("数据库表中没有数据。")
            return

        current_group_rows = [] # 存储当前文件组的行数据
        file_index = 1 # 文件序号

        for row_index, row_data in enumerate(rows):
            current_group_rows.append(row_data)

            # 如果当前组满员或已经是最后一行数据，则生成文件
            if len(current_group_rows) == ROWS_PER_FILE or row_index == len(rows) - 1:
                markdown_content_lines = [] # 存储当前文件的所有 Markdown 行

                print(f"正在处理第 {file_index} 组数据 (包含 {len(current_group_rows)} 条记录)...")

                # 为当前组中的每一行生成 Markdown 内容
                for row_in_group_index, row_data_in_group in enumerate(current_group_rows):
                    # 获取景点名称用于一级标题，处理 None 或空字符串的情况
                    attraction_name = clean_text(row_data_in_group.get('attraction_name') or '未知景点')
                    if not attraction_name: # 如果清洗后依然是空
                         attraction_name = '未知景点'


                    # 添加一级标题: 景点名 + _ + 行序号 (在文件组中的序号，0-4)
                    markdown_content_lines.append(f"# {attraction_name}_{row_in_group_index}\n")
                    markdown_content_lines.append("\n") # 添加空行增强可读性

                    # 添加二级标题和对应字段的数据
                    for col_name, comment in COLUMNS_FOR_LEVEL2.items():
                        field_value = row_data_in_group.get(col_name)
                        cleaned_value = clean_text(field_value)

                        # 添加二级标题 (使用中文 comment)
                        markdown_content_lines.append(f"## {comment}\n")
                        # 添加清洗后的数据
                        markdown_content_lines.append(f"{cleaned_value}\n")
                        markdown_content_lines.append("\n") # 添加空行

                    # 如果不是当前文件组的最后一行，添加分隔线
                    if row_in_group_index < len(current_group_rows) - 1:
                         markdown_content_lines.append("---\n") # 添加水平分隔线
                         markdown_content_lines.append("\n") # 分隔线后添加空行

                # 生成文件名 (使用文件序号，不足三位前补零)
                filename = os.path.join(OUTPUT_DIR, f'data_group_{file_index:03d}.md')

                # 将当前组的 Markdown 内容写入文件
                with open(filename, 'w', encoding='utf-8') as f:
                    f.writelines(markdown_content_lines)

                print(f"已生成文件: {filename}")

                # 重置，准备处理下一组数据
                current_group_rows = []
                file_index += 1

        print("\n数据导出完成！")

    except pymysql.Error as e:
        print(f"数据库错误: {e}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        # 关闭游标和连接
        if cursor:
            cursor.close()
        if conn:
            conn.close()
        print("数据库连接已关闭。")

# --- 脚本入口 ---
if __name__ == "__main__":
    export_data_to_markdown()
