# ES数据查询工具

这是一个简单的Elasticsearch数据查询工具，专为查询MinIO的S3事件日志设计。

## 功能特点

- 从ES查询MinIO日志数据
- 支持时间范围过滤
- 解析并提取关键字段
- 保存结果到JSON文件
- 灵活的命令行参数配置

## 使用方法

### 基本用法

```bash
python es_fetch.py
```

这将查询过去7天内的最新10条记录。

### 可选参数

- `--days N`: 查询过去N天的数据（默认30天）
- `--size N`: 返回N条记录（默认50条）
- `--raw`: 显示原始数据（默认只显示处理后的关键字段）
- `--index NAME`: 指定要查询的索引名称（默认使用脚本中配置的索引）
- `--save FILE`: 将结果保存到指定JSON文件（默认为minio_logs.json）

### 示例

查询过去60天的100条记录，并保存到自定义文件：
```bash
python es_fetch.py --days 60 --size 100 --save custom_logs.json
```

查询指定索引的原始数据：
```bash
python es_fetch.py --index minio3_events-2023 --raw
```

## 配置说明

脚本顶部的配置参数说明：

```python
# ES配置信息
ES_HOSTS = ["http://*************:9200"]  # ES服务器地址
ES_INDEX_PATTERN = "minio3_events-*"       # 索引模式
ES_USER = "elastic"                        # ES用户名
ES_PASSWORD = "scyd@lab1234"               # ES密码
```

修改这些值以适配您的ES环境。

## 排错指南

1. 连接失败：
   - 检查ES_HOSTS是否正确
   - 确认ES_USER和ES_PASSWORD认证信息正确
   - 确认ES服务器可访问

2. 查询返回为空：
   - 检查ES_INDEX_PATTERN是否匹配实际索引名
   - 尝试调整时间范围参数(--days)
   - 使用--raw参数检查原始数据结构

3. 解析错误：
   - 数据结构可能与期望不同，检查原始数据格式 