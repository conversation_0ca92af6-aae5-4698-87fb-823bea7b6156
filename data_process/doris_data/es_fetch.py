#!/usr/bin/env python
# -*- coding: utf-8 -*-

import datetime
import json
import argparse
import os
import requests
import urllib3
import base64
import traceback
from elasticsearch import Elasticsearch, helpers

# ES配置信息
ES_HOSTS = ["http://*************:9200"]
ES_INDEX_PATTERN = "minio3_events-*"
ES_USER = "elastic"
ES_PASSWORD = "scyd@lab1234"

def check_es_connection(hosts, username=None, password=None):
    """检查ES连接并尝试多种方式连接"""
    urllib3.disable_warnings()  # 禁用SSL警告
    
    print(f"检查ES服务器连接情况...")
    
    # 尝试不同的URL格式
    url_variants = []
    
    # 添加原始URL
    url_variants.append(hosts[0])
    
    # 如果URL不是以/结尾，添加一个以/结尾的变体
    if not hosts[0].endswith('/'):
        url_variants.append(hosts[0] + '/')
    
    # 如果主机被指定为IP，尝试不同的端口
    import re
    ip_pattern = re.compile(r'^\d+\.\d+\.\d+\.\d+$')
    
    base_url = hosts[0].rstrip('/')
    if '://' in base_url:
        protocol, rest = base_url.split('://', 1)
        host_part = rest.split('/', 1)[0]
        
        # 如果没有指定端口，尝试常用ES端口
        if ':' not in host_part and ip_pattern.match(host_part):
            for port in [9200, 9201, 9202, 9300]:
                url_variants.append(f"{protocol}://{host_part}:{port}")
    
    # 准备不同的认证方式
    auth_methods = []
    if username and password:
        # 创建正确的Basic Auth编码
        auth_string = f"{username}:{password}"
        encoded_auth = base64.b64encode(auth_string.encode()).decode()
        
        auth_methods = [
            {"name": "Basic Auth", "auth": (username, password)},
            {"name": "Headers Auth", "headers": {"Authorization": f"Basic {encoded_auth}"}}
        ]
    else:
        auth_methods = [{"name": "No Auth", "auth": None}]
    
    # 准备请求头
    headers = {"Accept": "application/json"}
    
    # 尝试每种URL和认证方式的组合
    print(f"将尝试以下URL变体:")
    for i, url in enumerate(url_variants):
        print(f"  {i+1}. {url}")
    
    for url in url_variants:
        for method in auth_methods:
            try:
                print(f"\n尝试连接 {url} 使用 {method['name']} 方式...")
                
                if "auth" in method:
                    response = requests.get(
                        url, 
                        headers=headers, 
                        auth=method["auth"], 
                        verify=False, 
                        timeout=10
                    )
                elif "headers" in method:
                    merged_headers = {**headers, **method["headers"]}
                    response = requests.get(
                        url, 
                        headers=merged_headers, 
                        verify=False, 
                        timeout=10
                    )
                    
                if response.status_code == 200:
                    print(f"连接成功! 状态码: {response.status_code}")
                    es_info = response.json()
                    version = es_info.get("version", {}).get("number", "未知")
                    print(f"ES版本: {version}")
                    print(f"ES名称: {es_info.get('name', '未知')}")
                    print(f"集群名: {es_info.get('cluster_name', '未知')}")
                    
                    # 如果成功找到可用的连接，更新全局ES_HOSTS
                    global ES_HOSTS
                    ES_HOSTS = [url]
                    print(f"已更新ES_HOSTS为: {ES_HOSTS}")
                    
                    return True, method["name"]
                else:
                    print(f"连接失败! 状态码: {response.status_code}")
                    print(f"响应内容: {response.text}")
            except Exception as e:
                print(f"请求异常: {e}")
    
    return False, None

def get_es_client():
    """创建并返回Elasticsearch客户端连接"""
    print(f"尝试连接到ES服务器: {ES_HOSTS}")
    
    # 先检查连接情况
    success, auth_method = check_es_connection(ES_HOSTS, ES_USER, ES_PASSWORD)
    if success:
        print(f"基本HTTP请求成功，使用 {auth_method} 方式。")
        # 连接成功，优先使用自定义客户端实现，因为原生客户端有版本兼容性问题
        print("使用自定义HTTP客户端实现以避免版本兼容性问题...")
        return create_custom_es_client(ES_HOSTS[0], ES_USER, ES_PASSWORD)
    else:
        print("基本HTTP请求失败，仍将尝试Elasticsearch客户端连接...")
    
    try:
        # 添加更多连接选项，处理可能的SSL/TLS和证书问题
        es_client = Elasticsearch(
            ES_HOSTS,
            basic_auth=(ES_USER, ES_PASSWORD),
            request_timeout=30,
            max_retries=3,
            retry_on_timeout=True,
            verify_certs=False,  # 如果ES使用自签名证书，不验证证书
            ssl_show_warn=False,  # 禁止SSL警告
            api_version="8"  # 明确指定API版本8，解决服务端版本兼容性问题
        )
        
        # 检查连接是否成功
        print("尝试ES客户端ping测试...")
        if es_client.ping():
            info = es_client.info()
            print(f"ES连接成功! 服务器版本: {info['version']['number']}")
            return es_client
        else:
            print("ES ping测试失败，尝试其他验证方式...")
            
            # 尝试info API，有时ping失败但其他API可用
            try:
                info = es_client.info()
                print(f"ES info API成功! 服务器版本: {info['version']['number']}")
                return es_client
            except Exception as info_err:
                print(f"ES info API也失败: {info_err}")
            
            # 如果基本HTTP连接成功但ES客户端API失败，返回自定义客户端
            if success:
                print("使用自定义HTTP客户端实现...")
                return create_custom_es_client(ES_HOSTS[0], ES_USER, ES_PASSWORD)
                
            # 如果所有尝试都失败，返回None
            return None
    except Exception as e:
        print(f"ES连接失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        
        # 尝试自定义客户端作为后备方案
        if success:
            print("尝试使用自定义HTTP客户端作为后备方案...")
            return create_custom_es_client(ES_HOSTS[0], ES_USER, ES_PASSWORD)
        
        return None

def create_custom_es_client(host, username, password):
    """创建自定义ES客户端，通过HTTP请求实现"""
    class CustomESClient:
        def __init__(self, host, username, password):
            self.host = host
            self.auth = (username, password) if username and password else None
            self.headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"  # 使用通用JSON格式，避免版本兼容性问题
            }
            print(f"初始化自定义ES客户端，连接到 {host}")
            # 测试连接
            try:
                response = requests.get(
                    self.host, 
                    headers=self.headers, 
                    auth=self.auth, 
                    verify=False,
                    timeout=10
                )
                if response.status_code == 200:
                    es_info = response.json()
                    print(f"自定义客户端连接成功! ES版本: {es_info.get('version', {}).get('number', '未知')}")
            except Exception as e:
                print(f"自定义客户端初始化警告: {e}")
        
        def search(self, index, query, size=10, sort=None):
            """实现search方法"""
            try:
                url = f"{self.host}/{index}/_search"
                payload = {
                    "query": query,
                    "size": size
                }
                if sort:
                    payload["sort"] = sort
                
                print(f"执行自定义查询: {url}")
                print(f"查询内容: {json.dumps(payload)}")
                
                response = requests.post(
                    url, 
                    headers=self.headers,
                    auth=self.auth,
                    json=payload,
                    verify=False,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"查询成功，共找到 {result.get('hits', {}).get('total', {}).get('value', 0)} 条记录")
                    return result
                else:
                    print(f"查询失败！状态码：{response.status_code}")
                    print(f"错误信息：{response.text}")
                    return {"hits": {"hits": [], "total": {"value": 0}}}
            except Exception as e:
                print(f"自定义查询出错: {e}")
                print(traceback.format_exc())
                return {"hits": {"hits": [], "total": {"value": 0}}}
        
        def ping(self):
            """实现ping方法"""
            try:
                response = requests.get(
                    self.host, 
                    headers=self.headers, 
                    auth=self.auth, 
                    verify=False,
                    timeout=10
                )
                return response.status_code == 200
            except Exception:
                return False
        
        def info(self):
            """实现info方法"""
            try:
                response = requests.get(
                    self.host, 
                    headers=self.headers, 
                    auth=self.auth, 
                    verify=False,
                    timeout=10
                )
                if response.status_code == 200:
                    return response.json()
                return {"version": {"number": "未知"}}
            except Exception:
                return {"version": {"number": "未知"}}
    
    print("创建自定义ES客户端...")
    return CustomESClient(host, username, password)

def get_time_range(days_ago=1):
    """获取指定天数前到现在的UTC时间范围"""
    now = datetime.datetime.now(datetime.timezone.utc)
    start_time = now - datetime.timedelta(days=120)
    return start_time.strftime('%Y-%m-%dT%H:%M:%S.%fZ'), now.strftime('%Y-%m-%dT%H:%M:%S.%fZ')

def search_es_data(es_client, start_time=None, end_time=None, size=10, index=None, query_type="time_range"):
    """查询ES数据
    
    参数:
        es_client: ES客户端
        start_time: 开始时间
        end_time: 结束时间
        size: 返回记录数量
        index: 要查询的索引，默认使用ES_INDEX_PATTERN
        query_type: 
            - "time_range": 按时间范围查询
            - "match_all": 匹配所有文档
            - "bucket": 按bucket名称查询
    """
    if not index:
        index = ES_INDEX_PATTERN
        
    # 默认查询
    final_query = {"match_all": {}}
    
    # 根据查询类型构建查询
    if query_type == "time_range" and start_time and end_time:
        # 查询时间字段可能是@timestamp或Records.eventTime，尝试两者
        final_query = {
            "bool": {
                "should": [
                    {
                        "range": {
                            "@timestamp": {
                                "gte": start_time,
                                "lt": end_time
                            }
                        }
                    },
                    {
                        "range": {
                            "Records.eventTime": {
                                "gte": start_time,
                                "lt": end_time
                            }
                        }
                    }
                ],
                "minimum_should_match": 1
            }
        }
    
    # 排序方式
    sort_config = [{"@timestamp": {"order": "desc"}}]
    
    try:
        print(f"查询ES索引: {index}")
        print(f"查询条件: {json.dumps(final_query)}")
        
        # 判断是原生客户端还是自定义客户端
        if hasattr(es_client, '__class__') and es_client.__class__.__name__ == 'CustomESClient':
            # 自定义客户端
            response = es_client.search(
                index=index,
                query=final_query,
                size=size,
                sort=sort_config
            )
            
            # 自定义客户端会返回符合标准格式的响应
            hits = response["hits"]["hits"]
            total = response["hits"]["total"]["value"]
            print(f"查询成功，共找到 {total} 条记录，返回了 {len(hits)} 条")
            return hits
        else:
            # 原生Elasticsearch客户端
            response = es_client.search(
                index=index,
                query=final_query,
                size=size,
                sort=sort_config
            )
            
            hits = response["hits"]["hits"]
            total = response["hits"]["total"]["value"]
            print(f"查询成功，共找到 {total} 条记录，返回了 {len(hits)} 条")
            return hits
    except Exception as e:
        print(f"ES查询失败: {e}")
        print(traceback.format_exc())
        return []

def process_record(record):
    """处理并解析单个ES记录"""
    try:
        source = record["_source"]
        
        # 首先尝试从Records字段中获取数据
        records = source.get("Records", [])
        
        # 根据Records字段结构处理数据
        if isinstance(records, list) and records:
            # 列表结构，取第一个元素
            record_data = records[0]
        elif isinstance(records, dict):
            # 字典结构，直接使用
            record_data = records
        else:
            # Records字段不可用，尝试直接使用source
            record_data = source
        
        # 提取关键信息
        processed = {
            "index_id": record["_id"],
            "timestamp": source.get("@timestamp"),
            "event_time": get_nested_value(record_data, "eventTime"),
            "event_name": get_nested_value(record_data, "eventName"),
            "bucket_name": get_nested_value(record_data, "s3.bucket.name"),
            "object_key": get_nested_value(record_data, "s3.object.key"),
            "size": get_nested_value(record_data, "s3.object.size"),
            "source_ip": get_nested_value(record_data, "requestParameters.sourceIPAddress"),
            "raw_data": record_data  # 保存原始数据以便进一步处理
        }
        return processed
    except Exception as e:
        print(f"记录解析失败: {e}")
        print(traceback.format_exc())
        return {"error": str(e), "raw": record.get("_source", {})}

def get_nested_value(data, path, default=None):
    """安全地获取嵌套字典中的值，支持点表示法"""
    if data is None:
        return default
        
    if "." not in path:
        return data.get(path, default)
    
    parts = path.split(".", 1)
    current_key, rest_path = parts[0], parts[1]
    
    next_data = data.get(current_key)
    if next_data is None:
        return default
    
    return get_nested_value(next_data, rest_path, default)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="从Elasticsearch获取MinIO事件数据")
    parser.add_argument("--days", type=int, default=30, help="查询过去几天的数据，默认30天")
    parser.add_argument("--size", type=int, default=50, help="返回的记录数量，默认50条")
    parser.add_argument("--raw", action="store_true", help="显示原始数据")
    parser.add_argument("--index", type=str, help="要查询的索引名称，默认使用配置中的索引")
    parser.add_argument("--save", type=str, default="minio_logs.json", help="保存结果到指定JSON文件，默认为minio_logs.json")
    parser.add_argument("--test-connection", action="store_true", help="只测试ES连接，不执行查询")
    return parser.parse_args()

def main():
    # 解析命令行参数
    args = parse_args()
    
    # 连接ES
    es_client = get_es_client()
    if not es_client:
        print("无法连接到ES，程序退出")
        return
    
    # 如果只是测试连接，直接返回
    if args.test_connection:
        print("ES连接测试完成，程序退出")
        return
    
    # 获取查询时间范围
    start_time, end_time = get_time_range(days_ago=args.days)
    
    # 查询数据
    print(f"查询时间范围: {start_time} 至 {end_time}")
    records = search_es_data(
        es_client, 
        start_time, 
        end_time, 
        size=args.size,
        index=args.index
    )
    
    # 处理结果
    processed_records = []
    
    # 处理并打印结果
    if records:
        print(f"\n===== 查询结果 (共{len(records)}条) =====")
        for i, record in enumerate(records):
            processed = process_record(record)
            processed_records.append(processed)
            
            print(f"\n--- 记录 {i+1} ---")
            if args.raw:
                # 显示完整原始数据
                print(json.dumps(record, ensure_ascii=False, indent=2))
            else:
                # 只显示处理后的关键信息
                print(json.dumps({k: v for k, v in processed.items() if k != 'raw_data'}, 
                                ensure_ascii=False, indent=2))
            
            # 只显示前5条详细信息
            if i >= 4 and len(records) > 5 and not args.save:
                print(f"\n... 还有 {len(records) - 5} 条记录 ...")
                break
    else:
        print("未查询到任何数据")
    
    # 保存结果到文件
    if args.save and processed_records:
        try:
            save_path = args.save
            # 如果不是绝对路径，转换为绝对路径
            if not os.path.isabs(save_path):
                save_path = os.path.abspath(save_path)
                
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(processed_records, f, ensure_ascii=False, indent=2)
            print(f"\n已将{len(processed_records)}条记录保存到文件:")
            print(f"  - 文件路径: {save_path}")
            print(f"  - 文件大小: {os.path.getsize(save_path) / 1024:.2f} KB")
        except Exception as e:
            print(f"保存文件时发生错误: {e}")

if __name__ == "__main__":
    main() 