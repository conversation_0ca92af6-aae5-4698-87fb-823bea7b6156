import datetime
import json
import urllib.parse
import argparse  # 新增导入，用于处理命令行参数
import time  # 添加 time 模块用于生成唯一的label
import os    # 用于文件操作
import subprocess  # 用于执行curl命令
from elasticsearch import Elasticsearch, helpers

# --- 配置部分 ---
# ES 配置
ES_HOSTS = ["http://*************:9200"]  # 替换为您的 ES 地址
ES_INDEX_PATTERN = "minio3_events"  # 替换为您的 MinIO 日志在 ES 中的索引名/模式
ES_USER = "elastic" # 可选
ES_PASSWORD = "scyd@lab1234" # 可选

# Doris 配置
DORIS_FE_HOST = "**************"  # 替换为 Doris FE 主机名或IP
DORIS_FE_HTTP_PORT = 8030  # 替换为 Doris FE HTTP 端口
DORIS_DB = "edu"  # 替换为 Doris 数据库名
DORIS_TABLE = "ods_edu_logs_minio3_events_49_s3_audit_logs"  # 更新为新的 Doris 表名
DORIS_USER = "admin"  # 替换为 Doris 用户名
DORIS_PASSWORD = "mMVk55_Rzud9iLs"  # 替换为 Doris 密码

# 临时文件目录
TMP_DIR = "../tmp_doris_files"

# 注意：使用交互式模式测试ES连接时，Doris连接信息可暂不配置
# 运行完整数据同步前，请确保以上Doris配置已更新为实际值

# --- 辅助函数 (与上一版基本一致) ---
def get_yesterday_utc_range():
    """获取昨天全天的 UTC 时间范围 (ISO格式字符串)"""
    today_utc = datetime.datetime.now(datetime.timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
    yesterday_utc = today_utc - datetime.timedelta(days=90)
    return yesterday_utc.strftime('%Y-%m-%dT%H:%M:%S.%fZ'), today_utc.strftime('%Y-%m-%dT%H:%M:%S.%fZ')


def get_nested_val(data_dict, path, default=None):
    """安全地从嵌套字典中获取值"""
    keys = path.split('.')
    val = data_dict
    try:
        for key in keys:
            val = val[key]
        return val
    except (KeyError, TypeError, IndexError):
        return default


def process_object_key_path(raw_object_key_value):
    """
    对原始 object_key (文件路径)进行 URL 解码和路径拆分。
    基于截图中的目录结构，支持五级目录划分：
    一级目录: exams
    二级目录: images/videos
    三级目录: alert_images/sampled_images/alert_video_clips
    四级目录: frame_rate_开头的内容(如果不是则为空)
    五级目录: 考场信息，如"学业水平考试-20241227-山东-滨州"

    返回一个包含解码后路径、level1-5 和 left_path 的字典。
    """
    path_data = {
        "decoded_object_key": None,
        "level1": None, "level2": None, "level3": None, "level4": None, "level5": None,
        "left_path": None,
        "left_path_json": {},
        "file_name": None
    }

    if raw_object_key_value is None:
        return path_data

    try:
        decoded_key = urllib.parse.unquote(str(raw_object_key_value))
    except Exception as e:
        print(f"警告: URL解码失败 '{raw_object_key_value}': {e}")
        path_data["decoded_object_key"] = str(raw_object_key_value)  # 保留原始值
        return path_data

    path_data["decoded_object_key"] = decoded_key

    if not decoded_key:
        return path_data
    if decoded_key == "/":
        path_data["left_path"] = "/"
        return path_data

    # 去除开头的斜杠，防止分割后有空元素
    if decoded_key.startswith('/'):
        decoded_key = decoded_key[1:]
    
    # 分割路径
    parts = decoded_key.split('/')
    num_parts = len(parts)
    
    # 处理文件名（最后一部分）
    if num_parts > 0 and not decoded_key.endswith('/'):
        path_data["file_name"] = parts[-1]
    else:
        path_data["file_name"] = ""

    # 处理五级目录结构
    if num_parts > 0:
        path_data["level1"] = parts[0] if num_parts > 0 else None  # 一级目录: exams
    
    if num_parts > 1:
        path_data["level2"] = parts[1] if num_parts > 1 else None  # 二级目录: images/videos
    
    if num_parts > 2:
        path_data["level3"] = parts[2] if num_parts > 2 else None  # 三级目录: alert_images等
    
    # 四级目录特殊处理：只接受frame_rate开头的内容，否则为空
    if num_parts > 3:
        if parts[3].startswith("frame_rate"):
            path_data["level4"] = parts[3]
        else:
            path_data["level4"] = None
            # 如果第四级不是frame_rate开头，则它实际上是第五级内容
            if num_parts > 3:
                path_data["level5"] = parts[3]
    
    # 处理第五级目录，它是考场信息
    if num_parts > 4:
        # 如果第四级已经有值（即是frame_rate开头），则第五级直接取parts[4]
        if path_data["level4"] is not None:
            path_data["level5"] = parts[4]
        # 否则第五级已经在上面的逻辑中赋值了
    
    # 处理剩余路径
    start_index = 5  # 默认从第六级开始是剩余路径
    
    # 如果第四级不是frame_rate开头，则从第五级开始计算剩余路径
    if path_data["level4"] is None:
        start_index = 4
    
    # 确保有剩余路径
    if num_parts > start_index:
        path_data["left_path"] = '/'.join(parts[start_index:])
        
        # 将剩余路径转换为JSON格式
        left_path_json = {}
        for i, part in enumerate(parts[start_index:], 1):
            left_path_json[str(i)] = part
        
        path_data["left_path_json"] = left_path_json
    
    return path_data


# --- ES及Doris交互核心逻辑 (与上一版基本一致) ---
def query_es_logs(es_client, start_time_str, end_time_str, query_size=1000):
    """从 ES 查询指定时间范围内的日志。query_size用于scan的批次大小。"""
    query_body = {
        "query": {
            "bool": {
                "filter": [
                    {
                        "range": {
                            "Records.eventTime": {
                                "gte": start_time_str,
                                "lt": end_time_str,
                                "format": "strict_date_optional_time_nanos||strict_date_optional_time||epoch_millis"
                            }
                        }
                    }
                ]
            }
        },
        "size": query_size  # helpers.scan 内部使用的批处理大小
        # "_source": ["Records"] # 可以考虑只拉取必要字段以提高效率
    }

    print(f"查询ES索引 '{ES_INDEX_PATTERN}' 时间范围：{start_time_str} 到 {end_time_str}")

    try:
        for hit in helpers.scan(es_client, query=query_body, index=ES_INDEX_PATTERN, scroll='5m'):
            record_array = get_nested_val(hit['_source'], 'Records')
            if isinstance(record_array, list) and len(record_array) > 0:
                yield record_array[0]
            elif isinstance(record_array, dict):
                yield record_array
            else:
                print(f"警告: ES文档ID {hit['_id']} 中的 'Records' 结构异常: {record_array}")
    except Exception as e:
        print(f"错误: 查询ES时发生错误: {e}")
        return  # 返回空迭代器


def transform_log_for_doris(log_entry):
    """将从 ES 获取的单条日志转换为 Doris 表结构对应的字典"""
    if not log_entry:
        return None

    raw_object_key = get_nested_val(log_entry, "s3.object.key")
    path_info = process_object_key_path(raw_object_key)

    event_name_val = get_nested_val(log_entry, "eventName", "")
    is_deleted_flag = event_name_val.startswith("s3:ObjectRemoved:") or \
                      event_name_val.startswith("s3:ObjectDeleted:")

    event_time_str = get_nested_val(log_entry, "eventTime")
    doris_event_time = None
    if event_time_str:
        try:
            dt_obj = datetime.datetime.fromisoformat(event_time_str.replace('Z', '+00:00'))
            doris_event_time = dt_obj.strftime('%Y-%m-%d %H:%M:%S')
        except ValueError:
            print(f"警告: 无法解析eventTime '{event_time_str}'. 将设为null.")

    content_length_val = get_nested_val(log_entry, "responseElements.content-length")
    try:
        content_length_val = int(content_length_val) if content_length_val is not None else None
    except (ValueError, TypeError):
        content_length_val = None

    object_size_val = get_nested_val(log_entry, "s3.object.size")
    try:
        object_size_val = int(object_size_val) if object_size_val is not None else None
    except (ValueError, TypeError):
        object_size_val = None

    # 使用file_name作为object_short_key
    object_short_key = path_info["file_name"]
    
    # 将left_path_json转换为字符串保存
    left_path_json_str = json.dumps(path_info["left_path_json"], ensure_ascii=False) if path_info["left_path_json"] else None

    # 按照新表结构调整字段顺序和内容
    doris_row = {
        # 联合主键字段
        "object_short_key": object_short_key,
        "event_time": doris_event_time,
        
        # 其他字段按表结构顺序
        "bucket_name": get_nested_val(log_entry, "s3.bucket.name"),
        "object_key": path_info["decoded_object_key"],
        "level1": path_info["level1"],
        "level2": path_info["level2"],
        "level3": path_info["level3"],
        "level4": path_info["level4"],
        "level5": path_info["level5"],
        "left_path": path_info["left_path"],
        "left_path_json": left_path_json_str,
        
        "event_name": event_name_val,
        "is_deleted": is_deleted_flag,
        
        # 自动添加更新时间字段
        "doris_last_updated_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        
        "aws_region": get_nested_val(log_entry, "awsRegion"),
        "event_source": get_nested_val(log_entry, "eventSource"),
        "event_version": get_nested_val(log_entry, "eventVersion"),
        "request_parameters_principal_id": get_nested_val(log_entry, "requestParameters.principalId"),
        "request_parameters_region": get_nested_val(log_entry, "requestParameters.region"),
        "request_parameters_source_ip_address": get_nested_val(log_entry, "requestParameters.sourceIPAddress"),
        "response_elements_content_length": content_length_val,
        "response_elements_x_amz_id_2": get_nested_val(log_entry, "responseElements.x-amz-id-2"),
        "response_elements_x_amz_request_id": get_nested_val(log_entry, "responseElements.x-amz-request-id"),
        "response_elements_x_minio_deployment_id": get_nested_val(log_entry, "responseElements.x-minio-deployment-id"),
        "response_elements_x_minio_origin_endpoint": get_nested_val(log_entry, "responseElements.x-minio-origin-endpoint"),
        "s3_bucket_arn": get_nested_val(log_entry, "s3.bucket.arn"),
        "s3_bucket_owner_identity_principal_id": get_nested_val(log_entry, "s3.bucket.ownerIdentity.principalId"),
        "s3_configuration_id": get_nested_val(log_entry, "s3.configurationId"),
        "s3_object_content_type": get_nested_val(log_entry, "s3.object.contentType"),
        "s3_object_e_tag": get_nested_val(log_entry, "s3.object.eTag"),
        "s3_object_sequencer": get_nested_val(log_entry, "s3.object.sequencer"),
        "s3_object_size": object_size_val,
        "s3_object_user_metadata_content_type": get_nested_val(log_entry, "s3.object.userMetadata.content-type"),
        "s3_object_version_id": get_nested_val(log_entry, "s3.object.versionId"),
        "s3_s3_schema_version": get_nested_val(log_entry, "s3.s3SchemaVersion"),
        "source_host": get_nested_val(log_entry, "source.host"),
        "source_port": get_nested_val(log_entry, "source.port"),
        "source_user_agent": get_nested_val(log_entry, "source.userAgent"),
        "user_identity_principal_id": get_nested_val(log_entry, "userIdentity.principalId")
    }
    return doris_row


class StreamLoadHelper:
    """Stream Load 帮助类，用于构建和执行Doris Stream Load命令"""
    
    def __init__(self, host, port, user, password, db, table):
        """
        初始化Stream Load帮助类
        
        Args:
            host: Doris FE主机名
            port: Doris HTTP端口
            user: Doris用户名
            password: Doris密码
            db: 数据库名
            table: 表名
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.db = db
        self.table = table
        
    def load_data_with_file(self, file_path, conditions=None):
        """
        通过文件方式加载数据到Doris
        
        Args:
            file_path: JSON文件路径
            conditions: Stream Load的条件参数，如format, read_json_by_line等
            
        Returns:
            成功返回True，失败返回False
        """
        if conditions is None:
            conditions = {}
            
        # 生成唯一的label，防止重复任务
        unique_label = f"minio_load_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_{hash(str(time.time()))}"
        
        # 拼接URL
        url = f"http://{self.host}:{self.port}/api/{self.db}/{self.table}/_stream_load"
        
        # 构建curl命令
        curl_cmd = ["curl", "--location-trusted", "-u", f"{self.user}:{self.password}"]
        
        # 添加条件参数
        curl_cmd.append("-H")
        curl_cmd.append(f"label:{unique_label}")
        
        # 添加 Expect: 100-continue 头，这是Doris要求的
        curl_cmd.append("-H")
        curl_cmd.append("Expect:100-continue")
        
        for key, value in conditions.items():
            curl_cmd.append("-H")
            curl_cmd.append(f"{key}:{value}")
            
        curl_cmd.extend(["-T", file_path, url])
        
        # 执行curl命令
        print(f"执行Stream Load命令: {' '.join(curl_cmd).replace(self.password, '******')}")
        result = subprocess.run(curl_cmd, capture_output=True, text=True)
        
        # 检查结果
        if result.returncode != 0:
            print(f"Stream Load执行失败: {result.stderr}")
            return False
            
        try:
            print(f"Stream Load原始响应: {result.stdout}")
            response_json = json.loads(result.stdout)
            print(f"Stream Load响应: {json.dumps(response_json, ensure_ascii=False)}")
            
            # 检查成功状态 - 注意Doris可能使用不同的字段名
            success = False
            if response_json.get("Status") == "Success" or response_json.get("status") == "Success":
                success = True
            elif response_json.get("status") == "success":  # 小写版本
                success = True
                
            if success:
                loaded_rows = response_json.get("NumberLoadedRows", response_json.get("number_loaded_rows", 0))
                txn_id = response_json.get("TxnId", response_json.get("txn_id", "-"))
                print(f"成功加载 {loaded_rows} 行数据到Doris. TxnId: {txn_id}")
                return True
            else:
                status = response_json.get("Status", response_json.get("status", "Unknown"))
                message = response_json.get("Message", response_json.get("msg", "No message"))
                print(f"加载数据到Doris失败. Status: {status}")
                print(f"Message: {message}")
                if "ErrorURL" in response_json or "error_url" in response_json:
                    error_url = response_json.get("ErrorURL", response_json.get("error_url"))
                    print(f"Doris错误详情URL: {error_url}")
                return False
        except json.JSONDecodeError:
            print(f"解析Stream Load响应失败: {result.stdout}")
            return False


def ensure_tmp_dir():
    """确保临时目录存在，不存在则创建"""
    if not os.path.exists(TMP_DIR):
        os.makedirs(TMP_DIR)
    return TMP_DIR


def save_batch_to_file(batch_data, batch_number):
    """
    将一批数据保存为JSON文件
    
    Args:
        batch_data: 要保存的数据列表
        batch_number: 批次编号，用于生成文件名
        
    Returns:
        生成的文件路径
    """
    tmp_dir = ensure_tmp_dir()
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    file_name = f"minio_logs_batch_{batch_number}_{timestamp}.json"
    file_path = os.path.join(tmp_dir, file_name)
    
    # 将数据按行保存为JSON文件
    with open(file_path, 'w', encoding='utf-8') as f:
        for record in batch_data:
            f.write(json.dumps(record, ensure_ascii=False) + '\n')
            
    print(f"已保存批次 {batch_number} 的 {len(batch_data)} 条记录到文件: {file_path}")
    return file_path


def load_data_to_doris_with_files(data_batches):
    """
    使用文件方式加载数据到Doris
    
    Args:
        data_batches: 数据批次的列表，每个批次是一个数据列表
        
    Returns:
        成功加载的记录数
    """
    # 初始化Stream Load帮助类
    stream_loader = StreamLoadHelper(
        host=DORIS_FE_HOST,
        port=DORIS_FE_HTTP_PORT,
        user=DORIS_USER,
        password=DORIS_PASSWORD,
        db=DORIS_DB,
        table=DORIS_TABLE
    )
    
    # 确保临时目录存在
    ensure_tmp_dir()
    
    # 条件参数
    conditions = {
        "format": "json",
        "read_json_by_line": "true",  # 按行读取JSON
        "strip_outer_array": "false"
    }
    
    # 记录成功加载的总记录数
    total_loaded_records = 0
    
    # 保存的文件路径列表，用于清理
    saved_files = []
    
    # 处理每个批次
    for batch_idx, batch in enumerate(data_batches):
        if not batch:
            continue
            
        # 保存批次数据到文件
        file_path = save_batch_to_file(batch, batch_idx + 1)
        saved_files.append(file_path)
        
        # 加载文件到Doris
        success = stream_loader.load_data_with_file(file_path, conditions)
        
        if success:
            total_loaded_records += len(batch)
            # 加载成功后删除文件
            try:
                os.remove(file_path)
                print(f"已删除临时文件: {file_path}")
                saved_files.remove(file_path)
            except Exception as e:
                print(f"删除临时文件 {file_path} 失败: {e}")
        else:
            print(f"批次 {batch_idx + 1} 加载失败，临时文件将保留: {file_path}")
    
    # 返回成功加载的记录数
    return total_loaded_records


# --- 主程序 ---
def main():
    """主执行函数"""
    parser = argparse.ArgumentParser(description="从Elasticsearch同步MinIO日志到Doris，并支持交互式测试。")
    parser.add_argument(
        "--interactive-test",
        action="store_true",  # 当此参数出现时，其值为True
        help="启用交互式测试模式：获取并解析一条数据供检查，确认后再继续完整导入。"
    )
    args = parser.parse_args()
    interactive_test = args.interactive_test
    interactive_test = True
    #
    print(f"脚本启动时间 {datetime.datetime.now()}")

    # 确保临时目录存在
    ensure_tmp_dir()

    # 简化ES客户端初始化
    try:
        print(f"尝试连接到Elasticsearch主机: {ES_HOSTS}...")
        
        # 创建ES客户端
        es_auth_params = {}
        if ES_USER and ES_PASSWORD:
            es_auth_params['basic_auth'] = (ES_USER, ES_PASSWORD)
            
        es_client = Elasticsearch(
            ES_HOSTS,
            retry_on_timeout=True,
            request_timeout=30,
            **es_auth_params
        )
        
        # 检查连接是否成功
        es_info = es_client.info()
        print(f"成功连接到Elasticsearch, 版本: {es_info.get('version', {}).get('number', '未知')}")
    except Exception as e:
        print(f"错误: 连接Elasticsearch失败: {e}")
        return

    # --- 交互式测试模块 ---
    if interactive_test:
        print("\n--- 交互式本地测试模式 ---")
        test_start_time_iso, test_end_time_iso = get_yesterday_utc_range()

        test_log_iterator = query_es_logs(es_client, test_start_time_iso, test_end_time_iso, query_size=10)

        try:
            first_es_log_entry = next(test_log_iterator, None)  # 获取第一条，如果没有则为None

            if first_es_log_entry:
                print("\n--- 从ES获取到的原始日志 (样例) ---")
                print(json.dumps(first_es_log_entry, indent=2, ensure_ascii=False))

                doris_transformed_row = transform_log_for_doris(first_es_log_entry)
                print("\n--- 经脚本转换后的Doris目标行数据 (样例) ---")
                if doris_transformed_row:
                    print(json.dumps(doris_transformed_row, indent=2, ensure_ascii=False))
                    
                    # 打印重点字段展示
                    print("\n--- 重点字段展示 ---")
                    print(f"level1: {doris_transformed_row.get('level1')}")
                    print(f"level2: {doris_transformed_row.get('level2')}")
                    print(f"level3: {doris_transformed_row.get('level3')}")
                    print(f"level4: {doris_transformed_row.get('level4')}")
                    print(f"level5: {doris_transformed_row.get('level5')}")
                    print(f"object_short_key: {doris_transformed_row.get('object_short_key')}")
                    print(f"left_path: {doris_transformed_row.get('left_path')}")
                    
                    # 解析并展示left_path_json
                    try:
                        if doris_transformed_row.get('left_path_json'):
                            left_path_json = json.loads(doris_transformed_row.get('left_path_json'))
                            print(f"left_path_json (解析后): {json.dumps(left_path_json, indent=2, ensure_ascii=False)}")
                        else:
                            print("left_path_json: 无")
                    except Exception as e:
                        print(f"left_path_json 解析错误: {e}")
                    
                else:
                    print("数据转换结果为None (可能由于输入无效).")

                print("-" * 50)
                proceed_input = input("以上转换是否符合预期？是否继续执行完整数据导入？ (y/n): ").strip().lower()
                if proceed_input != 'y':
                    print("用户选择退出。脚本执行结束。")
                    return  # 退出程序
                print("确认通过。将继续执行完整数据导入...\n")
            else:
                print(f"在时间范围 {test_start_time_iso} 到 {test_end_time_iso} 内未找到用于测试的ES日志。")
                return
        except Exception as e:
            print(f"交互式测试过程中发生错误: {e}")
            return
    # --- 交互式测试模块结束 ---

    # --- 完整数据导入流程 ---
    print("开始执行完整数据导入流程...")
    start_time_iso, end_time_iso = get_yesterday_utc_range()

    # 保存所有批次数据
    all_batches = []
    current_batch = []
    batch_commit_size = 5000  # 每 N 条记录提交一次到 Doris
    total_records_processed_from_es = 0

    for es_log_entry in query_es_logs(es_client, start_time_iso, end_time_iso):
        if es_log_entry:
            total_records_processed_from_es += 1
            doris_transformed_row = transform_log_for_doris(es_log_entry)
            if doris_transformed_row:
                current_batch.append(doris_transformed_row)

            if len(current_batch) >= batch_commit_size:
                print(f"已收集第 {len(all_batches) + 1} 批 {len(current_batch)} 条记录...")
                all_batches.append(current_batch)
                current_batch = []  # 清空当前批次

            if total_records_processed_from_es > 0 and total_records_processed_from_es % 1000 == 0:
                print(f"已从ES处理 {total_records_processed_from_es} 条记录...")

    # 处理最后一批数据
    if current_batch:
        print(f"已收集最后一批 {len(current_batch)} 条记录...")
        all_batches.append(current_batch)
    
    # 使用文件方式加载数据到Doris
    if all_batches:
        print(f"共收集了 {len(all_batches)} 个批次，总计 {sum(len(batch) for batch in all_batches)} 条记录")
        loaded_records = load_data_to_doris_with_files(all_batches)
        print(f"成功加载 {loaded_records} 条记录到Doris")
    else:
        print("没有收集到任何数据，无需加载")

    # 尝试清理临时目录中的文件
    try:
        file_count = 0
        for file_name in os.listdir(TMP_DIR):
            file_path = os.path.join(TMP_DIR, file_name)
            if os.path.isfile(file_path) and file_name.startswith("minio_logs_batch_"):
                os.remove(file_path)
                file_count += 1
        if file_count > 0:
            print(f"已清理 {file_count} 个临时文件")
    except Exception as e:
        print(f"清理临时文件时发生错误: {e}")

    print(f"MinIO日志同步流程结束于 {datetime.datetime.now()}")
    print(f"在指定时间段内，总共从ES检索并处理了 {total_records_processed_from_es} 条记录。")


if __name__ == "__main__":
    main()