import os
from minio import <PERSON>o


def download_into_one_folder(file_list):
    for file_path in file_list:
        mc.fget_object(
            bucket_name=bucket_name,
            object_name=file_path.split("/")[-1],
            file_path=f"{file_path}",
        )
        
def download_by_folder(file_list): 
    for remote_path in file_list:
        dir_path = os.path.dirname(remote_path)  # 获取上级目录（到y208）
        file_name = os.path.basename(remote_path)  # 获取文件名（y208_000124.jpg）
        
        # 构建本地保存路径（仅保留到y208目录）
        local_dir = os.path.join("./待分拣", dir_path)
        local_path = os.path.join(local_dir, file_name)
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # 下载文件
        try:
            mc.fget_object(
                bucket_name=bucket_name,
                object_name=remote_path,
                file_path=local_path
            )
            print(f"成功下载: {remote_path} -> {local_path}")
        except Exception as e:
            print(f"下载失败 {remote_path}: {str(e)}")


if __name__ == "__main__":
    # file_path = "临时脚本\无效图片列表.txt"
    file_path = "./文件列表.txt"
    
    
    file_list = []
    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            if line.strip() != 'file_path':
                file_list.append(line.strip())  # 使用strip()移除每行首尾的空白字符


    mc = Minio(
        "*************:9000",
        access_key="trip_admin",
        secret_key="trip_admin_scyd@lab1234",
        region="fr",  # https://github.com/minio/minio-py/issues/778 设置一个区域以免报错access deny
        secure=False,
    )
    bucket_name = "trip-data"
    # download_into_one_folder(file_list)
    download_by_folder(file_list)