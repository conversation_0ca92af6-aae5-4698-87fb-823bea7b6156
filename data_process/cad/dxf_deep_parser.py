import ezdxf
import json
import re
import os
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any, Optional, Union
from tqdm import tqdm


def extract_all_text_from_dxf(dxf_path: str) -> Dict[str, Any]:
    """
    深度提取DXF文件中的所有文本内容，包括块定义、图层信息等
    """
    try:
        doc = ezdxf.readfile(dxf_path)
    except Exception as e:
        return {"错误": f"无法读取DXF文件: {e}"}
    
    result = {
        "文件信息": {
            "文件路径": dxf_path,
            "DXF版本": doc.dxfversion
        },
        "模型空间文本": [],
        "图纸空间文本": [],
        "块定义文本": {},
        "图层信息": {},
        "表格文本": [],
        "所有文本汇总": [],
        "统计信息": defaultdict(int)
    }
    
    def clean_text(text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        # 移除MTEXT格式化代码
        text = re.sub(r'\\[A-Za-z][0-9]*;?', '', text)
        text = re.sub(r'\\[{}]', '', text)
        text = re.sub(r'\\P', '\n', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def extract_entity_text(entity, space_name: str):
        """从实体中提取文本"""
        entity_type = entity.dxftype()
        result["统计信息"][entity_type] += 1
        
        text_content = ""
        entity_info = {
            "实体类型": entity_type,
            "图层": entity.dxf.layer,
            "空间": space_name,
            "文本内容": "",
            "坐标": [],
            "其他属性": {}
        }
        
        try:
            if entity_type == 'TEXT':
                text_content = clean_text(entity.dxf.text)
                entity_info["坐标"] = list(entity.dxf.insert)
                entity_info["其他属性"] = {
                    "高度": entity.dxf.height,
                    "旋转": entity.dxf.rotation
                }
                
            elif entity_type == 'MTEXT':
                text_content = clean_text(entity.plain_text())
                entity_info["坐标"] = list(entity.dxf.insert)
                entity_info["其他属性"] = {
                    "字符高度": entity.dxf.char_height,
                    "旋转": entity.dxf.rotation,
                    "宽度": getattr(entity.dxf, 'width', 0)
                }
                
            elif entity_type == 'ATTRIB':
                text_content = clean_text(entity.dxf.text)
                entity_info["坐标"] = list(entity.dxf.insert)
                entity_info["其他属性"] = {
                    "标签": entity.dxf.tag,
                    "高度": entity.dxf.height
                }
                
            elif entity_type == 'ATTDEF':
                text_content = clean_text(entity.dxf.text)
                entity_info["坐标"] = list(entity.dxf.insert)
                entity_info["其他属性"] = {
                    "标签": entity.dxf.tag,
                    "提示": getattr(entity.dxf, 'prompt', ''),
                    "默认值": getattr(entity.dxf, 'text', '')
                }
                
            elif entity_type == 'INSERT':
                # 处理块插入的属性
                attrib_texts = []
                if hasattr(entity, 'attribs'):
                    for attrib in entity.attribs:
                        if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                            clean_attrib = clean_text(attrib.dxf.text)
                            if clean_attrib:
                                attrib_texts.append(f"{attrib.dxf.tag}: {clean_attrib}")
                
                text_content = "; ".join(attrib_texts)
                entity_info["坐标"] = list(entity.dxf.insert)
                entity_info["其他属性"] = {
                    "块名": entity.dxf.name,
                    "缩放": [entity.dxf.xscale, entity.dxf.yscale, entity.dxf.zscale],
                    "旋转": entity.dxf.rotation,
                    "属性数量": len(attrib_texts)
                }
                
            elif entity_type in ['DIMENSION', 'LEADER']:
                dim_text = getattr(entity.dxf, 'text', '')
                if dim_text:
                    text_content = clean_text(dim_text)
                    
        except Exception as e:
            entity_info["错误"] = f"提取文本时出错: {e}"
        
        entity_info["文本内容"] = text_content
        
        if text_content:
            result["所有文本汇总"].append(text_content)
            
            if space_name == "模型空间":
                result["模型空间文本"].append(entity_info)
            elif space_name == "图纸空间":
                result["图纸空间文本"].append(entity_info)
            else:
                if space_name not in result["块定义文本"]:
                    result["块定义文本"][space_name] = []
                result["块定义文本"][space_name].append(entity_info)
    
    # 1. 提取模型空间的文本
    print("正在提取模型空间文本...")
    msp = doc.modelspace()
    for entity in msp:
        extract_entity_text(entity, "模型空间")
    
    # 2. 提取图纸空间的文本
    print("正在提取图纸空间文本...")
    for layout in doc.layouts:
        if layout.name != 'Model':  # 跳过模型空间
            for entity in layout:
                extract_entity_text(entity, f"图纸空间-{layout.name}")
    
    # 3. 提取块定义中的文本
    print("正在提取块定义文本...")
    for block in doc.blocks:
        block_name = block.name
        if not block_name.startswith('*'):  # 跳过匿名块
            for entity in block:
                extract_entity_text(entity, f"块定义-{block_name}")
    
    # 4. 提取图层信息
    print("正在提取图层信息...")
    for layer in doc.layers:
        layer_name = layer.dxf.name
        result["图层信息"][layer_name] = {
            "颜色": layer.dxf.color,
            "线型": layer.dxf.linetype,
            "是否冻结": layer.is_frozen(),
            "是否锁定": layer.is_locked(),
            "是否关闭": layer.is_off()
        }
    
    # 5. 尝试提取表格实体（如果存在）
    print("正在查找表格实体...")
    try:
        for entity in msp.query('TABLE'):
            result["表格文本"].append({
                "表格信息": "找到TABLE实体",
                "图层": entity.dxf.layer
            })
    except:
        pass
    
    # # 6. 搜索特定关键词
    # search_keywords = ["消防设备应急电源", "消防", "应急", "电源", "设备"]
    # keyword_results = {}
    #
    # for keyword in search_keywords:
    #     matches = []
    #     for text in result["所有文本汇总"]:
    #         if keyword in text:
    #             matches.append(text)
    #     keyword_results[keyword] = matches
    #
    # result["关键词搜索"] = keyword_results
    
    return result


def save_deep_analysis(dxf_path: str, output_path: str, show_progress: bool = True):
    """
    执行深度分析并保存结果

    Args:
        dxf_path: DXF文件路径
        output_path: 输出JSON文件路径
        show_progress: 是否显示进度信息
    """
    if show_progress:
        print(f"开始深度分析DXF文件: {dxf_path}")

    analysis_result = extract_all_text_from_dxf(dxf_path)

    # 检查是否有错误
    if "错误" in analysis_result:
        print(f"分析失败: {analysis_result['错误']}")
        return False

    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)

    # 保存完整结果
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False

    if show_progress:
        # 打印统计信息
        print(f"\n=== 深度分析完成 ===")
        print(f"实体统计:")
        for entity_type, count in sorted(analysis_result["统计信息"].items()):
            print(f"  {entity_type}: {count}")

        print(f"\n文本统计:")
        print(f"  模型空间文本: {len(analysis_result['模型空间文本'])}")
        print(f"  图纸空间文本: {len(analysis_result['图纸空间文本'])}")
        print(f"  块定义文本: {sum(len(texts) for texts in analysis_result['块定义文本'].values())}")
        print(f"  总文本数量: {len(analysis_result['所有文本汇总'])}")
        print(f"  图层数量: {len(analysis_result['图层信息'])}")

        print(f"\n关键词搜索结果:")
        for keyword, matches in analysis_result["关键词搜索"].items():
            print(f"  '{keyword}': {len(matches)} 条匹配")
            if matches:
                for i, match in enumerate(matches[:3]):  # 显示前3条
                    print(f"    {i+1}. {match}")
                if len(matches) > 3:
                    print(f"    ... 还有 {len(matches) - 3} 条")

        print(f"\n结果已保存到: {output_path}")

        # 创建简化的文本摘要文件
        summary_path = output_path.replace('.json', '_summary.txt')
        try:
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write("=== DXF文件文本内容摘要 ===\n\n")
                f.write(f"文件: {dxf_path}\n")
                f.write(f"总文本数量: {len(analysis_result['所有文本汇总'])}\n\n")

                f.write("=== 所有文本内容 ===\n")
                for i, text in enumerate(analysis_result['所有文本汇总'], 1):
                    f.write(f"{i}. {text}\n")

                f.write(f"\n=== 关键词搜索结果 ===\n")
                for keyword, matches in analysis_result["关键词搜索"].items():
                    f.write(f"\n'{keyword}' ({len(matches)} 条):\n")
                    for match in matches:
                        f.write(f"  - {match}\n")

            print(f"文本摘要已保存到: {summary_path}")
        except Exception as e:
            print(f"保存摘要文件失败: {e}")

    return True


def get_dxf_files(input_path: Union[str, Path]) -> List[Path]:
    """
    获取DXF文件列表

    Args:
        input_path: 输入路径（文件或文件夹）

    Returns:
        DXF文件路径列表
    """
    input_path = Path(input_path)
    dxf_files = []

    if input_path.is_file():
        if input_path.suffix.lower() == '.dxf':
            dxf_files.append(input_path)
        else:
            print(f"警告: {input_path} 不是DXF文件")
    elif input_path.is_dir():
        # 递归查找所有DXF文件
        dxf_files = list(input_path.rglob('*.dxf')) + list(input_path.rglob('*.DXF'))
        if not dxf_files:
            print(f"警告: 在目录 {input_path} 中未找到DXF文件")
    else:
        print(f"错误: 路径 {input_path} 不存在")

    return dxf_files


def create_output_structure(input_path: Union[str, Path], output_base: Optional[str] = None) -> Path:
    """
    创建输出目录结构

    Args:
        input_path: 输入路径
        output_base: 输出基础目录（可选）

    Returns:
        输出目录路径
    """
    input_path = Path(input_path)

    if output_base:
        output_dir = Path(output_base)
    else:
        if input_path.is_file():
            # 如果输入是文件，在同级目录创建输出文件夹
            output_dir = input_path.parent / f"{input_path.stem}_parsed"
        else:
            # 如果输入是文件夹，在同级目录创建输出文件夹
            output_dir = input_path.parent / f"{input_path.name}_parsed"

    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    return output_dir


def process_dxf_batch(input_path: Union[str, Path], output_base: Optional[str] = None) -> Dict[str, Any]:
    """
    批量处理DXF文件

    Args:
        input_path: 输入路径（文件或文件夹）
        output_base: 输出基础目录（可选）

    Returns:
        处理结果统计
    """
    input_path = Path(input_path)

    # 验证输入路径
    if not input_path.exists():
        raise FileNotFoundError(f"输入路径不存在: {input_path}")

    # 获取所有DXF文件
    dxf_files = get_dxf_files(input_path)
    if not dxf_files:
        print("未找到任何DXF文件")
        return {"成功": 0, "失败": 0, "总计": 0}

    # 创建输出目录
    output_dir = create_output_structure(input_path, output_base)

    print(f"找到 {len(dxf_files)} 个DXF文件")
    print(f"输出目录: {output_dir}")

    # 处理统计
    results = {
        "成功": 0,
        "失败": 0,
        "总计": len(dxf_files),
        "失败文件": []
    }

    # 使用进度条批量处理
    for dxf_file in tqdm(dxf_files, desc="处理DXF文件"):
        try:
            # 生成输出文件名（保持原文件名，改为.json后缀）
            output_filename = f"{dxf_file.stem}.json"
            output_path = output_dir / output_filename

            # 处理单个文件
            success = save_deep_analysis(str(dxf_file), str(output_path), show_progress=False)

            if success:
                results["成功"] += 1
                print(f"✓ 成功处理: {dxf_file.name}")
            else:
                results["失败"] += 1
                results["失败文件"].append(str(dxf_file))
                print(f"✗ 处理失败: {dxf_file.name}")

        except Exception as e:
            results["失败"] += 1
            results["失败文件"].append(str(dxf_file))
            print(f"✗ 处理异常: {dxf_file.name} - {e}")

    # 打印最终统计
    print(f"\n=== 批量处理完成 ===")
    print(f"总文件数: {results['总计']}")
    print(f"成功处理: {results['成功']}")
    print(f"处理失败: {results['失败']}")

    if results["失败文件"]:
        print(f"\n失败文件列表:")
        for failed_file in results["失败文件"]:
            print(f"  - {failed_file}")

    return results


def main():
    """
    主函数 - 支持单文件和批量处理
    """
    import sys

    # 示例用法
    if len(sys.argv) > 1:
        # 从命令行参数获取输入路径
        input_path = sys.argv[1]
        output_base = sys.argv[2] if len(sys.argv) > 2 else None
    else:
        # 默认示例路径
        # input_path = '/Users/<USER>/Downloads/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图.dxf'
        input_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化'
        output_base = None

    try:
        # 使用新的批量处理函数
        results = process_dxf_batch(input_path, output_base)

        if results["总计"] > 0:
            success_rate = (results["成功"] / results["总计"]) * 100
            print(f"\n处理成功率: {success_rate:.1f}%")

    except Exception as e:
        print(f"程序执行出错: {e}")
        return 1

    return 0


if __name__ == '__main__':
    exit(main())
