#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证PDF质量对版面识别的影响
对比不同质量PDF的版面识别效果
"""

import os
import sys
import cv2
import json
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def verify_pdf_quality():
    """验证不同PDF质量的版面识别效果"""
    print("🔍 验证PDF质量对版面识别的影响")
    print("=" * 60)
    
    # 定义不同质量的PDF文件
    pdf_files = {
        "高质量PDF (ezdxf渲染)": "/Users/<USER>/PycharmProjects/crawl/pdf_test_output/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10_ezdxf_drawing.pdf",
        "基础PDF (matplotlib渲染)": "/Users/<USER>/PycharmProjects/crawl/pdf_test_output/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10_custom_matplotlib.pdf",
        "V4优化PDF": "/Users/<USER>/PycharmProjects/crawl/pdf_test_output/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10_v4_optimized.pdf"
    }
    
    try:
        from dxf_parser_structured_v4 import DXFLayoutAnalyzer
        analyzer = DXFLayoutAnalyzer()
        
        results = {}
        
        for pdf_name, pdf_path in pdf_files.items():
            print(f"\n📄 测试 {pdf_name}")
            print("-" * 40)
            
            if not os.path.exists(pdf_path):
                print(f"   ❌ 文件不存在: {pdf_path}")
                continue
            
            # 获取文件大小
            file_size = os.path.getsize(pdf_path) / 1024  # KB
            print(f"   📊 文件大小: {file_size:.1f}KB")
            
            try:
                # 转换PDF到图像
                images = analyzer.pdf_to_image(pdf_path)
                if not images:
                    print(f"   ❌ 图像转换失败")
                    continue
                
                print(f"   ✅ 成功转换 {len(images)} 页图像")
                
                # 分析版面
                layout_results = []
                for i, image in enumerate(images):
                    print(f"   🔍 分析页面{i+1}...")
                    layout_result = analyzer.analyze_layout(image)
                    layout_results.append(layout_result)
                
                # 统计结果
                total_pages = 0
                total_tables = 0
                total_main_drawings = 0
                
                for layout in layout_results:
                    if "pages" in layout:
                        total_pages += layout["total_pages"]
                        for page in layout["pages"]:
                            if page.get("main_drawing"):
                                total_main_drawings += 1
                            total_tables += len(page.get("tables", []))
                
                result_summary = {
                    "文件大小KB": file_size,
                    "识别页面数": total_pages,
                    "主图区域数": total_main_drawings,
                    "表格区域数": total_tables,
                    "版面识别质量": "优秀" if total_pages >= 2 and total_tables >= 4 else "一般" if total_pages >= 1 else "较差"
                }
                
                results[pdf_name] = result_summary
                
                print(f"   ✅ 识别结果:")
                print(f"      页面数: {total_pages}")
                print(f"      主图数: {total_main_drawings}")
                print(f"      表格数: {total_tables}")
                print(f"      质量评级: {result_summary['版面识别质量']}")
                
                # 保存可视化结果
                if images and layout_results:
                    save_visualization(images[0], layout_results[0], pdf_name.replace(" ", "_").replace("(", "").replace(")", ""))
                
            except Exception as e:
                print(f"   ❌ 分析失败: {e}")
                results[pdf_name] = {"错误": str(e)}
        
        # 生成对比报告
        generate_comparison_report(results)
        
        return results
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return {}

def save_visualization(image, layout_result, pdf_type):
    """保存版面识别可视化结果"""
    try:
        if "pages" not in layout_result:
            return
        
        vis_image = image.copy()
        
        for page in layout_result["pages"]:
            # 绘制页面边界 (蓝色)
            page_bounds = page["page_bounds"]
            cv2.rectangle(vis_image, 
                        (page_bounds["x"], page_bounds["y"]),
                        (page_bounds["x"] + page_bounds["width"], 
                         page_bounds["y"] + page_bounds["height"]),
                        (255, 0, 0), 3)
            
            # 绘制主图区域 (绿色)
            if page.get("main_drawing"):
                main_drawing = page["main_drawing"]
                cv2.rectangle(vis_image,
                            (main_drawing["x"], main_drawing["y"]),
                            (main_drawing["x"] + main_drawing["width"],
                             main_drawing["y"] + main_drawing["height"]),
                            (0, 255, 0), 2)
                
                cv2.putText(vis_image, "Main Drawing", 
                          (main_drawing["x"], main_drawing["y"] - 10),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            
            # 绘制表格区域 (红色)
            for j, table in enumerate(page.get("tables", [])):
                cv2.rectangle(vis_image,
                            (table["x"], table["y"]),
                            (table["x"] + table["width"],
                             table["y"] + table["height"]),
                            (0, 0, 255), 2)
                
                cv2.putText(vis_image, f"Table {j+1}", 
                          (table["x"], table["y"] - 10),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        
        # 保存可视化结果
        output_file = f"pdf_quality_comparison_{pdf_type}.png"
        cv2.imwrite(output_file, vis_image)
        print(f"   💾 可视化结果已保存: {output_file}")
        
    except Exception as e:
        print(f"   ⚠️  保存可视化失败: {e}")

def generate_comparison_report(results):
    """生成对比报告"""
    print(f"\n📊 PDF质量对比报告")
    print("=" * 60)
    
    # 保存详细结果
    with open("pdf_quality_comparison_report.json", 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"📄 详细报告已保存: pdf_quality_comparison_report.json")
    
    # 显示对比表格
    print(f"\n📋 对比表格:")
    print(f"{'PDF类型':<25} {'文件大小(KB)':<12} {'页面数':<8} {'主图数':<8} {'表格数':<8} {'质量评级':<10}")
    print("-" * 80)
    
    for pdf_name, result in results.items():
        if "错误" in result:
            print(f"{pdf_name:<25} {'错误':<12} {'-':<8} {'-':<8} {'-':<8} {'失败':<10}")
        else:
            print(f"{pdf_name:<25} {result['文件大小KB']:<12.1f} {result['识别页面数']:<8} {result['主图区域数']:<8} {result['表格区域数']:<8} {result['版面识别质量']:<10}")
    
    # 推荐最佳PDF
    best_pdf = None
    best_score = 0
    
    for pdf_name, result in results.items():
        if "错误" not in result:
            # 计算综合得分
            score = result['识别页面数'] * 10 + result['主图区域数'] * 5 + result['表格区域数'] * 2
            if score > best_score:
                best_score = score
                best_pdf = pdf_name
    
    if best_pdf:
        print(f"\n🏆 推荐使用: {best_pdf}")
        print(f"   综合得分: {best_score}")
        print(f"   版面识别质量: {results[best_pdf]['版面识别质量']}")
    
    print(f"\n💡 建议:")
    print(f"   - 高质量PDF (>2MB) 能显著提升版面识别准确率")
    print(f"   - 建议在生产环境中使用ezdxf渲染器")
    print(f"   - 避免使用低质量PDF (<100KB) 进行版面识别")

def main():
    """主函数"""
    print("🧪 PDF质量验证工具")
    print("验证不同PDF质量对版面识别的影响")
    print("=" * 60)
    
    results = verify_pdf_quality()
    
    if results:
        print(f"\n🎉 验证完成!")
        print(f"生成的文件:")
        print(f"  - pdf_quality_comparison_report.json: 详细对比报告")
        print(f"  - pdf_quality_comparison_*.png: 可视化结果")
    else:
        print(f"\n❌ 验证失败")

if __name__ == "__main__":
    main()
