import ezdxf
import json
import re
import os
import sys
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any, Optional, Union
from tqdm import tqdm


def clean_text(text: str) -> str:
    """
    清理文本内容，移除MTEXT格式化代码
    """
    if not text:
        return ""

    # 移除MTEXT格式化代码
    text = re.sub(r'\\[A-Za-z][0-9]*;?', '', text)
    text = re.sub(r'\\[{}]', '', text)
    text = re.sub(r'\\P', '\n', text)
    text = re.sub(r'\s+', ' ', text)
    return text.strip()


def safe_coordinate_conversion(coord) -> List[float]:
    """
    安全地将坐标对象转换为列表，处理Vec3等特殊对象
    """
    if coord is None:
        return []

    try:
        # 如果已经是列表或元组，直接转换
        if isinstance(coord, (list, tuple)):
            return [float(x) for x in coord]

        # 如果是Vec3或类似对象，尝试访问其坐标属性
        if hasattr(coord, 'x') and hasattr(coord, 'y'):
            z = getattr(coord, 'z', 0.0)  # 如果没有z坐标，默认为0
            return [float(coord.x), float(coord.y), float(z)]

        # 如果对象有__iter__方法，尝试迭代
        if hasattr(coord, '__iter__'):
            return [float(x) for x in coord]

        # 如果是单个数值，返回包含该数值的列表
        return [float(coord)]

    except Exception as e:
        print(f"坐标转换警告: {e}, 原始值: {coord}")
        return []


def extract_text_from_entity(entity) -> str:
    """
    从实体中提取文本内容，支持多种文本类型
    """
    entity_type = entity.dxftype()
    text_content = ""

    try:
        if entity_type == 'TEXT':
            text_content = clean_text(entity.dxf.text)
        elif entity_type == 'MTEXT':
            # MTEXT 可能包含格式化代码，需要清理
            text_content = clean_text(entity.plain_text())
        elif entity_type == 'ATTRIB':
            # 属性文本
            text_content = clean_text(entity.dxf.text)
        elif entity_type == 'ATTDEF':
            # 属性定义
            text_content = clean_text(entity.dxf.text)
        elif entity_type == 'INSERT':
            # 块插入，可能包含属性
            if hasattr(entity, 'attribs'):
                attrib_texts = []
                for attrib in entity.attribs:
                    if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                        clean_attrib = clean_text(attrib.dxf.text)
                        if clean_attrib:
                            attrib_texts.append(f"{attrib.dxf.tag}: {clean_attrib}")
                text_content = "; ".join(attrib_texts)
        elif entity_type in ['DIMENSION', 'LEADER']:
            # 尺寸标注和引线
            dim_text = getattr(entity.dxf, 'text', '')
            if dim_text:
                text_content = clean_text(dim_text)
    except Exception as e:
        print(f"提取文本时出错 ({entity_type}): {e}")

    return text_content.strip() if text_content else ""


def extract_entity_geometry(entity) -> Dict[str, Any]:
    """
    提取实体的几何信息
    """
    entity_type = entity.dxftype()
    geometry_info = {}
    
    try:
        if entity_type == 'LINE':
            geometry_info = {
                "起点坐标": safe_coordinate_conversion(entity.dxf.start),
                "终点坐标": safe_coordinate_conversion(entity.dxf.end)
            }
        elif entity_type == 'CIRCLE':
            geometry_info = {
                "圆心坐标": safe_coordinate_conversion(entity.dxf.center),
                "半径": entity.dxf.radius
            }
        elif entity_type == 'ARC':
            geometry_info = {
                "圆心坐标": safe_coordinate_conversion(entity.dxf.center),
                "半径": entity.dxf.radius,
                "起始角度": entity.dxf.start_angle,
                "终止角度": entity.dxf.end_angle
            }
        elif entity_type == 'ELLIPSE':
            geometry_info = {
                "中心坐标": safe_coordinate_conversion(entity.dxf.center),
                "长轴端点": safe_coordinate_conversion(entity.dxf.major_axis),
                "短长轴比": entity.dxf.ratio
            }
        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
            try:
                points = [safe_coordinate_conversion(p) for p in entity.get_points()]
                geometry_info = {
                    "顶点坐标列表": points,
                    "是否闭合": entity.is_closed
                }
            except:
                geometry_info = {"错误": "无法获取顶点信息"}
        elif entity_type == 'SPLINE':
            try:
                geometry_info = {
                    "控制点数量": len(entity.control_points),
                    "度数": entity.dxf.degree,
                    "是否闭合": entity.closed
                }
            except:
                geometry_info = {"错误": "无法获取样条信息"}
        elif entity_type == 'HATCH':
            geometry_info = {
                "填充图案": getattr(entity.dxf, 'pattern_name', ''),
                "填充类型": getattr(entity.dxf, 'hatch_style', 0)
            }
        elif entity_type == 'SOLID':
            try:
                geometry_info = {
                    "顶点1": safe_coordinate_conversion(entity.dxf.vtx0),
                    "顶点2": safe_coordinate_conversion(entity.dxf.vtx1),
                    "顶点3": safe_coordinate_conversion(entity.dxf.vtx2),
                    "顶点4": safe_coordinate_conversion(entity.dxf.vtx3) if hasattr(entity.dxf, 'vtx3') else None
                }
            except:
                geometry_info = {"错误": "无法获取实体顶点信息"}
        elif entity_type == 'POINT':
            geometry_info = {
                "坐标": safe_coordinate_conversion(entity.dxf.location)
            }
    except Exception as e:
        geometry_info = {"错误": f"提取几何信息时出错: {e}"}
    
    return geometry_info


def extract_text_info(entity) -> Dict[str, Any]:
    """
    提取文本相关信息
    """
    entity_type = entity.dxftype()
    text_info = {}
    
    try:
        if entity_type in ['TEXT', 'MTEXT', 'ATTRIB', 'ATTDEF']:
            text_content = extract_text_from_entity(entity)
            text_info = {
                "插入点坐标": safe_coordinate_conversion(entity.dxf.insert) if hasattr(entity.dxf, 'insert') else [],
                "文本内容": text_content,
                "文字高度": getattr(entity.dxf, 'height', getattr(entity.dxf, 'char_height', 0)),
                "旋转角度": getattr(entity.dxf, 'rotation', 0)
            }

            # 添加特定类型的额外信息
            if entity_type == 'MTEXT':
                text_info["宽度"] = getattr(entity.dxf, 'width', 0)
            elif entity_type in ['ATTRIB', 'ATTDEF']:
                text_info["标签"] = getattr(entity.dxf, 'tag', '')
                if entity_type == 'ATTDEF':
                    text_info["提示"] = getattr(entity.dxf, 'prompt', '')

        elif entity_type == 'INSERT':
            text_content = extract_text_from_entity(entity)
            if text_content:
                text_info = {
                    "插入点坐标": safe_coordinate_conversion(entity.dxf.insert),
                    "文本内容": text_content,
                    "块名": entity.dxf.name,
                    "缩放比例": [entity.dxf.xscale, entity.dxf.yscale, entity.dxf.zscale],
                    "旋转角度": entity.dxf.rotation
                }
        elif entity_type in ['DIMENSION', 'LEADER']:
            text_content = extract_text_from_entity(entity)
            if text_content:
                text_info = {
                    "文本内容": text_content,
                    "尺寸类型": getattr(entity, 'dimtype', '未知') if entity_type == 'DIMENSION' else '引线'
                }
    except Exception as e:
        text_info = {"错误": f"提取文本信息时出错: {e}"}
    
    return text_info


def extract_block_info(entity) -> Dict[str, Any]:
    """
    提取块插入信息
    """
    if entity.dxftype() != 'INSERT':
        return {}
    
    try:
        block_info = {
            "块名": entity.dxf.name,
            "插入点坐标": safe_coordinate_conversion(entity.dxf.insert),
            "缩放比例": [entity.dxf.xscale, entity.dxf.yscale, entity.dxf.zscale],
            "旋转角度": entity.dxf.rotation
        }
        
        # 提取块属性
        text_content = extract_text_from_entity(entity)
        if text_content:
            block_info["属性文本"] = text_content
            
        return block_info
    except Exception as e:
        return {"错误": f"提取块信息时出错: {e}"}


def extract_dimension_info(entity) -> Dict[str, Any]:
    """
    提取尺寸标注信息
    """
    if entity.dxftype() != 'DIMENSION':
        return {}
    
    try:
        dim_info = {
            "尺寸文本": getattr(entity.dxf, 'text', ''),
            "尺寸类型": getattr(entity, 'dimtype', '未知')
        }
        return dim_info
    except Exception as e:
        return {"错误": f"提取尺寸信息时出错: {e}"}


def extract_leader_info(entity) -> Dict[str, Any]:
    """
    提取引线信息
    """
    if entity.dxftype() != 'LEADER':
        return {}

    try:
        leader_info = {
            "顶点数量": len(entity.vertices) if hasattr(entity, 'vertices') else 0
        }
        return leader_info
    except Exception as e:
        return {"错误": f"提取引线信息时出错: {e}"}


def extract_comprehensive_text_from_dxf(doc) -> Dict[str, Any]:
    """
    全面提取DXF文件中的所有文本内容，包括模型空间、图纸空间和块定义
    """
    all_texts = []
    text_by_layer = defaultdict(list)

    def extract_from_space(entities, space_name: str):
        """从指定空间提取文本"""
        for entity in entities:
            text_content = extract_text_from_entity(entity)
            if text_content:
                text_entity = {
                    "空间": space_name,
                    "实体类型": entity.dxftype(),
                    "图层": entity.dxf.layer,
                    "文本内容": text_content,
                    "坐标": safe_coordinate_conversion(entity.dxf.insert) if hasattr(entity.dxf, 'insert') else []
                }
                all_texts.append(text_entity)
                text_by_layer[entity.dxf.layer].append(text_entity)

    # 提取模型空间
    extract_from_space(doc.modelspace(), "模型空间")

    # 提取图纸空间
    for layout in doc.layouts:
        if layout.name != 'Model':
            extract_from_space(layout, f"图纸空间-{layout.name}")

    # 提取块定义
    for block in doc.blocks:
        if not block.name.startswith('*'):
            extract_from_space(block, f"块定义-{block.name}")

    return {
        "所有文本实体": all_texts,
        "按图层分组": {layer: [t["文本内容"] for t in texts]
                     for layer, texts in text_by_layer.items()},
        "所有文本内容": [t["文本内容"] for t in all_texts if t["文本内容"]],
        "文本实体数量": len(all_texts)
    }


def parse_dxf_to_comprehensive_json(dxf_path: str, json_path: str) -> bool:
    """
    解析DXF文件，并将其中的实体信息转换为使用中文键的、对大模型友好的JSON格式。
    优化版本：支持更多实体类型，全面提取所有内容，保留原有输出格式。

    :param dxf_path: 输入的DXF文件路径
    :param json_path: 输出的JSON文件路径
    :return: 是否成功解析
    """
    try:
        doc = ezdxf.readfile(dxf_path)
    except IOError:
        print(f"错误: 无法找到或打开文件: {dxf_path}")
        return False
    except ezdxf.DXFStructureError:
        print(f"错误: 无效或损坏的DXF文件: {dxf_path}")
        return False
    except Exception as e:
        print(f"错误: 读取DXF文件时出现异常: {e}")
        return False

    # 统计实体类型
    entity_stats = defaultdict(int)
    parsed_data = []

    print(f"开始解析DXF文件: {dxf_path}")

    # 处理模型空间
    msp = doc.modelspace()
    for entity in msp:
        entity_type = entity.dxftype()
        entity_stats[entity_type] += 1

        # 使用描述性的中文作为key
        entity_data = {
            "实体类型": entity_type,
            "所在图层": entity.dxf.layer,
            "颜色索引": entity.dxf.color,
        }

        # 添加更多通用属性
        try:
            if hasattr(entity.dxf, 'linetype'):
                entity_data["线型"] = entity.dxf.linetype
            if hasattr(entity.dxf, 'lineweight'):
                entity_data["线宽"] = entity.dxf.lineweight
            if hasattr(entity.dxf, 'transparency'):
                entity_data["透明度"] = entity.dxf.transparency
        except:
            pass

        # 根据不同实体类型，提取其核心信息
        geometry_info = extract_entity_geometry(entity)
        if geometry_info:
            entity_data['几何信息'] = geometry_info

        text_info = extract_text_info(entity)
        if text_info:
            entity_data['文本信息'] = text_info

        if entity_type == 'INSERT':
            block_info = extract_block_info(entity)
            if block_info:
                entity_data['块信息'] = block_info
        elif entity_type == 'DIMENSION':
            dim_info = extract_dimension_info(entity)
            if dim_info:
                entity_data['尺寸信息'] = dim_info
        elif entity_type == 'LEADER':
            leader_info = extract_leader_info(entity)
            if leader_info:
                entity_data['引线信息'] = leader_info

        parsed_data.append(entity_data)

    # 全面提取文本内容
    comprehensive_text = extract_comprehensive_text_from_dxf(doc)

    # 提取图层信息
    layer_info = {}
    for layer in doc.layers:
        layer_name = layer.dxf.name
        layer_info[layer_name] = {
            "颜色": layer.dxf.color,
            "线型": layer.dxf.linetype,
            "是否冻结": layer.is_frozen(),
            "是否锁定": layer.is_locked(),
            "是否关闭": layer.is_off()
        }

    # 打印统计信息
    print(f"\n实体类型统计:")
    for entity_type, count in sorted(entity_stats.items()):
        print(f"  {entity_type}: {count}")

    print(f"\n找到 {comprehensive_text['文本实体数量']} 个包含文本内容的实体")

    # 为了让LLM更好地理解整个文件，我们创建一个包含元数据和实体数据的根对象
    final_output = {
        "文件元数据": {
            "文件名": dxf_path,
            "DXF版本": doc.dxfversion,
            "单位": "未指定 (通常为毫米)",
            "坐标系": "世界坐标系 (WCS)",
            "实体统计": dict(entity_stats),
            "文本实体数量": comprehensive_text['文本实体数量'],
            "图层数量": len(layer_info)
        },
        "文本内容汇总": comprehensive_text,
        "图层信息": layer_info,
        "实体列表": parsed_data
    }

    # 确保输出目录存在
    output_dir = os.path.dirname(json_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)

    try:
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(final_output, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False

    print(f"成功将 {dxf_path} 解析并保存至 {json_path}")
    print(f"包含 {len(parsed_data)} 个实体，其中 {comprehensive_text['文本实体数量']} 个包含有效文本内容")

    return True


def get_dxf_files(input_path: Union[str, Path]) -> List[Path]:
    """
    获取DXF文件列表，支持单文件和目录

    :param input_path: 输入路径（文件或文件夹）
    :return: DXF文件路径列表
    """
    input_path = Path(input_path)
    dxf_files = []

    if input_path.is_file():
        if input_path.suffix.lower() == '.dxf':
            dxf_files.append(input_path)
        else:
            print(f"警告: {input_path} 不是DXF文件")
    elif input_path.is_dir():
        # 递归查找所有DXF文件
        dxf_files = list(input_path.rglob('*.dxf')) + list(input_path.rglob('*.DXF'))
        if not dxf_files:
            print(f"警告: 在目录 {input_path} 中未找到DXF文件")
    else:
        print(f"错误: 路径 {input_path} 不存在")

    return dxf_files


def process_single_dxf(dxf_path: Path, output_dir: Optional[Path] = None) -> bool:
    """
    处理单个DXF文件

    :param dxf_path: DXF文件路径
    :param output_dir: 输出目录，如果为None则在DXF文件同目录下输出
    :return: 是否成功处理
    """
    try:
        # 确定输出路径
        if output_dir:
            output_path = output_dir / f"{dxf_path.stem}.json"
        else:
            output_path = dxf_path.parent / f"{dxf_path.stem}.json"

        # 解析DXF文件
        success = parse_dxf_to_comprehensive_json(str(dxf_path), str(output_path))

        if success:
            print(f"✓ 成功处理: {dxf_path.name} -> {output_path.name}")
        else:
            print(f"✗ 处理失败: {dxf_path.name}")

        return success

    except Exception as e:
        print(f"✗ 处理异常: {dxf_path.name} - {e}")
        return False


def process_dxf_batch(input_path: Union[str, Path], output_dir: Optional[str] = None) -> Dict[str, Any]:
    """
    批量处理DXF文件

    :param input_path: 输入路径（文件或文件夹）
    :param output_dir: 输出目录（可选）
    :return: 处理结果统计
    """
    input_path = Path(input_path)

    # 验证输入路径
    if not input_path.exists():
        raise FileNotFoundError(f"输入路径不存在: {input_path}")

    # 获取所有DXF文件
    dxf_files = get_dxf_files(input_path)
    if not dxf_files:
        print("未找到任何DXF文件")
        return {"成功": 0, "失败": 0, "总计": 0, "失败文件": []}

    # 确定输出目录
    output_path = None
    if output_dir:
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

    print(f"找到 {len(dxf_files)} 个DXF文件")
    if output_path:
        print(f"输出目录: {output_path}")
    else:
        print("输出到原文件同目录")

    # 处理统计
    results = {
        "成功": 0,
        "失败": 0,
        "总计": len(dxf_files),
        "失败文件": []
    }

    # 使用进度条批量处理
    for dxf_file in tqdm(dxf_files, desc="处理DXF文件"):
        success = process_single_dxf(dxf_file, output_path)

        if success:
            results["成功"] += 1
        else:
            results["失败"] += 1
            results["失败文件"].append(str(dxf_file))

    # 打印最终统计
    print(f"\n=== 批量处理完成 ===")
    print(f"总文件数: {results['总计']}")
    print(f"成功处理: {results['成功']}")
    print(f"处理失败: {results['失败']}")

    if results["失败文件"]:
        print(f"\n失败文件列表:")
        for failed_file in results["失败文件"]:
            print(f"  - {failed_file}")

    if results["总计"] > 0:
        success_rate = (results["成功"] / results["总计"]) * 100
        print(f"\n处理成功率: {success_rate:.1f}%")

    return results


def main():
    """
    主函数 - 支持单文件和批量处理
    """
    import argparse

    parser = argparse.ArgumentParser(description='DXF文件解析工具 v1.0 - 支持单文件和批量处理')
    parser.add_argument('input', help='输入DXF文件或包含DXF文件的目录')
    parser.add_argument('-o', '--output', help='输出目录（可选，默认为输入文件同目录）')
    parser.add_argument('--version', action='version', version='DXF Parser v1.0')

    # 如果没有命令行参数，使用默认示例
    if len(sys.argv) == 1:
        print("DXF文件解析工具 v1.0")
        print("使用示例:")
        print("  python dxf_parser_v1.py input.dxf")
        print("  python dxf_parser_v1.py /path/to/dxf/directory")
        print("  python dxf_parser_v1.py input.dxf -o /path/to/output")
        print("\n使用默认示例文件进行测试...")

        # 默认示例路径
        default_input = '/Users/<USER>/Downloads/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图.dxf'
        default_input = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化'
        if os.path.exists(default_input):
            input_path = default_input
            output_dir = None
        else:
            print("默认示例文件不存在，请提供输入路径")
            return 1
    else:
        args = parser.parse_args()
        input_path = args.input
        output_dir = args.output

    try:
        # 使用批量处理函数（支持单文件和目录）
        results = process_dxf_batch(input_path, output_dir)

        return 0 if results["失败"] == 0 else 1

    except Exception as e:
        print(f"程序执行出错: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
