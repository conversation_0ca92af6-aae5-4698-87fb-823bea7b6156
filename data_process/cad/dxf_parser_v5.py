
# -*- coding: utf-8 -*-
"""
DXF智能解析器V5 - 完整重构版本
高精度PDF转换 + 版面分析 + 坐标映射 + 内容填充
"""

import os
import json
import ezdxf
import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
from typing import Dict, List, Any, Optional, Tuple
import re
import shutil


class DXFParserV5:
    """DXF智能解析器V5 - 完整实现版本"""
    
    def __init__(self, dxf_path: str):
        self.dxf_path = dxf_path
        self.doc = None
        self.entities = []
        self.texts = []
        self.coordinate_mapping = {}

        # 创建输出目录
        self.output_dir = self._create_output_directory()
        self.pdf_path = None
        self.layout_image_path = None
        
    def parse(self) -> Dict:
        """主解析方法"""
        print(f"🚀 DXF智能解析V5")
        print(f"📁 文件: {os.path.basename(self.dxf_path)}")
        
        # 1. 高精度PDF转换
        pdf_path = self._convert_to_high_quality_pdf()
        if not pdf_path:
            return {"错误": "PDF转换失败"}
        
        # 2. DXF内容解析（先解析内容，再分析版面）
        if not self._parse_dxf_content():
            return {"错误": "DXF解析失败"}

        # 3. 版面分析（基于解析的内容）
        layout_result = self._analyze_layout(pdf_path)
        if not layout_result:
            return {"错误": "版面分析失败"}
        
        # 4. 坐标映射
        self._establish_coordinate_mapping(layout_result)
        
        # 5. 内容填充
        result = self._fill_content_to_layout(layout_result)
        
        print(f"✅ 解析完成")
        print(f"📁 输出目录: {self.output_dir}")
        return result

    def _create_output_directory(self) -> str:
        """创建输出目录"""
        base_name = os.path.splitext(os.path.basename(self.dxf_path))[0]
        output_dir = f"./dxf_v5_output_{base_name}"

        # 如果目录存在，先删除
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)

        # 创建新目录
        os.makedirs(output_dir, exist_ok=True)

        return output_dir
    
    def _convert_to_high_quality_pdf(self) -> Optional[str]:
        """高精度PDF转换 - 使用ezdxf drawing渲染"""
        print("1️⃣ 高精度PDF转换（ezdxf drawing渲染）...")

        try:
            from ezdxf.addons.drawing import RenderContext, Frontend
            from ezdxf.addons.drawing.matplotlib import MatplotlibBackend

            # 加载DXF文档
            self.doc = ezdxf.readfile(self.dxf_path)
            msp = self.doc.modelspace()

            # 创建渲染上下文
            ctx = RenderContext(self.doc)

            # 计算图纸边界
            try:
                bbox = ezdxf.bbox.extents(msp)
                if bbox.has_data:
                    width = bbox.size.x
                    height = bbox.size.y
                    center = bbox.center
                    bounds = {
                        'min_x': bbox.extmin.x,
                        'max_x': bbox.extmax.x,
                        'min_y': bbox.extmin.y,
                        'max_y': bbox.extmax.y
                    }
                    print(f"   📐 图纸边界: X({bounds['min_x']:.0f}~{bounds['max_x']:.0f}), Y({bounds['min_y']:.0f}~{bounds['max_y']:.0f})")
                    print(f"   📏 图纸尺寸: {width:.0f} x {height:.0f}")
                else:
                    # 默认边界
                    width, height = 100000, 100000
                    center = (0, 0)
                    bounds = {'min_x': 0, 'max_x': width, 'min_y': 0, 'max_y': height}
                    print("   ⚠️  使用默认边界")
            except Exception as e:
                width, height = 100000, 100000
                center = (0, 0)
                bounds = {'min_x': 0, 'max_x': width, 'min_y': 0, 'max_y': height}
                print(f"   ⚠️  边界计算失败，使用默认边界: {e}")

            # 设置合适的图形尺寸
            aspect_ratio = width / height if height > 0 else 1
            if aspect_ratio > 1:
                fig_width = 20
                fig_height = 20 / aspect_ratio
            else:
                fig_height = 20
                fig_width = 20 * aspect_ratio

            # 创建PDF路径
            pdf_path = os.path.join(self.output_dir, "high_quality.pdf")

            # 创建matplotlib后端
            fig, ax = plt.subplots(figsize=(fig_width, fig_height))
            backend = MatplotlibBackend(ax)

            # 创建前端并渲染
            frontend = Frontend(ctx, backend)
            frontend.draw_layout(msp, finalize=True)

            # 设置显示属性
            ax.set_aspect('equal')
            ax.set_facecolor('white')
            fig.patch.set_facecolor('white')

            # 移除坐标轴
            ax.set_xticks([])
            ax.set_yticks([])
            for spine in ax.spines.values():
                spine.set_visible(False)

            # 保存为PDF
            with PdfPages(pdf_path) as pdf:
                pdf.savefig(fig, bbox_inches='tight', dpi=300,
                           facecolor='white', edgecolor='none')

            plt.close(fig)

            # 检查文件大小
            if os.path.exists(pdf_path):
                file_size = os.path.getsize(pdf_path) / 1024
                print(f"   ✅ ezdxf drawing渲染完成: {file_size:.1f}KB")

                # 保存PDF路径供后续使用
                self.pdf_path = pdf_path
                return pdf_path
            else:
                print("   ❌ PDF文件未生成")
                return None

        except ImportError as e:
            print(f"   ❌ ezdxf drawing addon不可用: {e}")
            print("   🔄 回退到基础渲染方法...")
            return self._fallback_pdf_conversion()
        except Exception as e:
            print(f"   ❌ ezdxf drawing渲染失败: {e}")
            print("   🔄 回退到基础渲染方法...")
            return self._fallback_pdf_conversion()

    def _fallback_pdf_conversion(self) -> Optional[str]:
        """回退的PDF转换方法"""
        try:
            if not self.doc:
                self.doc = ezdxf.readfile(self.dxf_path)

            msp = self.doc.modelspace()

            # 使用简化的matplotlib渲染
            bounds = self._calculate_drawing_bounds(msp)
            if not bounds:
                return None

            width = bounds['max_x'] - bounds['min_x']
            height = bounds['max_y'] - bounds['min_y']

            fig_width = 16
            fig_height = 16 * height / width if width > 0 else 16

            pdf_path = os.path.join(self.output_dir, "fallback_quality.pdf")

            with PdfPages(pdf_path) as pdf:
                fig, ax = plt.subplots(figsize=(fig_width, fig_height))

                # 渲染实体
                rendered_count = 0
                for entity in msp:
                    if self._render_entity_v4_style(ax, entity):
                        rendered_count += 1

                # 设置显示
                margin = max(width, height) * 0.02
                ax.set_xlim(bounds['min_x'] - margin, bounds['max_x'] + margin)
                ax.set_ylim(bounds['min_y'] - margin, bounds['max_y'] + margin)
                ax.set_aspect('equal')
                ax.set_facecolor('white')
                fig.patch.set_facecolor('white')

                # 移除坐标轴
                ax.set_xticks([])
                ax.set_yticks([])
                for spine in ax.spines.values():
                    spine.set_visible(False)

                pdf.savefig(fig, bbox_inches='tight', dpi=300,
                           facecolor='white', edgecolor='none')
                plt.close(fig)

            file_size = os.path.getsize(pdf_path) / 1024
            print(f"   ✅ 回退渲染完成: {file_size:.1f}KB")

            self.pdf_path = pdf_path
            return pdf_path

        except Exception as e:
            print(f"   ❌ 回退渲染也失败: {e}")
            return None
    
    def _calculate_drawing_bounds(self, msp):
        """计算图纸的精确边界 - V4风格"""
        min_x, min_y = float('inf'), float('inf')
        max_x, max_y = float('-inf'), float('-inf')

        found_entities = False

        for entity in msp:
            try:
                entity_type = entity.dxftype()
                bounds = None

                if entity_type == 'LINE':
                    start, end = entity.dxf.start, entity.dxf.end
                    bounds = [start.x, start.y, end.x, end.y]
                elif entity_type == 'CIRCLE':
                    center = entity.dxf.center
                    radius = entity.dxf.radius
                    bounds = [center.x - radius, center.y - radius,
                             center.x + radius, center.y + radius]
                elif entity_type in ['TEXT', 'MTEXT']:
                    insert = entity.dxf.insert
                    height = getattr(entity.dxf, 'height', 100)
                    bounds = [insert.x, insert.y, insert.x + height, insert.y + height]
                elif entity_type == 'LWPOLYLINE':
                    points = list(entity.get_points())
                    if points:
                        x_coords = [p[0] for p in points]
                        y_coords = [p[1] for p in points]
                        bounds = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
                elif entity_type == 'POLYLINE':
                    vertices = list(entity.vertices)
                    if vertices:
                        x_coords = [v.dxf.location.x for v in vertices]
                        y_coords = [v.dxf.location.y for v in vertices]
                        bounds = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
                elif entity_type == 'ARC':
                    center = entity.dxf.center
                    radius = entity.dxf.radius
                    bounds = [center.x - radius, center.y - radius,
                             center.x + radius, center.y + radius]
                elif entity_type == 'INSERT':
                    insert = entity.dxf.insert
                    bounds = [insert.x, insert.y, insert.x + 1000, insert.y + 1000]

                if bounds:
                    min_x = min(min_x, bounds[0], bounds[2])
                    min_y = min(min_y, bounds[1], bounds[3])
                    max_x = max(max_x, bounds[0], bounds[2])
                    max_y = max(max_y, bounds[1], bounds[3])
                    found_entities = True

            except Exception:
                continue

        if not found_entities:
            return None

        return {'min_x': min_x, 'min_y': min_y, 'max_x': max_x, 'max_y': max_y}

    def _render_entity_v4_style(self, ax, entity) -> bool:
        """V4风格的实体渲染"""
        try:
            entity_type = entity.dxftype()

            if entity_type == 'LINE':
                start, end = entity.dxf.start, entity.dxf.end
                ax.plot([start.x, end.x], [start.y, end.y], 'k-', linewidth=0.3)
                return True

            elif entity_type == 'CIRCLE':
                center = entity.dxf.center
                radius = entity.dxf.radius
                circle = plt.Circle((center.x, center.y), radius, fill=False, color='black', linewidth=0.3)
                ax.add_patch(circle)
                return True

            elif entity_type in ['TEXT', 'MTEXT']:
                insert = entity.dxf.insert
                text = entity.text if entity_type == 'MTEXT' else entity.dxf.text
                if text and text.strip():
                    height = getattr(entity.dxf, 'height', 100)
                    font_size = max(0.5, height / 100)
                    ax.text(insert.x, insert.y, text, fontsize=font_size, color='black',
                           ha='left', va='bottom')
                return True

            elif entity_type == 'LWPOLYLINE':
                points = list(entity.get_points())
                if len(points) > 1:
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    ax.plot(x_coords, y_coords, 'k-', linewidth=0.3)
                return True

            elif entity_type == 'POLYLINE':
                vertices = list(entity.vertices)
                if len(vertices) > 1:
                    x_coords = [v.dxf.location.x for v in vertices]
                    y_coords = [v.dxf.location.y for v in vertices]
                    ax.plot(x_coords, y_coords, 'k-', linewidth=0.3)
                return True

            elif entity_type == 'ARC':
                center = entity.dxf.center
                radius = entity.dxf.radius
                start_angle = entity.dxf.start_angle
                end_angle = entity.dxf.end_angle

                # 创建弧线
                import numpy as np
                if end_angle < start_angle:
                    end_angle += 360
                angles = np.linspace(np.radians(start_angle), np.radians(end_angle), 50)
                x_coords = center.x + radius * np.cos(angles)
                y_coords = center.y + radius * np.sin(angles)
                ax.plot(x_coords, y_coords, 'k-', linewidth=0.3)
                return True

            elif entity_type == 'INSERT':
                # 简单渲染块插入
                insert = entity.dxf.insert
                ax.plot(insert.x, insert.y, 'ko', markersize=1)
                return True

        except Exception:
            pass

        return False
    
    def _extract_entity_coordinates(self, entity) -> List[Tuple[float, float]]:
        """提取实体坐标"""
        coords = []
        try:
            if entity.dxftype() == 'LINE':
                coords.extend([(entity.dxf.start.x, entity.dxf.start.y),
                              (entity.dxf.end.x, entity.dxf.end.y)])
            elif entity.dxftype() == 'CIRCLE':
                center = entity.dxf.center
                coords.append((center.x, center.y))
            elif entity.dxftype() in ['TEXT', 'MTEXT']:
                insert = entity.dxf.insert
                coords.append((insert.x, insert.y))
            elif entity.dxftype() == 'LWPOLYLINE':
                coords.extend(list(entity.get_points()))
        except Exception:
            pass
        return coords
    
    def _analyze_layout(self, pdf_path: str) -> Optional[Dict]:
        """版面分析 - 基于DXF坐标并生成可视化结果"""
        print("2️⃣ 版面分析...")

        try:
            # 基于DXF坐标范围进行版面分析
            if not self.texts:
                print("   ⚠️  无文本数据，使用默认布局")
                layout_result = self._get_default_layout()
            else:
                # 计算文本坐标范围
                x_coords = [t["x"] for t in self.texts]
                y_coords = [t["y"] for t in self.texts]

                x_min, x_max = min(x_coords), max(x_coords)
                y_min, y_max = min(y_coords), max(y_coords)

                # 检测是否为双页布局（基于X坐标分布）
                mid_x = (x_min + x_max) / 2
                left_texts = [t for t in self.texts if t["x"] < mid_x]
                right_texts = [t for t in self.texts if t["x"] >= mid_x]

                # 如果左右都有足够的文本，认为是双页
                is_dual_page = len(left_texts) > 10 and len(right_texts) > 10

                if is_dual_page:
                    pages = self._create_dual_page_layout(x_min, x_max, y_min, y_max, mid_x)
                    print(f"   ✅ 检测到双页布局")
                else:
                    pages = self._create_single_page_layout(x_min, x_max, y_min, y_max)
                    print(f"   ✅ 检测到单页布局")

                layout_result = {
                    "coordinate_bounds": {"x_min": x_min, "x_max": x_max, "y_min": y_min, "y_max": y_max},
                    "pages": pages,
                    "total_pages": len(pages)
                }

            # 生成版面识别可视化结果
            self._generate_layout_visualization(layout_result)

            return layout_result

        except Exception as e:
            print(f"   ❌ 版面分析失败: {e}")
            return self._get_default_layout()

    def _generate_layout_visualization(self, layout_result: Dict):
        """生成版面识别可视化结果"""
        try:
            print("   🎨 生成版面识别可视化...")

            if not self.pdf_path or not os.path.exists(self.pdf_path):
                print("   ⚠️  PDF文件不存在，跳过可视化")
                return

            # 使用PDF转图像进行可视化（如果可用）
            try:
                from pdf2image import convert_from_path
                images = convert_from_path(self.pdf_path, dpi=150)
                if images:
                    image = np.array(images[0])
                    self._draw_layout_boxes(image, layout_result)
                else:
                    print("   ⚠️  PDF转图像失败，使用DXF坐标可视化")
                    self._draw_layout_boxes_from_dxf(layout_result)
            except ImportError:
                print("   ⚠️  pdf2image不可用，使用DXF坐标可视化")
                self._draw_layout_boxes_from_dxf(layout_result)
            except Exception as e:
                print(f"   ⚠️  PDF可视化失败: {e}，使用DXF坐标可视化")
                self._draw_layout_boxes_from_dxf(layout_result)

        except Exception as e:
            print(f"   ❌ 可视化生成失败: {e}")

    def _draw_layout_boxes_from_dxf(self, layout_result: Dict):
        """基于DXF坐标绘制版面框"""
        try:
            bounds = layout_result["coordinate_bounds"]
            width = bounds["x_max"] - bounds["x_min"]
            height = bounds["y_max"] - bounds["y_min"]

            # 创建可视化图像
            fig, ax = plt.subplots(figsize=(16, 12))

            # 绘制所有文本点
            if self.texts:
                x_coords = [t["x"] for t in self.texts]
                y_coords = [t["y"] for t in self.texts]
                ax.scatter(x_coords, y_coords, c='lightgray', s=1, alpha=0.5, label='文本位置')

            # 绘制版面框
            colors = ['red', 'blue', 'green', 'orange']
            for i, page in enumerate(layout_result["pages"]):
                color = colors[i % len(colors)]

                # 绘制页面边界
                page_bounds = page["bounds"]
                rect = plt.Rectangle((page_bounds["x_min"], page_bounds["y_min"]),
                                   page_bounds["x_max"] - page_bounds["x_min"],
                                   page_bounds["y_max"] - page_bounds["y_min"],
                                   fill=False, edgecolor=color, linewidth=3,
                                   label=f'页面{page["page_number"]}')
                ax.add_patch(rect)

                # 绘制主图区域
                main_drawing = page["main_drawing"]
                rect = plt.Rectangle((main_drawing["x_min"], main_drawing["y_min"]),
                                   main_drawing["x_max"] - main_drawing["x_min"],
                                   main_drawing["y_max"] - main_drawing["y_min"],
                                   fill=False, edgecolor='green', linewidth=2,
                                   linestyle='--', alpha=0.7)
                ax.add_patch(rect)

                # 绘制表格区域
                for j, table in enumerate(page["tables"]):
                    rect = plt.Rectangle((table["x_min"], table["y_min"]),
                                       table["x_max"] - table["x_min"],
                                       table["y_max"] - table["y_min"],
                                       fill=False, edgecolor='purple', linewidth=2,
                                       linestyle=':', alpha=0.7)
                    ax.add_patch(rect)

            # 设置显示
            ax.set_xlim(bounds["x_min"] - width*0.05, bounds["x_max"] + width*0.05)
            ax.set_ylim(bounds["y_min"] - height*0.05, bounds["y_max"] + height*0.05)
            ax.set_aspect('equal')
            ax.legend()
            ax.set_title('版面识别结果 (基于DXF坐标)')
            ax.grid(True, alpha=0.3)

            # 保存可视化结果
            vis_path = os.path.join(self.output_dir, "layout_visualization.png")
            plt.savefig(vis_path, dpi=300, bbox_inches='tight')
            plt.close(fig)

            self.layout_image_path = vis_path
            print(f"   ✅ 版面可视化已保存: {os.path.basename(vis_path)}")

        except Exception as e:
            print(f"   ❌ DXF坐标可视化失败: {e}")

    def _draw_layout_boxes(self, image: np.ndarray, layout_result: Dict):
        """在PDF图像上绘制版面框"""
        try:
            # 这里需要建立图像坐标与DXF坐标的映射关系
            # 暂时使用DXF坐标可视化
            self._draw_layout_boxes_from_dxf(layout_result)
        except Exception as e:
            print(f"   ❌ 图像版面可视化失败: {e}")
            self._draw_layout_boxes_from_dxf(layout_result)

    def _get_default_layout(self) -> Dict:
        """获取默认布局"""
        return {
            "coordinate_bounds": {"x_min": 0, "x_max": 100000, "y_min": 0, "y_max": 100000},
            "pages": self._create_dual_page_layout(0, 100000, 0, 100000, 50000),
            "total_pages": 2
        }

    def _create_dual_page_layout(self, x_min: float, x_max: float, y_min: float, y_max: float, mid_x: float) -> List[Dict]:
        """创建双页布局"""
        pages = []

        # 左页
        left_page = {
            "page_number": 1,
            "bounds": {"x_min": x_min, "x_max": mid_x, "y_min": y_min, "y_max": y_max},
            "main_drawing": {
                "x_min": x_min + (mid_x - x_min) * 0.05,
                "x_max": mid_x - (mid_x - x_min) * 0.05,
                "y_min": y_min + (y_max - y_min) * 0.3,
                "y_max": y_max - (y_max - y_min) * 0.1
            },
            "tables": [
                {
                    "name": "左下表格",
                    "x_min": x_min + (mid_x - x_min) * 0.05,
                    "x_max": x_min + (mid_x - x_min) * 0.45,
                    "y_min": y_min + (y_max - y_min) * 0.05,
                    "y_max": y_min + (y_max - y_min) * 0.25
                },
                {
                    "name": "右下表格",
                    "x_min": x_min + (mid_x - x_min) * 0.55,
                    "x_max": mid_x - (mid_x - x_min) * 0.05,
                    "y_min": y_min + (y_max - y_min) * 0.05,
                    "y_max": y_min + (y_max - y_min) * 0.25
                }
            ]
        }

        # 右页
        right_page = {
            "page_number": 2,
            "bounds": {"x_min": mid_x, "x_max": x_max, "y_min": y_min, "y_max": y_max},
            "main_drawing": {
                "x_min": mid_x + (x_max - mid_x) * 0.05,
                "x_max": x_max - (x_max - mid_x) * 0.05,
                "y_min": y_min + (y_max - y_min) * 0.3,
                "y_max": y_max - (y_max - y_min) * 0.1
            },
            "tables": [
                {
                    "name": "左下表格",
                    "x_min": mid_x + (x_max - mid_x) * 0.05,
                    "x_max": mid_x + (x_max - mid_x) * 0.45,
                    "y_min": y_min + (y_max - y_min) * 0.05,
                    "y_max": y_min + (y_max - y_min) * 0.25
                },
                {
                    "name": "右下表格",
                    "x_min": mid_x + (x_max - mid_x) * 0.55,
                    "x_max": x_max - (x_max - mid_x) * 0.05,
                    "y_min": y_min + (y_max - y_min) * 0.05,
                    "y_max": y_min + (y_max - y_min) * 0.25
                }
            ]
        }

        pages.extend([left_page, right_page])
        return pages

    def _create_single_page_layout(self, x_min: float, x_max: float, y_min: float, y_max: float) -> List[Dict]:
        """创建单页布局"""
        page = {
            "page_number": 1,
            "bounds": {"x_min": x_min, "x_max": x_max, "y_min": y_min, "y_max": y_max},
            "main_drawing": {
                "x_min": x_min + (x_max - x_min) * 0.05,
                "x_max": x_max - (x_max - x_min) * 0.05,
                "y_min": y_min + (y_max - y_min) * 0.3,
                "y_max": y_max - (y_max - y_min) * 0.1
            },
            "tables": [
                {
                    "name": "左下表格",
                    "x_min": x_min + (x_max - x_min) * 0.05,
                    "x_max": x_min + (x_max - x_min) * 0.45,
                    "y_min": y_min + (y_max - y_min) * 0.05,
                    "y_max": y_min + (y_max - y_min) * 0.25
                },
                {
                    "name": "右下表格",
                    "x_min": x_min + (x_max - x_min) * 0.55,
                    "x_max": x_max - (x_max - x_min) * 0.05,
                    "y_min": y_min + (y_max - y_min) * 0.05,
                    "y_max": y_min + (y_max - y_min) * 0.25
                }
            ]
        }
        return [page]
    

    
    def _parse_dxf_content(self) -> bool:
        """解析DXF内容"""
        print("3️⃣ DXF内容解析...")
        
        try:
            if not self.doc:
                self.doc = ezdxf.readfile(self.dxf_path)
            
            msp = self.doc.modelspace()
            
            # 解析所有实体
            for entity in msp:
                entity_info = self._extract_entity_info(entity)
                if entity_info:
                    self.entities.append(entity_info)
                    
                    # 单独收集文本
                    if entity_info["type"] in ["TEXT", "MTEXT"]:
                        self.texts.append(entity_info)
            
            print(f"   ✅ 解析完成: {len(self.entities)}个实体, {len(self.texts)}个文本")
            return True
            
        except Exception as e:
            print(f"   ❌ DXF解析失败: {e}")
            return False
    
    def _extract_entity_info(self, entity) -> Optional[Dict]:
        """提取实体信息"""
        try:
            entity_info = {
                "type": entity.dxftype(),
                "layer": getattr(entity.dxf, 'layer', ''),
                "color": getattr(entity.dxf, 'color', 256)
            }
            
            if entity.dxftype() == 'TEXT':
                entity_info.update({
                    "text": entity.dxf.text,
                    "x": entity.dxf.insert.x,
                    "y": entity.dxf.insert.y,
                    "height": entity.dxf.height
                })
            elif entity.dxftype() == 'MTEXT':
                entity_info.update({
                    "text": entity.text,
                    "x": entity.dxf.insert.x,
                    "y": entity.dxf.insert.y,
                    "height": getattr(entity.dxf, 'height', 0)
                })
            elif entity.dxftype() == 'LINE':
                entity_info.update({
                    "start_x": entity.dxf.start.x,
                    "start_y": entity.dxf.start.y,
                    "end_x": entity.dxf.end.x,
                    "end_y": entity.dxf.end.y
                })
            elif entity.dxftype() == 'CIRCLE':
                entity_info.update({
                    "center_x": entity.dxf.center.x,
                    "center_y": entity.dxf.center.y,
                    "radius": entity.dxf.radius
                })
            
            return entity_info

        except Exception:
            return None

    def _establish_coordinate_mapping(self, layout_result: Dict):
        """建立坐标映射关系 - 简化版本"""
        print("4️⃣ 建立坐标映射...")

        # 直接使用DXF坐标，无需复杂映射
        self.coordinate_mapping = layout_result["coordinate_bounds"]

        print(f"   ✅ 坐标映射建立完成")
        print(f"      DXF范围: X({self.coordinate_mapping['x_min']:.0f}~{self.coordinate_mapping['x_max']:.0f})")
        print(f"      DXF范围: Y({self.coordinate_mapping['y_min']:.0f}~{self.coordinate_mapping['y_max']:.0f})")

    def _is_point_in_region(self, x: float, y: float, region: Dict) -> bool:
        """判断点是否在区域内 - 使用DXF坐标"""
        return (region["x_min"] <= x <= region["x_max"] and
                region["y_min"] <= y <= region["y_max"])

    def _fill_content_to_layout(self, layout_result: Dict) -> Dict:
        """将内容填充到版面结构中"""
        print("5️⃣ 内容填充...")

        # 执行智能融合
        fusion_result = self._intelligent_fusion(layout_result)

        print(f"   ✅ 内容填充完成")
        return fusion_result

    def _intelligent_fusion(self, layout_result: Dict) -> Dict:
        """智能融合：版面分析结果 + DXF解析内容"""
        print("   🔄 执行智能融合...")

        # 1. 建立精确的坐标映射关系
        coordinate_mapping = self._build_precise_coordinate_mapping(layout_result)

        # 2. 按版面区域分配实体
        entity_allocation = self._allocate_entities_to_regions(layout_result, coordinate_mapping)

        # 3. 构建最终结果
        fusion_result = self._build_fusion_result(layout_result, entity_allocation, coordinate_mapping)

        print(f"   ✅ 智能融合完成")
        print(f"      坐标映射: {coordinate_mapping.get('mapping_info', '未知')}")
        print(f"      实体分配: {sum(len(regions) for regions in entity_allocation.values())}个区域")

        return fusion_result

    def _build_precise_coordinate_mapping(self, layout_result: Dict) -> Dict:
        """建立精确的坐标映射关系"""
        print("     📐 建立精确坐标映射...")

        if not self.texts:
            return {"error": "无文本数据"}

        # DXF坐标范围
        dxf_bounds = layout_result["coordinate_bounds"]
        dxf_width = dxf_bounds["x_max"] - dxf_bounds["x_min"]
        dxf_height = dxf_bounds["y_max"] - dxf_bounds["y_min"]

        # 版面坐标范围（基于DXF坐标）
        layout_bounds = dxf_bounds.copy()

        mapping = {
            "dxf_bounds": dxf_bounds,
            "layout_bounds": layout_bounds,
            "scale_x": 1.0,  # 直接使用DXF坐标，无需缩放
            "scale_y": 1.0,
            "offset_x": 0.0,
            "offset_y": 0.0,
            "mapping_info": f"DXF坐标直接映射 ({dxf_width:.0f}x{dxf_height:.0f})"
        }

        print(f"        ✅ 坐标映射: {mapping['mapping_info']}")
        return mapping

    def _allocate_entities_to_regions(self, layout_result: Dict, coordinate_mapping: Dict) -> Dict:
        """按版面区域分配实体"""
        print("     🎯 按区域分配实体...")

        allocation = {}

        for page in layout_result["pages"]:
            page_key = f"page_{page['page_number']}"
            allocation[page_key] = {
                "page_info": page,
                "page_entities": [],
                "main_drawing": {
                    "region_info": page["main_drawing"],
                    "entities": [],
                    "texts": []
                },
                "tables": []
            }

            # 分配页面实体
            page_entities = self._get_entities_in_region(page["bounds"], coordinate_mapping)
            allocation[page_key]["page_entities"] = page_entities

            # 分配主图实体
            main_entities = self._get_entities_in_region(page["main_drawing"], coordinate_mapping)
            main_texts = [e for e in main_entities if e["type"] in ["TEXT", "MTEXT"]]
            allocation[page_key]["main_drawing"]["entities"] = main_entities
            allocation[page_key]["main_drawing"]["texts"] = main_texts

            # 分配表格实体
            for i, table in enumerate(page["tables"]):
                table_entities = self._get_entities_in_region(table, coordinate_mapping)
                table_texts = [e for e in table_entities if e["type"] in ["TEXT", "MTEXT"]]

                # 调试信息
                print(f"        表格{i+1}({table['name']}): 坐标范围 X({table['x_min']:.0f}~{table['x_max']:.0f}), Y({table['y_min']:.0f}~{table['y_max']:.0f})")
                print(f"        找到 {len(table_entities)} 个实体, {len(table_texts)} 个文本")

                # 如果表格为空，扩大搜索范围
                if len(table_entities) == 0:
                    expanded_table = self._expand_region(table, 0.1)  # 扩大10%
                    table_entities = self._get_entities_in_region(expanded_table, coordinate_mapping)
                    table_texts = [e for e in table_entities if e["type"] in ["TEXT", "MTEXT"]]
                    print(f"        扩大搜索后: {len(table_entities)} 个实体, {len(table_texts)} 个文本")

                table_allocation = {
                    "region_info": table,
                    "entities": table_entities,
                    "texts": table_texts,
                    "text_content": self._join_texts(table_texts)
                }
                allocation[page_key]["tables"].append(table_allocation)

        total_allocated = sum(len(page_data["page_entities"]) for page_data in allocation.values())
        print(f"        ✅ 实体分配完成: {total_allocated}/{len(self.entities)}个实体")

        return allocation

    def _get_entities_in_region(self, region: Dict, coordinate_mapping: Dict) -> List[Dict]:
        """获取指定区域内的实体"""
        entities_in_region = []

        for entity in self.entities:
            if self._is_entity_in_region_precise(entity, region, coordinate_mapping):
                entities_in_region.append(entity)

        return entities_in_region

    def _is_entity_in_region_precise(self, entity: Dict, region: Dict, coordinate_mapping: Dict) -> bool:
        """精确判断实体是否在区域内"""
        try:
            # 获取实体坐标
            entity_coords = self._get_entity_coordinates(entity)
            if not entity_coords:
                return False

            # 检查每个坐标点是否在区域内
            for x, y in entity_coords:
                if (region["x_min"] <= x <= region["x_max"] and
                    region["y_min"] <= y <= region["y_max"]):
                    return True

            return False

        except Exception:
            return False

    def _get_entity_coordinates(self, entity: Dict) -> List[tuple]:
        """获取实体的所有坐标点"""
        coords = []

        try:
            if entity["type"] in ["TEXT", "MTEXT"]:
                coords.append((entity["x"], entity["y"]))
            elif entity["type"] == "LINE":
                coords.append((entity["start_x"], entity["start_y"]))
                coords.append((entity["end_x"], entity["end_y"]))
            elif entity["type"] == "CIRCLE":
                coords.append((entity["center_x"], entity["center_y"]))
            # 可以添加更多实体类型的坐标提取
        except KeyError:
            pass

        return coords

    def _enhance_entity_with_text_content(self, entity: Dict) -> Dict:
        """增强实体信息，添加文本内容显示"""
        enhanced_entity = entity.copy()

        # 为TEXT和MTEXT实体添加文本内容显示
        if entity["type"] in ["TEXT", "MTEXT"]:
            text_content = entity.get("text", "").strip()
            if text_content:
                enhanced_entity["显示文本"] = text_content
            else:
                enhanced_entity["显示文本"] = "[空文本]"

        # 为其他实体添加坐标信息显示
        elif entity["type"] == "LINE":
            enhanced_entity["坐标信息"] = f"起点({entity.get('start_x', 0):.0f},{entity.get('start_y', 0):.0f}) -> 终点({entity.get('end_x', 0):.0f},{entity.get('end_y', 0):.0f})"
        elif entity["type"] == "CIRCLE":
            enhanced_entity["坐标信息"] = f"圆心({entity.get('center_x', 0):.0f},{entity.get('center_y', 0):.0f}) 半径{entity.get('radius', 0):.0f}"

        return enhanced_entity

    def _expand_region(self, region: Dict, factor: float) -> Dict:
        """扩大区域范围"""
        width = region["x_max"] - region["x_min"]
        height = region["y_max"] - region["y_min"]

        expand_x = width * factor
        expand_y = height * factor

        return {
            "x_min": region["x_min"] - expand_x,
            "x_max": region["x_max"] + expand_x,
            "y_min": region["y_min"] - expand_y,
            "y_max": region["y_max"] + expand_y,
            "name": region.get("name", "扩展区域")
        }

    def _build_fusion_result(self, layout_result: Dict, entity_allocation: Dict, coordinate_mapping: Dict) -> Dict:
        """构建融合结果"""
        print("     🏗️  构建融合结果...")

        result = {
            "文件元数据": {
                "文件名": os.path.basename(self.dxf_path),
                "解析方法": "DXF智能解析V5 - 智能融合版",
                "总实体数": len(self.entities),
                "总文本数": len(self.texts),
                "坐标映射": coordinate_mapping["mapping_info"]
            },
            "版面分析": {
                "总页面数": layout_result["total_pages"],
                "坐标范围": layout_result["coordinate_bounds"],
                "版面详情": []
            },
            "图纸结构": {
                "图纸数量": layout_result["total_pages"],
                "图纸列表": []
            },
            "融合统计": {
                "版面区域数": len(entity_allocation),
                "实体分配率": 0.0,
                "文本分配率": 0.0
            }
        }

        # 构建每个页面的详细结果
        total_allocated_entities = 0
        total_allocated_texts = 0

        for page_key, page_data in entity_allocation.items():
            page_info = page_data["page_info"]

            # 版面分析详情
            layout_detail = {
                "页面编号": page_info["page_number"],
                "页面边界": page_info["bounds"],
                "主图区域": page_info["main_drawing"],
                "表格区域": [table["region_info"] for table in page_data["tables"]],
                "实体统计": {
                    "页面总实体": len(page_data["page_entities"]),
                    "主图实体": len(page_data["main_drawing"]["entities"]),
                    "表格实体": sum(len(table["entities"]) for table in page_data["tables"])
                }
            }
            result["版面分析"]["版面详情"].append(layout_detail)

            # 图纸结构详情
            drawing_detail = {
                "图纸名称": f"图纸页面{page_info['page_number']}",
                "页面边界": page_info["bounds"],
                "主图": {
                    "区域边界": page_info["main_drawing"],
                    "实体列表": [self._enhance_entity_with_text_content(e) for e in page_data["main_drawing"]["entities"]],
                    "文本内容": self._join_texts(page_data["main_drawing"]["texts"]),
                    "实体统计": self._count_entities_by_type(page_data["main_drawing"]["entities"])
                },
                "表格区域": []
            }

            # 添加表格详情
            for i, table_data in enumerate(page_data["tables"]):
                table_detail = {
                    "表格名称": table_data["region_info"]["name"],
                    "区域边界": table_data["region_info"],
                    "实体列表": [self._enhance_entity_with_text_content(e) for e in table_data["entities"]],
                    "文本内容": table_data["text_content"],
                    "实体统计": self._count_entities_by_type(table_data["entities"])
                }
                drawing_detail["表格区域"].append(table_detail)

            result["图纸结构"]["图纸列表"].append(drawing_detail)

            # 统计
            total_allocated_entities += len(page_data["page_entities"])
            total_allocated_texts += len([e for e in page_data["page_entities"] if e["type"] in ["TEXT", "MTEXT"]])

        # 更新融合统计
        result["融合统计"]["实体分配率"] = total_allocated_entities / len(self.entities) if self.entities else 0
        result["融合统计"]["文本分配率"] = total_allocated_texts / len(self.texts) if self.texts else 0

        print(f"        ✅ 融合结果构建完成")
        print(f"           实体分配率: {result['融合统计']['实体分配率']:.1%}")
        print(f"           文本分配率: {result['融合统计']['文本分配率']:.1%}")

        return result

    def _count_entities_by_type(self, entities: List[Dict]) -> Dict:
        """统计实体类型"""
        counts = {}
        for entity in entities:
            entity_type = entity.get("type", "UNKNOWN")
            counts[entity_type] = counts.get(entity_type, 0) + 1
        return counts



    def _join_texts(self, texts: List[Dict]) -> str:
        """将文本列表连接成字符串"""
        if not texts:
            return ""

        # 按Y坐标排序（从上到下）
        sorted_texts = sorted(texts, key=lambda t: -t["y"])

        # 提取文本内容并清理
        text_contents = []
        for text in sorted_texts:
            content = text.get("text", "").strip()
            if content:
                # 清理MTEXT格式代码
                content = re.sub(r'\\[A-Za-z][0-9]*;?', '', content)
                content = re.sub(r'\\[{}]', '', content)
                content = re.sub(r'\\P', ' ', content)
                content = re.sub(r'\s+', ' ', content).strip()
                if content:
                    text_contents.append(content)

        # 用特殊分隔符连接
        return " | ".join(text_contents)


def main():
    """主函数"""
    dxf_path = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10.dxf"

    if not os.path.exists(dxf_path):
        print(f"❌ 文件不存在: {dxf_path}")
        return

    # 创建解析器并执行解析
    parser = DXFParserV5(dxf_path)
    result = parser.parse()

    # 保存结果到输出目录
    output_file = os.path.join(parser.output_dir, "analysis_result.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2, default=str)

    # 显示结果统计
    if "错误" not in result:
        file_size = os.path.getsize(output_file) / 1024
        print(f"\n📊 解析结果:")
        print(f"   输出目录: {parser.output_dir}")
        print(f"   分析结果: {os.path.basename(output_file)} ({file_size:.1f}KB)")

        # 显示生成的文件
        print(f"\n📁 生成的文件:")
        for file_name in os.listdir(parser.output_dir):
            file_path = os.path.join(parser.output_dir, file_name)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path) / 1024
                print(f"   - {file_name}: {size:.1f}KB")

        print(f"\n📈 数据统计:")
        print(f"   图纸数量: {result['图纸结构']['图纸数量']}")
        print(f"   总实体数: {result['文件元数据']['总实体数']}")
        print(f"   总文本数: {result['文件元数据']['总文本数']}")

        if file_size > 100:
            print(f"   ✅ 数据丰富度: 良好")
        else:
            print(f"   ⚠️  数据丰富度: 较低")

        print(f"\n🎯 关键文件:")
        if parser.pdf_path:
            print(f"   📄 高质量PDF: {os.path.basename(parser.pdf_path)}")
        if parser.layout_image_path:
            print(f"   🎨 版面可视化: {os.path.basename(parser.layout_image_path)}")

    else:
        print(f"\n❌ 解析失败: {result['错误']}")


if __name__ == "__main__":
    main()
