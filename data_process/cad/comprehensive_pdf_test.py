#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合PDF转换测试脚本
对比不同渲染方法的效果，帮助用户选择最佳方案
"""

import os
import sys
import glob
import time
import shutil
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_all_rendering_methods(dxf_path: str, output_dir: str):
    """测试所有可用的渲染方法"""
    print(f"\n🔬 综合渲染测试: {os.path.basename(dxf_path)}")
    print("=" * 60)
    
    os.makedirs(output_dir, exist_ok=True)
    base_name = os.path.splitext(os.path.basename(dxf_path))[0]
    
    results = {}
    
    # 1. 测试高级渲染器 - ezdxf drawing
    print("\n1️⃣  测试高级渲染器 (ezdxf drawing)")
    try:
        from advanced_dxf_renderer import AdvancedDXFRenderer
        renderer = AdvancedDXFRenderer()
        
        output_pdf = os.path.join(output_dir, f"{base_name}_ezdxf_drawing.pdf")
        start_time = time.time()
        
        success = renderer.render_with_ezdxf_drawing(dxf_path, output_pdf)
        end_time = time.time()
        
        if success and os.path.exists(output_pdf):
            file_size = os.path.getsize(output_pdf)
            results['ezdxf_drawing'] = {
                'success': True,
                'file': output_pdf,
                'size': file_size,
                'time': end_time - start_time
            }
            print(f"   ✅ 成功 - 大小: {file_size/1024:.1f}KB, 耗时: {end_time-start_time:.2f}s")
        else:
            results['ezdxf_drawing'] = {'success': False}
            print(f"   ❌ 失败")
            
    except Exception as e:
        results['ezdxf_drawing'] = {'success': False, 'error': str(e)}
        print(f"   ❌ 异常: {e}")
    
    # 2. 测试高级渲染器 - 自定义matplotlib
    print("\n2️⃣  测试高级渲染器 (自定义matplotlib)")
    try:
        from advanced_dxf_renderer import AdvancedDXFRenderer
        renderer = AdvancedDXFRenderer()
        
        output_pdf = os.path.join(output_dir, f"{base_name}_custom_matplotlib.pdf")
        start_time = time.time()
        
        success = renderer.render_with_custom_matplotlib(dxf_path, output_pdf)
        end_time = time.time()
        
        if success and os.path.exists(output_pdf):
            file_size = os.path.getsize(output_pdf)
            results['custom_matplotlib'] = {
                'success': True,
                'file': output_pdf,
                'size': file_size,
                'time': end_time - start_time
            }
            print(f"   ✅ 成功 - 大小: {file_size/1024:.1f}KB, 耗时: {end_time-start_time:.2f}s")
        else:
            results['custom_matplotlib'] = {'success': False}
            print(f"   ❌ 失败")
            
    except Exception as e:
        results['custom_matplotlib'] = {'success': False, 'error': str(e)}
        print(f"   ❌ 异常: {e}")
    
    # 3. 测试V4原始渲染器
    print("\n3️⃣  测试V4原始渲染器")
    try:
        from dxf_parser_structured_v4 import DXFLayoutAnalyzer
        analyzer = DXFLayoutAnalyzer()
        
        output_pdf = os.path.join(output_dir, f"{base_name}_v4_original.pdf")
        start_time = time.time()
        
        # 强制使用基础渲染
        result_pdf = analyzer.dxf_to_pdf(dxf_path, output_pdf, use_advanced=False)
        end_time = time.time()
        
        if result_pdf and os.path.exists(result_pdf):
            file_size = os.path.getsize(result_pdf)
            results['v4_original'] = {
                'success': True,
                'file': result_pdf,
                'size': file_size,
                'time': end_time - start_time
            }
            print(f"   ✅ 成功 - 大小: {file_size/1024:.1f}KB, 耗时: {end_time-start_time:.2f}s")
        else:
            results['v4_original'] = {'success': False}
            print(f"   ❌ 失败")
            
    except Exception as e:
        results['v4_original'] = {'success': False, 'error': str(e)}
        print(f"   ❌ 异常: {e}")
    
    # 4. 测试V4优化渲染器
    print("\n4️⃣  测试V4优化渲染器")
    try:
        from dxf_parser_structured_v4 import DXFLayoutAnalyzer
        analyzer = DXFLayoutAnalyzer()
        
        output_pdf = os.path.join(output_dir, f"{base_name}_v4_optimized.pdf")
        start_time = time.time()
        
        # 使用高级渲染
        result_pdf = analyzer.dxf_to_pdf(dxf_path, output_pdf, use_advanced=True)
        end_time = time.time()
        
        if result_pdf and os.path.exists(result_pdf):
            file_size = os.path.getsize(result_pdf)
            results['v4_optimized'] = {
                'success': True,
                'file': result_pdf,
                'size': file_size,
                'time': end_time - start_time
            }
            print(f"   ✅ 成功 - 大小: {file_size/1024:.1f}KB, 耗时: {end_time-start_time:.2f}s")
        else:
            results['v4_optimized'] = {'success': False}
            print(f"   ❌ 失败")
            
    except Exception as e:
        results['v4_optimized'] = {'success': False, 'error': str(e)}
        print(f"   ❌ 异常: {e}")
    
    # 生成对比报告
    print("\n📊 渲染结果对比")
    print("-" * 60)
    print(f"{'方法':<20} {'状态':<8} {'大小(KB)':<12} {'耗时(s)':<10}")
    print("-" * 60)
    
    for method, result in results.items():
        if result['success']:
            size_kb = result['size'] / 1024
            time_s = result['time']
            print(f"{method:<20} {'✅成功':<8} {size_kb:<12.1f} {time_s:<10.2f}")
        else:
            print(f"{method:<20} {'❌失败':<8} {'-':<12} {'-':<10}")
    
    # 推荐最佳方法
    successful_methods = [(k, v) for k, v in results.items() if v['success']]
    if successful_methods:
        # 按文件大小排序（通常更大的文件包含更多细节）
        best_method = max(successful_methods, key=lambda x: x[1]['size'])
        print(f"\n🏆 推荐方法: {best_method[0]}")
        print(f"   文件: {os.path.basename(best_method[1]['file'])}")
    else:
        print(f"\n❌ 所有渲染方法都失败了")
    
    return results

def batch_comparison_test(input_dir: str, output_dir: str, max_files: int = 5):
    """批量对比测试"""
    print(f"\n🚀 批量渲染对比测试")
    print(f"📁 输入目录: {input_dir}")
    print(f"📁 输出目录: {output_dir}")
    print(f"📊 最大测试文件数: {max_files}")
    
    # 查找DXF文件
    dxf_files = glob.glob(os.path.join(input_dir, "*.dxf"))
    if not dxf_files:
        print(f"❌ 在 {input_dir} 中未找到DXF文件")
        return
    
    # 限制测试文件数量
    test_files = dxf_files[:max_files]
    print(f"📊 将测试 {len(test_files)} 个文件")
    
    os.makedirs(output_dir, exist_ok=True)
    
    all_results = {}
    
    for i, dxf_file in enumerate(test_files, 1):
        print(f"\n{'='*80}")
        print(f"测试文件 {i}/{len(test_files)}: {os.path.basename(dxf_file)}")
        print(f"{'='*80}")
        
        file_output_dir = os.path.join(output_dir, f"test_{i:02d}_{os.path.splitext(os.path.basename(dxf_file))[0]}")
        results = test_all_rendering_methods(dxf_file, file_output_dir)
        all_results[os.path.basename(dxf_file)] = results
    
    # 生成总结报告
    print(f"\n{'='*80}")
    print(f"📈 批量测试总结报告")
    print(f"{'='*80}")
    
    method_success_count = {}
    method_total_time = {}
    method_avg_size = {}
    
    for file_name, file_results in all_results.items():
        for method, result in file_results.items():
            if method not in method_success_count:
                method_success_count[method] = 0
                method_total_time[method] = 0
                method_avg_size[method] = []
            
            if result['success']:
                method_success_count[method] += 1
                method_total_time[method] += result['time']
                method_avg_size[method].append(result['size'])
    
    print(f"\n📊 各方法成功率统计:")
    print(f"{'方法':<20} {'成功率':<10} {'平均耗时':<12} {'平均大小':<12}")
    print("-" * 60)
    
    for method in method_success_count:
        success_rate = method_success_count[method] / len(test_files) * 100
        avg_time = method_total_time[method] / max(1, method_success_count[method])
        avg_size = sum(method_avg_size[method]) / max(1, len(method_avg_size[method])) / 1024
        
        print(f"{method:<20} {success_rate:<10.1f}% {avg_time:<12.2f}s {avg_size:<12.1f}KB")
    
    # 推荐最佳方法
    best_method = max(method_success_count.items(), key=lambda x: x[1])
    print(f"\n🏆 推荐使用: {best_method[0]} (成功率: {best_method[1]/len(test_files)*100:.1f}%)")

def interactive_test():
    """交互式测试"""
    print(f"\n🎮 综合PDF转换测试")
    print("请选择测试模式:")
    print("1. 单文件全方法对比")
    print("2. 批量文件对比测试")
    print("3. 使用默认测试文件")
    print("0. 退出")
    
    choice = input("\n请输入选择 (0-3): ").strip()
    
    if choice == '1':
        dxf_path = input("请输入DXF文件路径: ").strip()
        if os.path.exists(dxf_path):
            output_dir = input("请输入输出目录 (回车使用默认): ").strip()
            output_dir = output_dir if output_dir else "./pdf_comparison_output"
            test_all_rendering_methods(dxf_path, output_dir)
        else:
            print("❌ 文件不存在")
    
    elif choice == '2':
        input_dir = input("请输入DXF文件目录: ").strip()
        if os.path.exists(input_dir):
            output_dir = input("请输入输出目录 (回车使用默认): ").strip()
            output_dir = output_dir if output_dir else "./batch_comparison_output"
            max_files = input("请输入最大测试文件数 (回车使用默认5): ").strip()
            max_files = int(max_files) if max_files.isdigit() else 5
            batch_comparison_test(input_dir, output_dir, max_files)
        else:
            print("❌ 目录不存在")
    
    elif choice == '3':
        # 使用默认测试目录
        default_dir = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸"
        if os.path.exists(default_dir):
            output_dir = "./comprehensive_test_output"
            print(f"使用默认测试目录: {default_dir}")
            batch_comparison_test(default_dir, output_dir, 3)
        else:
            print("❌ 默认测试目录不存在")
    
    elif choice == '0':
        print("👋 退出测试")
        return
    
    else:
        print("❌ 无效选择")

def main():
    """主函数"""
    print("🚀 综合PDF转换测试工具")
    print("=" * 50)
    
    # 检查依赖
    missing_deps = []
    try:
        import matplotlib
    except ImportError:
        missing_deps.append('matplotlib')
    
    try:
        import ezdxf
    except ImportError:
        missing_deps.append('ezdxf')
    
    if missing_deps:
        print(f"❌ 缺少依赖: {', '.join(missing_deps)}")
        print("请安装: pip install matplotlib ezdxf")
        return
    
    print("✅ 基础依赖检查通过")
    
    if len(sys.argv) > 1:
        # 命令行模式
        if sys.argv[1] == 'batch' and len(sys.argv) > 2:
            input_dir = sys.argv[2]
            output_dir = sys.argv[3] if len(sys.argv) > 3 else "./batch_comparison_output"
            max_files = int(sys.argv[4]) if len(sys.argv) > 4 else 5
            batch_comparison_test(input_dir, output_dir, max_files)
        elif len(sys.argv) > 1:
            dxf_path = sys.argv[1]
            output_dir = sys.argv[2] if len(sys.argv) > 2 else "./pdf_comparison_output"
            test_all_rendering_methods(dxf_path, output_dir)
    else:
        # 交互式模式
        interactive_test()

if __name__ == "__main__":
    main()
