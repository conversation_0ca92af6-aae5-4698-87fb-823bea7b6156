import ezdxf
import json
import re
from collections import defaultdict
from typing import Dict, List, Any, Optional


def clean_text_content(text: str) -> str:
    """
    清理文本内容，移除格式化代码和特殊字符
    """
    if not text:
        return ""
    
    # 移除 MTEXT 格式化代码
    text = re.sub(r'\\[A-Za-z][0-9]*;?', '', text)
    text = re.sub(r'\\[{}]', '', text)
    text = re.sub(r'\\P', '\n', text)  # 段落分隔符
    text = re.sub(r'\\X', '', text)    # 扩展字符
    
    # 清理多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    
    return text


def extract_comprehensive_text(entity) -> Dict[str, Any]:
    """
    全面提取实体的文本内容和相关信息
    """
    entity_type = entity.dxftype()
    result = {
        "实体类型": entity_type,
        "图层": entity.dxf.layer,
        "文本内容": "",
        "坐标": [],
        "属性": {}
    }
    
    try:
        if entity_type == 'TEXT':
            result["文本内容"] = clean_text_content(entity.dxf.text)
            result["坐标"] = list(entity.dxf.insert)
            result["属性"] = {
                "高度": entity.dxf.height,
                "旋转角度": entity.dxf.rotation,
                "对齐方式": getattr(entity.dxf, 'halign', 0)
            }
            
        elif entity_type == 'MTEXT':
            result["文本内容"] = clean_text_content(entity.plain_text())
            result["坐标"] = list(entity.dxf.insert)
            result["属性"] = {
                "高度": entity.dxf.char_height,
                "旋转角度": entity.dxf.rotation,
                "宽度": getattr(entity.dxf, 'width', 0),
                "行间距": getattr(entity.dxf, 'line_spacing_factor', 1.0)
            }
            
        elif entity_type == 'ATTRIB':
            result["文本内容"] = clean_text_content(entity.dxf.text)
            result["坐标"] = list(entity.dxf.insert)
            result["属性"] = {
                "标签": entity.dxf.tag,
                "高度": entity.dxf.height,
                "旋转角度": entity.dxf.rotation
            }
            
        elif entity_type == 'ATTDEF':
            result["文本内容"] = clean_text_content(entity.dxf.text)
            result["坐标"] = list(entity.dxf.insert)
            result["属性"] = {
                "标签": entity.dxf.tag,
                "提示": getattr(entity.dxf, 'prompt', ''),
                "默认值": getattr(entity.dxf, 'text', ''),
                "高度": entity.dxf.height
            }
            
        elif entity_type == 'INSERT':
            # 处理块插入及其属性
            result["坐标"] = list(entity.dxf.insert)
            result["属性"] = {
                "块名": entity.dxf.name,
                "缩放": [entity.dxf.xscale, entity.dxf.yscale, entity.dxf.zscale],
                "旋转角度": entity.dxf.rotation
            }
            
            # 提取块属性文本
            attrib_texts = []
            if hasattr(entity, 'attribs'):
                for attrib in entity.attribs:
                    if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                        clean_attrib_text = clean_text_content(attrib.dxf.text)
                        if clean_attrib_text:
                            attrib_texts.append(f"{attrib.dxf.tag}: {clean_attrib_text}")
            
            result["文本内容"] = "; ".join(attrib_texts)
            
        elif entity_type in ['DIMENSION', 'LEADER']:
            # 尺寸标注和引线可能包含文本
            dim_text = getattr(entity.dxf, 'text', '')
            if dim_text:
                result["文本内容"] = clean_text_content(dim_text)
                
    except Exception as e:
        result["错误"] = f"提取文本时出错: {e}"
    
    return result


def find_table_structures(text_entities: List[Dict]) -> List[Dict]:
    """
    尝试识别表格结构
    """
    tables = []
    
    # 按Y坐标分组（行）
    rows = defaultdict(list)
    for entity in text_entities:
        if entity["文本内容"] and entity["坐标"]:
            y_coord = round(entity["坐标"][1], 1)  # 四舍五入到小数点后1位
            rows[y_coord].append(entity)
    
    # 对每行按X坐标排序
    sorted_rows = []
    for y_coord in sorted(rows.keys(), reverse=True):  # 从上到下
        row = sorted(rows[y_coord], key=lambda x: x["坐标"][0])  # 从左到右
        sorted_rows.append((y_coord, row))
    
    # 尝试识别表格
    current_table = []
    for i, (y_coord, row) in enumerate(sorted_rows):
        if len(row) >= 2:  # 至少有2列才可能是表格
            current_table.append(row)
        else:
            if len(current_table) >= 2:  # 至少2行才是表格
                tables.append({
                    "表格索引": len(tables),
                    "行数": len(current_table),
                    "内容": extract_table_content(current_table)
                })
            current_table = []
    
    # 处理最后一个表格
    if len(current_table) >= 2:
        tables.append({
            "表格索引": len(tables),
            "行数": len(current_table),
            "内容": extract_table_content(current_table)
        })
    
    return tables


def extract_table_content(table_rows: List[List[Dict]]) -> Dict:
    """
    从表格行中提取键值对内容
    """
    table_content = {}
    
    for row in table_rows:
        if len(row) >= 2:
            # 尝试将第一列作为键，第二列作为值
            key = clean_text_content(row[0]["文本内容"])
            value = clean_text_content(row[1]["文本内容"])
            
            # 清理键名
            key = key.replace(":", "").replace("：", "").strip()
            
            if key and value:
                table_content[key] = value
    
    return table_content


def parse_dxf_enhanced(dxf_path: str, json_path: str):
    """
    增强版DXF解析器，专注于提取所有文本内容和表格结构
    """
    try:
        doc = ezdxf.readfile(dxf_path)
    except Exception as e:
        print(f"错误: 无法读取DXF文件: {e}")
        return
    
    msp = doc.modelspace()
    
    # 统计信息
    entity_stats = defaultdict(int)
    all_text_entities = []
    
    print("开始增强解析DXF文件...")
    
    # 遍历所有实体
    for entity in msp:
        entity_type = entity.dxftype()
        entity_stats[entity_type] += 1
        
        # 提取文本内容
        if entity_type in ['TEXT', 'MTEXT', 'ATTRIB', 'ATTDEF', 'INSERT', 'DIMENSION', 'LEADER']:
            text_info = extract_comprehensive_text(entity)
            if text_info["文本内容"]:  # 只保存有内容的文本
                all_text_entities.append(text_info)
    
    # 识别表格结构
    tables = find_table_structures(all_text_entities)
    
    # 按图层分组文本
    text_by_layer = defaultdict(list)
    for text_entity in all_text_entities:
        text_by_layer[text_entity["图层"]].append(text_entity["文本内容"])
    
    # 创建输出结构
    output = {
        "文件信息": {
            "文件路径": dxf_path,
            "DXF版本": doc.dxfversion,
            "实体统计": dict(entity_stats),
            "文本实体总数": len(all_text_entities)
        },
        "文本内容": {
            "所有文本": [entity["文本内容"] for entity in all_text_entities],
            "按图层分组": dict(text_by_layer)
        },
        "表格数据": tables,
        "详细文本实体": all_text_entities
    }
    
    # 保存结果
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(output, f, ensure_ascii=False, indent=2)
    
    # 打印统计信息
    print(f"\n=== 解析完成 ===")
    print(f"实体类型统计:")
    for entity_type, count in sorted(entity_stats.items()):
        print(f"  {entity_type}: {count}")
    
    print(f"\n文本统计:")
    print(f"  总文本实体数: {len(all_text_entities)}")
    print(f"  识别到的表格数: {len(tables)}")
    print(f"  图层数: {len(text_by_layer)}")
    
    print(f"\n结果已保存到: {json_path}")
    
    # 搜索特定内容
    search_terms = ["消防设备应急电源", "消防", "应急", "电源"]
    print(f"\n=== 搜索结果 ===")
    for term in search_terms:
        found_texts = [text for text in output["文本内容"]["所有文本"] if term in text]
        if found_texts:
            print(f"包含 '{term}' 的文本 ({len(found_texts)} 条):")
            for text in found_texts[:5]:  # 只显示前5条
                print(f"  - {text}")
            if len(found_texts) > 5:
                print(f"  ... 还有 {len(found_texts) - 5} 条")
        else:
            print(f"未找到包含 '{term}' 的文本")


if __name__ == '__main__':
    dxf_file = '/Users/<USER>/Downloads/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图.dxf'
    json_file = 'output_enhanced.json'
    parse_dxf_enhanced(dxf_file, json_file)
