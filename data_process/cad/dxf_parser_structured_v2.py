import ezdxf
import json
import re
from collections import defaultdict
from typing import Dict, List, Any, Optional, Tuple
from ezdxf import bbox as ezdxf_bbox


class DXFStructuredParser:
    """
    结构化DXF解析器 - 将图纸内容按逻辑结构组织
    """
    
    def __init__(self, dxf_path: str):
        self.dxf_path = dxf_path
        self.doc = None
        self.all_entities = []
        self.text_entities = []
        
    def load_document(self) -> bool:
        """加载DXF文档"""
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            return True
        except Exception as e:
            print(f"错误: 无法读取DXF文件: {e}")
            return False
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        # 移除MTEXT格式化代码
        text = re.sub(r'\\[A-Za-z][0-9]*;?', '', text)
        text = re.sub(r'\\[{}]', '', text)
        text = re.sub(r'\\P', '\n', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def extract_entity_text(self, entity) -> Optional[Dict]:
        """从实体中提取文本信息"""
        entity_type = entity.dxftype()
        text_content = ""
        
        try:
            if entity_type == 'TEXT':
                text_content = self.clean_text(entity.dxf.text)
                coords = list(entity.dxf.insert)
            elif entity_type == 'MTEXT':
                text_content = self.clean_text(entity.plain_text())
                coords = list(entity.dxf.insert)
            elif entity_type == 'ATTRIB':
                text_content = self.clean_text(entity.dxf.text)
                coords = list(entity.dxf.insert)
            elif entity_type == 'ATTDEF':
                text_content = self.clean_text(entity.dxf.text)
                coords = list(entity.dxf.insert)
            elif entity_type == 'INSERT':
                # 提取块属性
                attrib_texts = []
                if hasattr(entity, 'attribs'):
                    for attrib in entity.attribs:
                        if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                            clean_attrib = self.clean_text(attrib.dxf.text)
                            if clean_attrib:
                                attrib_texts.append(f"{attrib.dxf.tag}: {clean_attrib}")
                text_content = "; ".join(attrib_texts)
                coords = list(entity.dxf.insert)
            else:
                return None
                
            if text_content:
                return {
                    "实体类型": entity_type,
                    "图层": entity.dxf.layer,
                    "文本内容": text_content,
                    "坐标": coords,
                    "X": coords[0],
                    "Y": coords[1],
                    "属性": {
                        "高度": getattr(entity.dxf, 'height', 0),
                        "旋转": getattr(entity.dxf, 'rotation', 0)
                    }
                }
        except Exception as e:
            print(f"提取文本时出错 ({entity_type}): {e}")
        
        return None
    
    def collect_all_text_entities(self):
        """收集所有文本实体"""
        self.text_entities = []
        
        # 模型空间
        for entity in self.doc.modelspace():
            text_info = self.extract_entity_text(entity)
            if text_info:
                text_info["空间类型"] = "模型空间"
                self.text_entities.append(text_info)
        
        # 图纸空间
        for layout in self.doc.layouts:
            if layout.name != 'Model':
                for entity in layout:
                    text_info = self.extract_entity_text(entity)
                    if text_info:
                        text_info["空间类型"] = f"图纸空间-{layout.name}"
                        self.text_entities.append(text_info)
        
        # 块定义
        for block in self.doc.blocks:
            if not block.name.startswith('*'):
                for entity in block:
                    text_info = self.extract_entity_text(entity)
                    if text_info:
                        text_info["空间类型"] = f"块定义-{block.name}"
                        self.text_entities.append(text_info)
    
    def detect_drawing_sheets(self) -> List[Dict]:
        """检测图纸边框，识别多个图稿"""
        msp = self.doc.modelspace()
        frames = []
        
        try:
            # 查找可能的图框（大的闭合矩形）
            for entity in msp.query('LWPOLYLINE'):
                if entity.is_closed and len(entity) == 4:
                    try:
                        bbox = ezdxf_bbox.extents([entity])
                        if bbox and bbox.size.x > 50000 and bbox.size.y > 30000:  # 调整阈值
                            frames.append({
                                "图框实体": entity,
                                "包围盒": bbox,
                                "图层": entity.dxf.layer,
                                "左下角": list(bbox.extmin),
                                "右上角": list(bbox.extmax),
                                "宽度": bbox.size.x,
                                "高度": bbox.size.y
                            })
                    except:
                        continue
        except:
            pass
        
        # 如果没找到图框，根据文本分布创建虚拟图框
        if not frames:
            if self.text_entities:
                x_coords = [t["X"] for t in self.text_entities]
                y_coords = [t["Y"] for t in self.text_entities]
                
                # 按X坐标分组，识别可能的多个图稿
                x_min, x_max = min(x_coords), max(x_coords)
                x_mid = (x_min + x_max) / 2
                
                # 检查是否有明显的左右分布
                left_texts = [t for t in self.text_entities if t["X"] < x_mid]
                right_texts = [t for t in self.text_entities if t["X"] >= x_mid]
                
                if len(left_texts) > 10 and len(right_texts) > 10:
                    # 创建两个虚拟图框
                    left_x = [t["X"] for t in left_texts]
                    left_y = [t["Y"] for t in left_texts]
                    right_x = [t["X"] for t in right_texts]
                    right_y = [t["Y"] for t in right_texts]
                    
                    frames = [
                        {
                            "图框类型": "虚拟图框",
                            "图稿名称": "左侧图稿",
                            "左下角": [min(left_x) - 1000, min(left_y) - 1000],
                            "右上角": [max(left_x) + 1000, max(left_y) + 1000],
                            "宽度": max(left_x) - min(left_x) + 2000,
                            "高度": max(left_y) - min(left_y) + 2000
                        },
                        {
                            "图框类型": "虚拟图框",
                            "图稿名称": "右侧图稿",
                            "左下角": [min(right_x) - 1000, min(right_y) - 1000],
                            "右上角": [max(right_x) + 1000, max(right_y) + 1000],
                            "宽度": max(right_x) - min(right_x) + 2000,
                            "高度": max(right_y) - min(right_y) + 2000
                        }
                    ]
                else:
                    # 单个图稿
                    frames = [{
                        "图框类型": "虚拟图框",
                        "图稿名称": "主图稿",
                        "左下角": [min(x_coords) - 1000, min(y_coords) - 1000],
                        "右上角": [max(x_coords) + 1000, max(y_coords) + 1000],
                        "宽度": max(x_coords) - min(x_coords) + 2000,
                        "高度": max(y_coords) - min(y_coords) + 2000
                    }]
        
        return frames
    
    def classify_text_by_region(self, frame: Dict) -> Dict:
        """将文本按区域分类（标题栏、图纸内容、表格等）"""
        left_bottom = frame["左下角"]
        right_top = frame["右上角"]

        x_min = left_bottom[0] if isinstance(left_bottom, list) else left_bottom
        y_min = left_bottom[1] if isinstance(left_bottom, list) else left_bottom
        x_max = right_top[0] if isinstance(right_top, list) else right_top
        y_max = right_top[1] if isinstance(right_top, list) else right_top
        
        # 筛选在当前图框内的文本
        frame_texts = []
        for text in self.text_entities:
            if (x_min <= text["X"] <= x_max and y_min <= text["Y"] <= y_max):
                frame_texts.append(text)
        
        # 定义区域
        width = x_max - x_min
        height = y_max - y_min
        
        # 标题栏区域（通常在右下角）
        title_x_min = x_max - width * 0.4
        title_y_min = y_min
        title_y_max = y_min + height * 0.3
        
        # 图例区域（通常在左下角或右下角）
        legend_x_max = x_min + width * 0.4
        legend_y_min = y_min
        legend_y_max = y_min + height * 0.4
        
        # 分类文本
        title_texts = []
        legend_texts = []
        table_texts = []
        content_texts = []
        
        for text in frame_texts:
            x, y = text["X"], text["Y"]
            
            # 标题栏区域
            if (title_x_min <= x <= x_max and title_y_min <= y <= title_y_max):
                title_texts.append(text)
            # 图例区域
            elif (x_min <= x <= legend_x_max and legend_y_min <= y <= legend_y_max):
                legend_texts.append(text)
            # 检查是否为表格内容（包含特定关键词或格式）
            elif self.is_table_content(text["文本内容"]):
                table_texts.append(text)
            else:
                content_texts.append(text)
        
        return {
            "标题栏": self.organize_title_block(title_texts),
            "图例说明": self.organize_legend(legend_texts),
            "表格数据": self.organize_tables(table_texts),
            "图纸内容": self.organize_content(content_texts),
            "区域统计": {
                "标题栏文本数": len(title_texts),
                "图例文本数": len(legend_texts),
                "表格文本数": len(table_texts),
                "内容文本数": len(content_texts),
                "总文本数": len(frame_texts)
            }
        }
    
    def is_table_content(self, text: str) -> bool:
        """判断是否为表格内容"""
        table_keywords = [
            "序号", "图例", "设备名称", "型号", "规格", "数量", "备注",
            "消防设备应急电源", "应急照明", "火灾报警", "设备编号",
            "CAM", "CAC", "MJ", "FCUN", "FTBN", "#", "@"
        ]
        return any(keyword in text for keyword in table_keywords)
    
    def organize_title_block(self, texts: List[Dict]) -> Dict:
        """组织标题栏信息"""
        title_info = {
            "图纸名称": "",
            "项目名称": "",
            "设计单位": "",
            "图纸编号": "",
            "设计日期": "",
            "比例": "",
            "其他信息": []
        }
        
        for text in texts:
            content = text["文本内容"]
            # 简单的关键词匹配
            if "比例" in content or "比 例" in content:
                title_info["比例"] = content
            elif "图纸" in content and "编号" in content:
                title_info["图纸编号"] = content
            elif "设计" in content:
                title_info["设计单位"] = content
            elif "日期" in content or "20" in content and len(content) < 20:
                title_info["设计日期"] = content
            else:
                title_info["其他信息"].append(content)
        
        return title_info

    def organize_legend(self, texts: List[Dict]) -> Dict:
        """组织图例说明"""
        legend_info = {
            "设备图例": [],
            "线路图例": [],
            "符号说明": [],
            "其他说明": []
        }

        for text in texts:
            content = text["文本内容"]
            if any(keyword in content for keyword in ["设备", "CAM", "CAC", "MJ"]):
                legend_info["设备图例"].append(content)
            elif any(keyword in content for keyword in ["线", "电缆", "导线"]):
                legend_info["线路图例"].append(content)
            elif any(keyword in content for keyword in ["符号", "标识", "代号"]):
                legend_info["符号说明"].append(content)
            else:
                legend_info["其他说明"].append(content)

        return legend_info

    def organize_tables(self, texts: List[Dict]) -> List[Dict]:
        """组织表格数据"""
        if not texts:
            return []

        # 按Y坐标分组（行）
        rows = defaultdict(list)
        for text in texts:
            y_coord = round(text["Y"], 1)
            rows[y_coord].append(text)

        # 按Y坐标排序（从上到下）
        sorted_rows = []
        for y_coord in sorted(rows.keys(), reverse=True):
            row = sorted(rows[y_coord], key=lambda x: x["X"])  # 按X坐标排序
            sorted_rows.append(row)

        # 识别表格
        tables = []
        current_table = []

        for row in sorted_rows:
            if len(row) >= 2:  # 至少2列才可能是表格
                current_table.append(row)
            else:
                if len(current_table) >= 2:  # 至少2行才是表格
                    tables.append(self.extract_table_data(current_table))
                current_table = []

        # 处理最后一个表格
        if len(current_table) >= 2:
            tables.append(self.extract_table_data(current_table))

        return tables

    def extract_table_data(self, table_rows: List[List[Dict]]) -> Dict:
        """从表格行中提取结构化数据"""
        table_data = {
            "表格类型": "未知",
            "行数": len(table_rows),
            "列数": max(len(row) for row in table_rows) if table_rows else 0,
            "表头": [],
            "数据行": [],
            "键值对": {}
        }

        if not table_rows:
            return table_data

        # 第一行作为表头
        if table_rows:
            table_data["表头"] = [cell["文本内容"] for cell in table_rows[0]]

        # 其余行作为数据
        for row in table_rows[1:]:
            row_data = [cell["文本内容"] for cell in row]
            table_data["数据行"].append(row_data)

        # 尝试提取键值对（适用于设备表格）
        for row in table_rows:
            if len(row) >= 2:
                key = row[0]["文本内容"].strip()
                value = row[1]["文本内容"].strip()
                if key and value:
                    # 清理键名
                    key = key.replace(":", "").replace("：", "")
                    table_data["键值对"][key] = value

        # 判断表格类型
        all_text = " ".join([cell["文本内容"] for row in table_rows for cell in row])
        if "消防设备" in all_text or "应急电源" in all_text:
            table_data["表格类型"] = "消防设备表"
        elif "设备名称" in all_text and "图例" in all_text:
            table_data["表格类型"] = "设备图例表"
        elif "序号" in all_text:
            table_data["表格类型"] = "编号对照表"

        return table_data

    def organize_content(self, texts: List[Dict]) -> Dict:
        """组织图纸内容"""
        content_info = {
            "设备标识": [],
            "线路标识": [],
            "尺寸标注": [],
            "说明文字": [],
            "按图层分组": defaultdict(list)
        }

        for text in texts:
            content = text["文本内容"]
            layer = text["图层"]

            # 按图层分组
            content_info["按图层分组"][layer].append(content)

            # 按内容类型分类
            if any(keyword in content for keyword in ["#", "@", "CAM", "CAC", "MJ"]):
                content_info["设备标识"].append(content)
            elif any(keyword in content for keyword in ["线", "电缆", "导线", "FCUN", "FTBN"]):
                content_info["线路标识"].append(content)
            elif content.replace(".", "").replace("-", "").isdigit():
                content_info["尺寸标注"].append(content)
            else:
                content_info["说明文字"].append(content)

        # 转换defaultdict为普通dict
        content_info["按图层分组"] = dict(content_info["按图层分组"])

        return content_info

    def generate_structured_output(self) -> Dict:
        """生成结构化输出"""
        if not self.load_document():
            return {"错误": "无法加载DXF文件"}

        print("正在收集文本实体...")
        self.collect_all_text_entities()

        print("正在检测图纸边框...")
        drawing_sheets = self.detect_drawing_sheets()

        print(f"检测到 {len(drawing_sheets)} 个图稿")

        # 构建结构化输出
        structured_output = {
            "文件信息": {
                "文件路径": self.dxf_path,
                "DXF版本": self.doc.dxfversion,
                "图稿数量": len(drawing_sheets),
                "总文本实体数": len(self.text_entities)
            },
            "图稿列表": [],
            "全局统计": {
                "实体类型统计": defaultdict(int),
                "图层统计": defaultdict(int),
                "关键词搜索": {}
            }
        }

        # 统计信息
        for text in self.text_entities:
            structured_output["全局统计"]["实体类型统计"][text["实体类型"]] += 1
            structured_output["全局统计"]["图层统计"][text["图层"]] += 1

        # 处理每个图稿
        for i, frame in enumerate(drawing_sheets):
            print(f"正在处理第 {i+1} 个图稿...")

            regions = self.classify_text_by_region(frame)

            sheet_info = {
                "图稿索引": i,
                "图稿名称": frame.get("图稿名称", f"图稿-{i+1}"),
                "图框信息": {
                    "类型": frame.get("图框类型", "实体图框"),
                    "左下角坐标": frame["左下角"],
                    "右上角坐标": frame["右上角"],
                    "尺寸": {
                        "宽度": frame["宽度"],
                        "高度": frame["高度"]
                    }
                },
                "标题栏": regions["标题栏"],
                "图例说明": regions["图例说明"],
                "表格数据": regions["表格数据"],
                "图纸内容": regions["图纸内容"],
                "统计信息": regions["区域统计"]
            }

            structured_output["图稿列表"].append(sheet_info)

        # 关键词搜索
        search_keywords = ["消防设备应急电源", "消防", "应急", "电源", "设备"]
        for keyword in search_keywords:
            matches = [text["文本内容"] for text in self.text_entities if keyword in text["文本内容"]]
            structured_output["全局统计"]["关键词搜索"][keyword] = {
                "匹配数量": len(matches),
                "匹配内容": matches
            }

        # 转换defaultdict为普通dict
        structured_output["全局统计"]["实体类型统计"] = dict(structured_output["全局统计"]["实体类型统计"])
        structured_output["全局统计"]["图层统计"] = dict(structured_output["全局统计"]["图层统计"])

        return structured_output


def main():
    """主函数"""
    dxf_file = '/Users/<USER>/Downloads/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图.dxf'
    output_file = 'output_structured_v2.json'

    print("开始结构化解析DXF文件...")
    parser = DXFStructuredParser(dxf_file)
    result = parser.generate_structured_output()

    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    # 打印摘要
    print(f"\n=== 结构化解析完成 ===")
    if "错误" not in result:
        print(f"文件: {result['文件信息']['文件路径']}")
        print(f"图稿数量: {result['文件信息']['图稿数量']}")
        print(f"总文本数: {result['文件信息']['总文本实体数']}")

        print(f"\n图稿详情:")
        for sheet in result["图稿列表"]:
            print(f"  {sheet['图稿名称']}:")
            print(f"    标题栏文本: {sheet['统计信息']['标题栏文本数']}")
            print(f"    图例文本: {sheet['统计信息']['图例文本数']}")
            print(f"    表格文本: {sheet['统计信息']['表格文本数']}")
            print(f"    内容文本: {sheet['统计信息']['内容文本数']}")
            print(f"    表格数量: {len(sheet['表格数据'])}")

        print(f"\n关键词搜索结果:")
        for keyword, data in result["全局统计"]["关键词搜索"].items():
            print(f"  '{keyword}': {data['匹配数量']} 条")

    print(f"\n结果已保存到: {output_file}")


if __name__ == '__main__':
    main()
