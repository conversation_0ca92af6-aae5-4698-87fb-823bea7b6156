import ezdxf
import json
import re
import os
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any, Optional, Tuple, Union
from ezdxf import bbox as ezdxf_bbox
from tqdm import tqdm


def safe_coordinate_conversion(coord) -> List[float]:
    """
    安全地将坐标对象转换为列表，处理Vec3等特殊对象
    """
    if coord is None:
        return []

    try:
        # 如果已经是列表或元组，直接转换
        if isinstance(coord, (list, tuple)):
            return [float(x) for x in coord]

        # 如果是Vec3或类似对象，尝试访问其坐标属性
        if hasattr(coord, 'x') and hasattr(coord, 'y'):
            z = getattr(coord, 'z', 0.0)  # 如果没有z坐标，默认为0
            return [float(coord.x), float(coord.y), float(z)]

        # 如果对象有__iter__方法，尝试迭代
        if hasattr(coord, '__iter__'):
            return [float(x) for x in coord]

        # 如果是单个数值，返回包含该数值的列表
        return [float(coord)]

    except Exception as e:
        print(f"坐标转换警告: {e}, 原始值: {coord}")
        return []


def safe_json_conversion(obj) -> Any:
    """
    安全地将对象转换为JSON可序列化的格式
    """
    if obj is None:
        return None

    try:
        # 基本类型直接返回
        if isinstance(obj, (str, int, float, bool)):
            return obj

        # 列表和元组
        if isinstance(obj, (list, tuple)):
            return [safe_json_conversion(item) for item in obj]

        # 字典
        if isinstance(obj, dict):
            return {key: safe_json_conversion(value) for key, value in obj.items()}

        # Vec3或类似的坐标对象
        if hasattr(obj, 'x') and hasattr(obj, 'y'):
            z = getattr(obj, 'z', 0.0)
            return [float(obj.x), float(obj.y), float(z)]

        # 有__iter__方法的对象
        if hasattr(obj, '__iter__') and not isinstance(obj, str):
            return [safe_json_conversion(item) for item in obj]

        # 其他对象尝试转换为字符串
        return str(obj)

    except Exception as e:
        print(f"JSON转换警告: {e}, 原始值类型: {type(obj)}")
        return str(obj)


class DXFComprehensiveParser:
    """
    全面的DXF解析器 V3 - 更全面的CAD图纸格式信息解析
    支持坐标选项控制，保留所有内容而不精简
    """
    
    def __init__(self, dxf_path: str, include_coordinates: bool = True, include_raw_data: bool = True):
        """
        初始化解析器
        
        Args:
            dxf_path: DXF文件路径
            include_coordinates: 是否包含坐标信息
            include_raw_data: 是否包含原始数据
        """
        self.dxf_path = dxf_path
        self.include_coordinates = include_coordinates
        self.include_raw_data = include_raw_data
        self.doc = None
        self.all_entities = []
        self.text_entities = []
        self.geometric_entities = []
        self.dimension_entities = []
        self.block_entities = []
        
    def load_document(self) -> bool:
        """加载DXF文档"""
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            return True
        except Exception as e:
            print(f"错误: 无法读取DXF文件: {e}")
            return False
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        # 移除MTEXT格式化代码
        text = re.sub(r'\\[A-Za-z][0-9]*;?', '', text)
        text = re.sub(r'\\[{}]', '', text)
        text = re.sub(r'\\P', '\n', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def extract_comprehensive_entity_info(self, entity, space_name: str) -> Dict:
        """提取完整的实体信息"""
        entity_type = entity.dxftype()

        # 基础信息 - 参考output.json的格式
        entity_info = {
            "实体类型": entity_type,
            "所在图层": getattr(entity.dxf, 'layer', ''),
            "颜色索引": getattr(entity.dxf, 'color', 0),
        }

        # 添加更多通用属性
        if hasattr(entity.dxf, 'linetype'):
            entity_info["线型"] = entity.dxf.linetype
        if hasattr(entity.dxf, 'lineweight'):
            entity_info["线宽"] = entity.dxf.lineweight

        # 根据实体类型提取详细信息
        if entity_type in ['TEXT', 'MTEXT', 'ATTRIB', 'ATTDEF']:
            text_info = self._extract_text_info(entity)
            geo_info = self._extract_geometry_info(entity)

            if text_info.get("文本内容"):
                entity_info["文本信息"] = {
                    "插入点坐标": geo_info.get("插入点", []),
                    "文本内容": text_info["文本内容"],
                    "文字高度": text_info.get("文本高度", 0),
                    "旋转角度": text_info.get("旋转角度", 0)
                }

        elif entity_type == 'INSERT':
            block_info = self._extract_block_info(entity)
            geo_info = self._extract_geometry_info(entity)

            entity_info["块信息"] = {
                "块名": block_info.get("块名称", ""),
                "插入点坐标": geo_info.get("插入点", []),
                "缩放比例": [
                    block_info.get("缩放比例", {}).get("X", 1.0),
                    block_info.get("缩放比例", {}).get("Y", 1.0),
                    block_info.get("缩放比例", {}).get("Z", 1.0)
                ],
                "旋转角度": block_info.get("旋转角度", 0)
            }

            # 提取块属性文本
            if block_info.get("属性列表"):
                attr_texts = []
                for attr in block_info["属性列表"]:
                    if attr.get("值"):
                        attr_texts.append(f"{attr.get('标签', '')}: {attr['值']}")
                if attr_texts:
                    entity_info["块信息"]["属性文本"] = "; ".join(attr_texts)

        elif entity_type == 'LINE':
            geo_info = self._extract_geometric_info(entity)
            entity_info["几何信息"] = {
                "起点坐标": geo_info.get("起点", []),
                "终点坐标": geo_info.get("终点", [])
            }

        elif entity_type == 'CIRCLE':
            geo_info = self._extract_geometric_info(entity)
            entity_info["几何信息"] = {
                "圆心坐标": geo_info.get("圆心", []),
                "半径": geo_info.get("半径", 0)
            }

        elif entity_type == 'ARC':
            geo_info = self._extract_geometric_info(entity)
            entity_info["几何信息"] = {
                "圆心坐标": geo_info.get("圆心", []),
                "半径": geo_info.get("半径", 0),
                "起始角度": geo_info.get("起始角度", 0),
                "终止角度": geo_info.get("结束角度", 0)
            }

        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
            geo_info = self._extract_geometric_info(entity)
            # 压缩顶点坐标格式 - 每个坐标压缩为一行
            vertices = geo_info.get("顶点列表", [])
            compressed_vertices = []
            for vertex in vertices:
                if len(vertex) >= 2:
                    compressed_vertices.append(f"({vertex[0]:.2f}, {vertex[1]:.2f})")

            entity_info["几何信息"] = {
                "顶点坐标": compressed_vertices,
                "是否闭合": geo_info.get("是否闭合", False)
            }

        elif entity_type == 'DIMENSION':
            dim_info = self._extract_dimension_info(entity)
            entity_info["尺寸信息"] = {
                "尺寸文本": dim_info.get("标注文本", ""),
                "尺寸类型": getattr(entity, 'dimtype', 0) if hasattr(entity, 'dimtype') else 0
            }

        elif entity_type == 'HATCH':
            entity_info["填充信息"] = {
                "填充图案": getattr(entity.dxf, 'pattern_name', ''),
                "填充类型": getattr(entity.dxf, 'hatch_style', 0)
            }

        return entity_info
    
    def _extract_geometry_info(self, entity) -> Dict:
        """提取几何信息"""
        geometry_info = {}
        entity_type = entity.dxftype()

        try:
            if hasattr(entity.dxf, 'insert'):
                geometry_info["插入点"] = safe_coordinate_conversion(entity.dxf.insert)
            elif hasattr(entity.dxf, 'start') and hasattr(entity.dxf, 'end'):
                geometry_info["起点"] = safe_coordinate_conversion(entity.dxf.start)
                geometry_info["终点"] = safe_coordinate_conversion(entity.dxf.end)
            elif hasattr(entity.dxf, 'center'):
                geometry_info["中心点"] = safe_coordinate_conversion(entity.dxf.center)
                if hasattr(entity.dxf, 'radius'):
                    geometry_info["半径"] = entity.dxf.radius

            # 尝试获取包围盒 - 只有有效值才记录
            try:
                bbox = ezdxf_bbox.extents([entity])
                if bbox:
                    extmin = safe_coordinate_conversion(bbox.extmin)
                    extmax = safe_coordinate_conversion(bbox.extmax)

                    # 检查是否有有效的坐标值
                    if (extmin and extmax and
                        all(isinstance(x, (int, float)) and not (x == float('inf') or x != x) for x in extmin) and
                        all(isinstance(x, (int, float)) and not (x == float('inf') or x != x) for x in extmax)):

                        width = float(bbox.size.x)
                        height = float(bbox.size.y)
                        depth = float(bbox.size.z)

                        # 只有尺寸有效才记录
                        if not (width != width or height != height or depth != depth):  # 检查NaN
                            geometry_info["包围盒"] = {
                                "最小点": extmin,
                                "最大点": extmax,
                                "尺寸": {
                                    "宽度": width,
                                    "高度": height,
                                    "深度": depth
                                }
                            }
            except:
                pass

        except Exception as e:
            geometry_info["几何提取错误"] = str(e)

        return geometry_info
    
    def _extract_raw_attributes(self, entity) -> Dict:
        """提取原始DXF属性"""
        raw_attrs = {}
        try:
            # 获取所有DXF属性
            for attr_name in dir(entity.dxf):
                if not attr_name.startswith('_'):
                    try:
                        value = getattr(entity.dxf, attr_name)
                        if not callable(value):
                            raw_attrs[attr_name] = safe_json_conversion(value)
                    except:
                        pass
        except Exception as e:
            raw_attrs["属性提取错误"] = str(e)

        return raw_attrs
    
    def _extract_text_info(self, entity) -> Dict:
        """提取文本信息"""
        text_info = {}
        entity_type = entity.dxftype()
        
        try:
            if entity_type == 'TEXT':
                text_info["文本内容"] = self.clean_text(entity.dxf.text)
                text_info["文本高度"] = getattr(entity.dxf, 'height', 0)
                text_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)
                text_info["对齐方式"] = getattr(entity.dxf, 'halign', 0)
                text_info["字体样式"] = getattr(entity.dxf, 'style', '')
                
            elif entity_type == 'MTEXT':
                text_info["文本内容"] = self.clean_text(entity.plain_text())
                text_info["原始文本"] = entity.dxf.text if self.include_raw_data else ""
                text_info["字符高度"] = getattr(entity.dxf, 'char_height', 0)
                text_info["文本宽度"] = getattr(entity.dxf, 'width', 0)
                text_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)
                text_info["对齐方式"] = getattr(entity.dxf, 'attachment_point', 1)
                text_info["字体样式"] = getattr(entity.dxf, 'style', '')
                text_info["行间距"] = getattr(entity.dxf, 'line_spacing_factor', 1.0)
                
            elif entity_type in ['ATTRIB', 'ATTDEF']:
                text_info["文本内容"] = self.clean_text(entity.dxf.text)
                text_info["标签"] = getattr(entity.dxf, 'tag', '')
                text_info["提示"] = getattr(entity.dxf, 'prompt', '')
                text_info["默认值"] = getattr(entity.dxf, 'text', '')
                text_info["文本高度"] = getattr(entity.dxf, 'height', 0)
                text_info["是否可见"] = not getattr(entity.dxf, 'invisible', False)
                text_info["是否常量"] = getattr(entity.dxf, 'const', False)
                text_info["是否验证"] = getattr(entity.dxf, 'verify', False)
                
        except Exception as e:
            text_info["文本提取错误"] = str(e)
        
        return text_info
    
    def _extract_block_info(self, entity) -> Dict:
        """提取块信息"""
        block_info = {}
        
        try:
            block_info["块名称"] = getattr(entity.dxf, 'name', '')
            block_info["缩放比例"] = {
                "X": getattr(entity.dxf, 'xscale', 1.0),
                "Y": getattr(entity.dxf, 'yscale', 1.0),
                "Z": getattr(entity.dxf, 'zscale', 1.0)
            }
            block_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)
            
            # 提取块属性
            block_attributes = []
            if hasattr(entity, 'attribs'):
                for attrib in entity.attribs:
                    if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                        attr_info = {
                            "标签": getattr(attrib.dxf, 'tag', ''),
                            "值": self.clean_text(attrib.dxf.text),
                            "位置": safe_coordinate_conversion(attrib.dxf.insert) if self.include_coordinates else None
                        }
                        block_attributes.append(attr_info)
            
            block_info["属性列表"] = block_attributes
            block_info["属性数量"] = len(block_attributes)
            
            # 如果包含原始数据，添加块定义信息
            if self.include_raw_data and self.doc:
                try:
                    block_def = self.doc.blocks.get(entity.dxf.name)
                    if block_def:
                        block_info["块定义信息"] = {
                            "实体数量": len(block_def),
                            "基点": safe_coordinate_conversion(block_def.block.dxf.base_point) if self.include_coordinates else None
                        }
                except:
                    pass
                    
        except Exception as e:
            block_info["块信息提取错误"] = str(e)
        
        return block_info

    def _extract_geometric_info(self, entity) -> Dict:
        """提取几何实体信息"""
        geo_info = {}
        entity_type = entity.dxftype()

        try:
            if entity_type == 'LINE':
                geo_info["几何类型"] = "直线"
                if self.include_coordinates:
                    start_coords = safe_coordinate_conversion(entity.dxf.start)
                    end_coords = safe_coordinate_conversion(entity.dxf.end)
                    geo_info["起点"] = start_coords
                    geo_info["终点"] = end_coords
                    # 计算长度
                    if len(start_coords) >= 2 and len(end_coords) >= 2:
                        import math
                        dx = end_coords[0] - start_coords[0]
                        dy = end_coords[1] - start_coords[1]
                        geo_info["长度"] = math.sqrt(dx*dx + dy*dy)
                        geo_info["角度"] = math.atan2(dy, dx) * 180 / math.pi

            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                geo_info["几何类型"] = "多段线"
                geo_info["是否闭合"] = entity.is_closed
                geo_info["顶点数量"] = len(entity)

                if self.include_coordinates:
                    vertices = []
                    for vertex in entity:
                        if hasattr(vertex, 'dxf'):
                            vertices.append(safe_coordinate_conversion(vertex.dxf.location))
                        else:
                            vertices.append(safe_coordinate_conversion(vertex))
                    geo_info["顶点列表"] = vertices

                # 计算周长/长度
                try:
                    if hasattr(entity, 'virtual_entities'):
                        total_length = 0
                        for segment in entity.virtual_entities():
                            if hasattr(segment, 'length'):
                                total_length += segment.length()
                        geo_info["总长度"] = total_length
                except:
                    pass

            elif entity_type == 'CIRCLE':
                geo_info["几何类型"] = "圆"
                if self.include_coordinates:
                    geo_info["圆心"] = safe_coordinate_conversion(entity.dxf.center)
                geo_info["半径"] = entity.dxf.radius
                geo_info["直径"] = entity.dxf.radius * 2
                geo_info["周长"] = 2 * 3.14159 * entity.dxf.radius
                geo_info["面积"] = 3.14159 * entity.dxf.radius * entity.dxf.radius

            elif entity_type == 'ARC':
                geo_info["几何类型"] = "圆弧"
                if self.include_coordinates:
                    geo_info["圆心"] = safe_coordinate_conversion(entity.dxf.center)
                geo_info["半径"] = entity.dxf.radius
                geo_info["起始角度"] = entity.dxf.start_angle
                geo_info["结束角度"] = entity.dxf.end_angle
                # 计算弧长
                import math
                angle_diff = entity.dxf.end_angle - entity.dxf.start_angle
                if angle_diff < 0:
                    angle_diff += 360
                geo_info["弧长"] = entity.dxf.radius * math.radians(angle_diff)

            elif entity_type == 'ELLIPSE':
                geo_info["几何类型"] = "椭圆"
                if self.include_coordinates:
                    geo_info["中心点"] = safe_coordinate_conversion(entity.dxf.center)
                    geo_info["长轴向量"] = safe_coordinate_conversion(entity.dxf.major_axis)
                geo_info["短长轴比"] = entity.dxf.ratio

        except Exception as e:
            geo_info["几何信息提取错误"] = str(e)

        return geo_info

    def _extract_dimension_info(self, entity) -> Dict:
        """提取标注信息"""
        dim_info = {}
        entity_type = entity.dxftype()

        try:
            dim_info["标注类型"] = entity_type

            # 标注文本
            dim_text = getattr(entity.dxf, 'text', '')
            if dim_text:
                dim_info["标注文本"] = self.clean_text(dim_text)

            # 标注样式
            dim_info["标注样式"] = getattr(entity.dxf, 'dimstyle', '')

            if self.include_coordinates:
                # 标注位置
                if hasattr(entity.dxf, 'text_midpoint'):
                    dim_info["文本位置"] = safe_coordinate_conversion(entity.dxf.text_midpoint)

                # 根据标注类型提取特定信息
                if entity_type == 'DIMENSION':
                    if hasattr(entity.dxf, 'defpoint'):
                        dim_info["定义点"] = safe_coordinate_conversion(entity.dxf.defpoint)
                    if hasattr(entity.dxf, 'defpoint2'):
                        dim_info["定义点2"] = safe_coordinate_conversion(entity.dxf.defpoint2)
                    if hasattr(entity.dxf, 'defpoint3'):
                        dim_info["定义点3"] = safe_coordinate_conversion(entity.dxf.defpoint3)

                elif entity_type == 'LEADER':
                    # 引线信息
                    if hasattr(entity, 'vertices'):
                        dim_info["引线顶点"] = [safe_coordinate_conversion(v) for v in entity.vertices]

            # 标注值
            if hasattr(entity.dxf, 'actual_measurement'):
                dim_info["实际测量值"] = entity.dxf.actual_measurement

        except Exception as e:
            dim_info["标注信息提取错误"] = str(e)

        return dim_info



    def collect_all_entities(self):
        """收集所有实体"""
        self.all_entities = []
        self.text_entities = []
        self.geometric_entities = []
        self.dimension_entities = []
        self.block_entities = []

        # 模型空间
        for entity in self.doc.modelspace():
            entity_info = self.extract_comprehensive_entity_info(entity, "模型空间")
            self.all_entities.append(entity_info)
            self._categorize_entity(entity_info)

        # 图纸空间
        for layout in self.doc.layouts:
            if layout.name != 'Model':
                for entity in layout:
                    entity_info = self.extract_comprehensive_entity_info(entity, f"图纸空间-{layout.name}")
                    self.all_entities.append(entity_info)
                    self._categorize_entity(entity_info)

        # 块定义
        for block in self.doc.blocks:
            if not block.name.startswith('*'):
                for entity in block:
                    entity_info = self.extract_comprehensive_entity_info(entity, f"块定义-{block.name}")
                    self.all_entities.append(entity_info)
                    self._categorize_entity(entity_info)

    def _categorize_entity(self, entity_info: Dict):
        """将实体分类到不同列表中"""
        entity_type = entity_info["实体类型"]

        if entity_type in ['TEXT', 'MTEXT', 'ATTRIB', 'ATTDEF']:
            self.text_entities.append(entity_info)
        elif entity_type == 'INSERT':
            self.block_entities.append(entity_info)
        elif entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE', 'CIRCLE', 'ARC', 'ELLIPSE', 'SPLINE', 'HATCH']:
            self.geometric_entities.append(entity_info)
        elif entity_type in ['DIMENSION', 'LEADER']:
            self.dimension_entities.append(entity_info)

    def extract_document_metadata(self) -> Dict:
        """提取精简的文档元数据"""
        # 统计实体类型
        entity_stats = defaultdict(int)
        for entity in self.all_entities:
            entity_stats[entity["实体类型"]] += 1

        return {
            "文件名": os.path.basename(self.dxf_path),
            "DXF版本": self.doc.dxfversion,
            "实体统计": dict(entity_stats),
            "文本实体数量": len(self.text_entities)
        }





    def detect_drawing_sheets(self) -> List[Dict]:
        """检测多个图纸区域"""
        # 收集所有文本的坐标
        text_coords = []
        for entity in self.all_entities:
            coords = None
            # 从文本信息中获取坐标
            if entity.get("文本信息") and entity["文本信息"].get("插入点坐标"):
                coords = entity["文本信息"]["插入点坐标"]
            # 从块信息中获取坐标
            elif entity.get("块信息") and entity["块信息"].get("插入点坐标"):
                coords = entity["块信息"]["插入点坐标"]

            if coords and len(coords) >= 2:
                text_coords.append((coords[0], coords[1], entity))

        if not text_coords:
            return []

        # 按X坐标排序，检测是否有明显的左右分布
        text_coords.sort(key=lambda x: x[0])
        x_coords = [coord[0] for coord in text_coords]

        # 计算X坐标的分布
        x_min, x_max = min(x_coords), max(x_coords)
        x_range = x_max - x_min

        if x_range < 100000:  # 单个图纸
            return [{
                "图纸名称": "主图纸",
                "区域": "全图",
                "X范围": [x_min, x_max],
                "Y范围": [min(coord[1] for coord in text_coords), max(coord[1] for coord in text_coords)],
                "文本数量": len(text_coords)
            }]

        # 改进的多图纸检测算法
        # 使用K-means聚类方法检测图纸分布
        import numpy as np

        # 将坐标转换为numpy数组
        coords_array = np.array([(coord[0], coord[1]) for coord in text_coords])

        # 尝试检测2个聚类（左右图纸）
        sheets = []
        try:
            from sklearn.cluster import KMeans

            # 先尝试2个聚类
            kmeans = KMeans(n_clusters=2, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(coords_array)

            # 检查聚类结果的质量
            cluster_0_coords = coords_array[cluster_labels == 0]
            cluster_1_coords = coords_array[cluster_labels == 1]

            # 计算两个聚类中心的X坐标距离
            center_0_x = np.mean(cluster_0_coords[:, 0])
            center_1_x = np.mean(cluster_1_coords[:, 0])
            x_distance = abs(center_1_x - center_0_x)

            # 检查是否是双页图纸（通过PAGE信息判断）
            has_page_info = self._check_for_page_info()

            # 如果有PAGE信息，使用更严格的分割条件
            distance_threshold = 0.5 if has_page_info else 0.3

            # 如果两个聚类中心距离足够大，且每个聚类都有足够的点，则认为是两个图纸
            if x_distance > x_range * distance_threshold and len(cluster_0_coords) > 100 and len(cluster_1_coords) > 100:
                # 确定左右图纸
                if center_0_x < center_1_x:
                    left_coords, right_coords = cluster_0_coords, cluster_1_coords
                else:
                    left_coords, right_coords = cluster_1_coords, cluster_0_coords

                sheets.append({
                    "图纸名称": "图纸1",
                    "图纸类型": "主图纸",
                    "X范围": [float(np.min(left_coords[:, 0])), float(np.max(left_coords[:, 0]))],
                    "Y范围": [float(np.min(left_coords[:, 1])), float(np.max(left_coords[:, 1]))],
                    "文本数量": len(left_coords)
                })

                sheets.append({
                    "图纸名称": "图纸2",
                    "图纸类型": "主图纸",
                    "X范围": [float(np.min(right_coords[:, 0])), float(np.max(right_coords[:, 0]))],
                    "Y范围": [float(np.min(right_coords[:, 1])), float(np.max(right_coords[:, 1]))],
                    "文本数量": len(right_coords)
                })
            else:
                # 单个图纸
                sheets.append({
                    "图纸名称": "主图纸",
                    "区域": "全图",
                    "X范围": [x_min, x_max],
                    "Y范围": [min(coord[1] for coord in text_coords), max(coord[1] for coord in text_coords)],
                    "文本数量": len(text_coords)
                })

        except ImportError:
            # 如果没有sklearn，回退到简单的分割方法
            x_mid = (x_min + x_max) / 2
            left_texts = [coord for coord in text_coords if coord[0] < x_mid]
            right_texts = [coord for coord in text_coords if coord[0] >= x_mid]

            if len(left_texts) > 100 and len(right_texts) > 100:
                left_x = [coord[0] for coord in left_texts]
                left_y = [coord[1] for coord in left_texts]
                sheets.append({
                    "图纸名称": "图纸1",
                    "图纸类型": "主图纸",
                    "X范围": [min(left_x), max(left_x)],
                    "Y范围": [min(left_y), max(left_y)],
                    "文本数量": len(left_texts)
                })

                right_x = [coord[0] for coord in right_texts]
                right_y = [coord[1] for coord in right_texts]
                sheets.append({
                    "图纸名称": "图纸2",
                    "图纸类型": "主图纸",
                    "X范围": [min(right_x), max(right_x)],
                    "Y范围": [min(right_y), max(right_y)],
                    "文本数量": len(right_texts)
                })
            else:
                sheets.append({
                    "图纸名称": "图纸1",
                    "图纸类型": "主图纸",
                    "X范围": [x_min, x_max],
                    "Y范围": [min(coord[1] for coord in text_coords), max(coord[1] for coord in text_coords)],
                    "文本数量": len(text_coords)
                })

        return sheets if sheets else [{
            "图纸名称": "主图纸",
            "区域": "全图",
            "X范围": [x_min, x_max],
            "Y范围": [min(coord[1] for coord in text_coords), max(coord[1] for coord in text_coords)],
            "文本数量": len(text_coords)
        }]

    def _check_for_page_info(self) -> bool:
        """检查是否存在PAGE信息，表明这是多页图纸"""
        for entity in self.all_entities:
            text_content = ""
            if entity.get("文本信息"):
                text_content = entity["文本信息"].get("文本内容", "")
            elif entity.get("块信息"):
                text_content = entity["块信息"].get("属性文本", "")

            if "PAGE:" in text_content and "PAGE_COUNT:" in text_content:
                return True
        return False

    def detect_content_areas_for_sheets(self, drawing_sheets: List[Dict]) -> List[Dict]:
        """为每个图纸检测内容区域（主图、图例、图表等）"""

        # 为每个图纸初始化内容区域
        for sheet in drawing_sheets:
            sheet["主图"] = {
                "区域类型": "主图内容",
                "实体列表": [],
                "图层统计": defaultdict(int),
                "文本内容": defaultdict(list)
            }
            sheet["图例"] = {
                "区域类型": "图例说明",
                "实体列表": [],
                "图层统计": defaultdict(int),
                "文本内容": defaultdict(list)
            }
            sheet["图表"] = []  # 图表列表，可能有多个表格

        # 查找表格内的文本（网格状分布）
        text_entities_with_coords = []
        for entity in self.all_entities:
            coords = None
            text_content = None

            # 从文本信息中获取
            if entity.get("文本信息"):
                coords = entity["文本信息"].get("插入点坐标")
                text_content = entity["文本信息"].get("文本内容")
            # 从块信息中获取
            elif entity.get("块信息"):
                coords = entity["块信息"].get("插入点坐标")
                text_content = entity["块信息"].get("属性文本")

            if coords and text_content:
                text_entities_with_coords.append({
                    "坐标": coords,
                    "文本内容": text_content
                })

        if text_entities_with_coords:
            # 为每个图纸检测表格
            for sheet in drawing_sheets:
                sheet_x_range = sheet["X范围"]
                sheet_y_range = sheet["Y范围"]

                # 筛选属于当前图纸的文本实体
                sheet_text_entities = []
                for entity in text_entities_with_coords:
                    x, y = entity["坐标"][0], entity["坐标"][1]
                    if (sheet_x_range[0] <= x <= sheet_x_range[1] and
                        sheet_y_range[0] <= y <= sheet_y_range[1]):
                        sheet_text_entities.append(entity)

                # 按Y坐标分组（行）
                rows = defaultdict(list)
                for entity in sheet_text_entities:
                    y_coord = entity["坐标"][1]
                    # 将相近的Y坐标归为同一行
                    row_key = round(y_coord / 1000) * 1000
                    rows[row_key].append(entity)

                # 检测表格（降低阈值：至少2行，每行至少2列）
                potential_tables = []
                for row_y, row_entities in rows.items():
                    if len(row_entities) >= 2:
                        # 按X坐标排序
                        row_entities.sort(key=lambda x: x["坐标"][0])
                        potential_tables.append({
                            "行Y坐标": row_y,
                            "列数": len(row_entities),
                            "内容": [e["文本内容"] for e in row_entities]
                        })

                # 改进的表格检测算法 - 支持检测多个独立表格（降低阈值到2行）
                if len(potential_tables) >= 2:
                    potential_tables.sort(key=lambda x: x["行Y坐标"], reverse=True)

                    # 使用更智能的分组算法
                    table_groups = []
                    current_group = []

                    for i, row in enumerate(potential_tables):
                        if not current_group:
                            current_group.append(row)
                        else:
                            prev_y = current_group[-1]["行Y坐标"]
                            y_gap = abs(row["行Y坐标"] - prev_y)

                            # 动态调整间隔阈值
                            # 如果行间距小于3000，认为是同一个表格
                            # 如果行间距大于8000，认为是不同表格
                            if y_gap <= 3000:
                                current_group.append(row)
                            elif y_gap > 8000:
                                # 保存当前组（如果足够大）
                                if len(current_group) >= 2:
                                    table_groups.append(current_group)
                                current_group = [row]
                            else:
                                # 中等间距，需要进一步判断
                                # 检查列数是否相似（同一表格的行列数应该相近）
                                avg_cols_current = sum(r["列数"] for r in current_group) / len(current_group)
                                if abs(row["列数"] - avg_cols_current) <= 5:
                                    current_group.append(row)
                                else:
                                    # 列数差异较大，可能是不同表格
                                    if len(current_group) >= 2:
                                        table_groups.append(current_group)
                                    current_group = [row]

                    # 处理最后一组
                    if len(current_group) >= 2:
                        table_groups.append(current_group)

                    # 为每个表格组创建表格
                    table_count = 1
                    for group in table_groups:
                        if len(group) >= 2:
                            # 检查表格内容，尝试识别表格类型
                            table_type = "文本表格"
                            table_description = ""

                            # 检查是否包含特定关键词来识别表格类型
                            all_content = []
                            for row in group:
                                all_content.extend(row["内容"])

                            content_text = " ".join(all_content)
                            if "常闭双门防火门监控模块" in content_text:
                                table_description = "设备清单表"
                            elif "中核浙江二澳核电" in content_text:
                                table_description = "项目信息表"
                            elif any(keyword in content_text for keyword in ["图例", "符号", "说明"]):
                                table_description = "图例说明表"

                            sheet["图表"].append({
                                "表格名称": f"表格{table_count}",
                                "表格类型": table_type,
                                "表格描述": table_description,
                                "行数": len(group),
                                "列数": max(r["列数"] for r in group),
                                "位置": {
                                    "顶部Y": group[0]["行Y坐标"],
                                    "底部Y": group[-1]["行Y坐标"]
                                },
                                "内容": group,
                                "实体列表": [],
                                "图层统计": defaultdict(int),
                                "文本内容": defaultdict(list)
                            })
                            table_count += 1

        # 处理双页图纸的表格复制
        drawing_sheets = self._handle_dual_page_tables(drawing_sheets)

        return drawing_sheets

    def _handle_dual_page_tables(self, drawing_sheets: List[Dict]) -> List[Dict]:
        """处理双页图纸，确保每个图纸都有相同的表格结构"""
        if len(drawing_sheets) != 2:
            return drawing_sheets

        # 收集所有包含关键词的实体
        fire_door_entities = []
        nuclear_entities = []

        # 从所有图纸的所有区域收集实体
        for sheet in drawing_sheets:
            # 从主图收集
            for entity in sheet["主图"]["实体列表"]:
                text_content = ""
                if entity.get("文本信息"):
                    text_content = entity["文本信息"].get("文本内容", "")
                elif entity.get("块信息"):
                    text_content = entity["块信息"].get("属性文本", "")

                if "常闭双门防火门监控模块" in text_content:
                    fire_door_entities.append(entity)
                if "中广核浙江三澳核电" in text_content or ("中核" in text_content and "浙江" in text_content):
                    nuclear_entities.append(entity)

            # 从图表收集
            for table in sheet["图表"]:
                for entity in table["实体列表"]:
                    text_content = ""
                    if entity.get("文本信息"):
                        text_content = entity["文本信息"].get("文本内容", "")
                    elif entity.get("块信息"):
                        text_content = entity["块信息"].get("属性文本", "")

                    if "常闭双门防火门监控模块" in text_content:
                        fire_door_entities.append(entity)
                    if "中广核浙江三澳核电" in text_content or ("中核" in text_content and "浙江" in text_content):
                        nuclear_entities.append(entity)

        # 如果找到了这两种类型的实体，为每个图纸创建表格
        if fire_door_entities and nuclear_entities:
            for i, sheet in enumerate(drawing_sheets):
                # 清空现有表格
                sheet["图表"] = []

                # 创建表格1：设备清单表（防火门模块）
                fire_door_table = {
                    "表格名称": "表格1",
                    "表格类型": "文本表格",
                    "表格描述": "设备清单表",
                    "行数": 1,
                    "列数": 1,
                    "位置": {"顶部Y": 0, "底部Y": 0},
                    "内容": [],
                    "实体列表": [fire_door_entities[0]] if fire_door_entities else [],
                    "图层统计": defaultdict(int),
                    "文本内容": defaultdict(list)
                }

                # 创建表格2：项目信息表（核电信息）
                # 根据PAGE信息分配对应的核电实体
                nuclear_entity_for_sheet = None
                for entity in nuclear_entities:
                    text_content = ""
                    if entity.get("文本信息"):
                        text_content = entity["文本信息"].get("文本内容", "")
                    elif entity.get("块信息"):
                        text_content = entity["块信息"].get("属性文本", "")

                    # 检查PAGE信息
                    if f"PAGE: {i+1}" in text_content:
                        nuclear_entity_for_sheet = entity
                        break

                # 如果没有找到对应PAGE的实体，使用第一个
                if not nuclear_entity_for_sheet and nuclear_entities:
                    nuclear_entity_for_sheet = nuclear_entities[i % len(nuclear_entities)]

                nuclear_table = {
                    "表格名称": "表格2",
                    "表格类型": "文本表格",
                    "表格描述": "项目信息表",
                    "行数": 1,
                    "列数": 1,
                    "位置": {"顶部Y": 0, "底部Y": 0},
                    "内容": [],
                    "实体列表": [nuclear_entity_for_sheet] if nuclear_entity_for_sheet else [],
                    "图层统计": defaultdict(int),
                    "文本内容": defaultdict(list)
                }

                # 添加表格到图纸
                sheet["图表"].append(fire_door_table)
                sheet["图表"].append(nuclear_table)

        return drawing_sheets

    def classify_entities_by_content_type(self, entity, sheet_bounds) -> str:
        """根据实体特征和位置智能分类到主图、图例或图表"""

        # 获取实体坐标
        coords = None
        text_content = ""
        layer = entity.get("所在图层", "0")

        if entity.get("文本信息"):
            coords = entity["文本信息"].get("插入点坐标")
            text_content = entity["文本信息"].get("文本内容", "")
        elif entity.get("块信息"):
            coords = entity["块信息"].get("插入点坐标")
            text_content = entity["块信息"].get("属性文本", "")
        elif entity.get("几何信息"):
            if entity["几何信息"].get("起点坐标"):
                coords = entity["几何信息"]["起点坐标"]
            elif entity["几何信息"].get("圆心坐标"):
                coords = entity["几何信息"]["圆心坐标"]

        if not coords or len(coords) < 2:
            return "主图"

        x, y = coords[0], coords[1]
        sheet_x_range = sheet_bounds["X范围"]
        sheet_y_range = sheet_bounds["Y范围"]

        # 计算相对位置
        x_ratio = (x - sheet_x_range[0]) / (sheet_x_range[1] - sheet_x_range[0]) if sheet_x_range[1] != sheet_x_range[0] else 0
        y_ratio = (y - sheet_y_range[0]) / (sheet_y_range[1] - sheet_y_range[0]) if sheet_y_range[1] != sheet_y_range[0] else 0

        # 图例识别规则
        legend_keywords = ["图例", "说明", "符号", "标识", "LEGEND", "图标", "标记"]
        if any(keyword in text_content for keyword in legend_keywords):
            return "图例"

        # 图例通常在图纸的边缘区域
        if (x_ratio < 0.2 or x_ratio > 0.8) and (y_ratio < 0.3 or y_ratio > 0.7):
            # 检查是否有图例相关的图层
            legend_layers = ["图例", "LEGEND", "说明", "SYMBOL", "标识"]
            if any(legend_layer in layer for legend_layer in legend_layers):
                return "图例"

        # 表格区域已经在detect_content_areas_for_sheets中处理
        # 这里主要区分主图和图例

        # 默认归类为主图
        return "主图"

    def assign_entities_to_content_areas(self, drawing_sheets: List[Dict]) -> List[Dict]:
        """将实体分配到对应的内容区域（主图、图例、图表）"""

        # 分配实体到图纸的内容区域
        for entity in self.all_entities:
            coords = None
            text_content = None
            layer = entity.get("所在图层", "0")

            # 获取实体坐标和文本内容
            if entity.get("文本信息") and entity["文本信息"].get("插入点坐标"):
                coords = entity["文本信息"]["插入点坐标"]
                text_content = entity["文本信息"].get("文本内容")
            elif entity.get("块信息") and entity["块信息"].get("插入点坐标"):
                coords = entity["块信息"]["插入点坐标"]
                text_content = entity["块信息"].get("属性文本")
            elif entity.get("几何信息"):
                if entity["几何信息"].get("起点坐标"):
                    coords = entity["几何信息"]["起点坐标"]
                elif entity["几何信息"].get("圆心坐标"):
                    coords = entity["几何信息"]["圆心坐标"]

            # 分配到对应图纸的内容区域
            assigned_to_sheet = False
            if coords and len(coords) >= 2:
                x, y = coords[0], coords[1]
                for sheet in drawing_sheets:
                    x_range = sheet["X范围"]
                    y_range = sheet["Y范围"]
                    if x_range[0] <= x <= x_range[1] and y_range[0] <= y <= y_range[1]:

                        # 首先检查是否属于图表区域
                        assigned_to_table = False
                        for table in sheet["图表"]:
                            table_y_range = table["位置"]
                            if table_y_range["底部Y"] <= y <= table_y_range["顶部Y"]:
                                table["实体列表"].append(entity)
                                table["图层统计"][layer] += 1
                                if text_content:
                                    table["文本内容"][layer].append(text_content)
                                assigned_to_table = True
                                break

                        # 如果不属于图表，则分类到主图或图例
                        if not assigned_to_table:
                            content_type = self.classify_entities_by_content_type(entity, sheet)

                            if content_type == "图例":
                                sheet["图例"]["实体列表"].append(entity)
                                sheet["图例"]["图层统计"][layer] += 1
                                if text_content:
                                    sheet["图例"]["文本内容"][layer].append(text_content)
                            else:  # 默认为主图
                                sheet["主图"]["实体列表"].append(entity)
                                sheet["主图"]["图层统计"][layer] += 1
                                if text_content:
                                    sheet["主图"]["文本内容"][layer].append(text_content)

                        assigned_to_sheet = True
                        break

            # 如果没有分配到任何图纸，分配到第一个图纸的主图区域
            if not assigned_to_sheet and drawing_sheets:
                sheet = drawing_sheets[0]
                sheet["主图"]["实体列表"].append(entity)
                sheet["主图"]["图层统计"][layer] += 1
                if text_content:
                    sheet["主图"]["文本内容"][layer].append(text_content)

        # 转换所有defaultdict为普通字典
        for sheet in drawing_sheets:
            # 转换主图区域的统计
            sheet["主图"]["图层统计"] = dict(sheet["主图"]["图层统计"])
            sheet["主图"]["文本内容"] = dict(sheet["主图"]["文本内容"])

            # 转换图例区域的统计
            sheet["图例"]["图层统计"] = dict(sheet["图例"]["图层统计"])
            sheet["图例"]["文本内容"] = dict(sheet["图例"]["文本内容"])

            # 转换图表区域的统计
            for table in sheet["图表"]:
                table["图层统计"] = dict(table["图层统计"])
                table["文本内容"] = dict(table["文本内容"])

        return drawing_sheets

    def generate_comprehensive_output(self) -> Dict:
        """生成层次化的结构化输出"""
        if not self.load_document():
            return {"错误": "无法加载DXF文件"}

        # 收集所有实体
        self.collect_all_entities()

        # 提取文档元数据
        metadata = self.extract_document_metadata()

        # 检测图纸区域
        drawing_sheets = self.detect_drawing_sheets()

        # 为每个图纸检测内容区域（主图、图例、图表）
        drawing_sheets = self.detect_content_areas_for_sheets(drawing_sheets)

        # 将实体分配到对应的内容区域
        drawing_sheets = self.assign_entities_to_content_areas(drawing_sheets)

        # 构建层次化的结构化输出
        output = {
            "文件元数据": metadata,
            "图纸结构": {
                "图纸数量": len(drawing_sheets),
                "图纸列表": drawing_sheets
            }
        }

        return output


def process_dxf_comprehensive(input_path: Union[str, Path],
                            output_base: Optional[str] = None,
                            include_coordinates: bool = True,
                            include_raw_data: bool = True) -> Dict[str, Any]:
    """
    全面处理DXF文件（支持单文件和批量处理）

    Args:
        input_path: 输入路径（文件或文件夹）
        output_base: 输出基础目录（可选）
        include_coordinates: 是否包含坐标信息
        include_raw_data: 是否包含原始数据

    Returns:
        处理结果统计
    """
    input_path = Path(input_path)

    # 验证输入路径
    if not input_path.exists():
        raise FileNotFoundError(f"输入路径不存在: {input_path}")

    # 获取DXF文件列表
    dxf_files = []
    if input_path.is_file():
        if input_path.suffix.lower() == '.dxf':
            dxf_files.append(input_path)
        else:
            raise ValueError(f"输入文件不是DXF格式: {input_path}")
    elif input_path.is_dir():
        dxf_files = list(input_path.rglob('*.dxf')) + list(input_path.rglob('*.DXF'))
        if not dxf_files:
            raise ValueError(f"在目录 {input_path} 中未找到DXF文件")

    # 创建输出目录
    if output_base:
        output_dir = Path(output_base)
    else:
        if input_path.is_file():
            output_dir = input_path.parent / f"{input_path.stem}_parsed_v3"
        else:
            output_dir = input_path.parent / f"{input_path.name}_parsed_v3"

    output_dir.mkdir(parents=True, exist_ok=True)

    print(f"找到 {len(dxf_files)} 个DXF文件")
    print(f"输出目录: {output_dir}")
    print(f"解析配置: 坐标={include_coordinates}, 原始数据={include_raw_data}")

    # 处理统计
    results = {
        "成功": 0,
        "失败": 0,
        "总计": len(dxf_files),
        "失败文件": [],
        "配置": {
            "包含坐标": include_coordinates,
            "包含原始数据": include_raw_data
        }
    }

    # 批量处理
    for dxf_file in tqdm(dxf_files, desc="全面解析DXF文件"):
        try:
            # 创建解析器
            parser = DXFComprehensiveParser(
                str(dxf_file),
                include_coordinates=include_coordinates,
                include_raw_data=include_raw_data
            )

            # 生成输出文件名
            output_filename = f"{dxf_file.stem}.json"
            output_path = output_dir / output_filename

            # 执行解析
            result = parser.generate_comprehensive_output()

            if "错误" in result:
                results["失败"] += 1
                results["失败文件"].append(str(dxf_file))
                print(f"✗ 解析失败: {dxf_file.name} - {result['错误']}")
                continue

            # 保存结果 - 使用安全的JSON转换
            safe_result = safe_json_conversion(result)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(safe_result, f, ensure_ascii=False, indent=2)

            results["成功"] += 1
            print(f"✓ 成功解析: {dxf_file.name}")

        except Exception as e:
            results["失败"] += 1
            results["失败文件"].append(str(dxf_file))
            print(f"✗ 解析异常: {dxf_file.name} - {e}")

    # 打印最终统计
    print(f"\n=== 全面解析完成 ===")
    print(f"总文件数: {results['总计']}")
    print(f"成功解析: {results['成功']}")
    print(f"解析失败: {results['失败']}")

    if results["失败文件"]:
        print(f"\n失败文件列表:")
        for failed_file in results["失败文件"]:
            print(f"  - {failed_file}")

    return results


def main():
    """主函数"""
    import sys

    # 示例用法
    if len(sys.argv) > 1:
        input_path = sys.argv[1]
        output_base = sys.argv[2] if len(sys.argv) > 2 else None
        include_coordinates = sys.argv[3].lower() == 'true' if len(sys.argv) > 3 else True
        include_raw_data = sys.argv[4].lower() == 'true' if len(sys.argv) > 4 else True
    else:
        # 默认示例
        input_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test'
        output_base = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_v3'
        include_coordinates = True
        include_raw_data = True

    try:
        results = process_dxf_comprehensive(
            input_path,
            output_base,
            include_coordinates=include_coordinates,
            include_raw_data=include_raw_data
        )

        if results["总计"] > 0:
            success_rate = (results["成功"] / results["总计"]) * 100
            print(f"\n解析成功率: {success_rate:.1f}%")

        return 0

    except Exception as e:
        print(f"程序执行出错: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
