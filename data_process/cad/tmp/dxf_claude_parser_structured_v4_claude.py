#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DXF结构化解析器 V4 - 基于图像版面识别（修复版）
结合DXF解析和图像版面识别技术，实现更准确的CAD图纸结构化解析

主要修复：
1. 修复JSON序列化问题（移除lambda函数）
2. 优化坐标映射处理
3. 改进错误处理
"""

import os
import json
import ezdxf
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from collections import defaultdict


def json_serializable(obj):
    """将对象转换为JSON可序列化的格式"""
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, (np.bool_, bool)):
        return bool(obj)
    elif isinstance(obj, dict):
        # 过滤掉函数类型的值
        return {k: json_serializable(v) for k, v in obj.items() if not callable(v)}
    elif isinstance(obj, (list, tuple)):
        return [json_serializable(item) for item in obj]
    elif callable(obj):
        # 跳过函数类型
        return None
    else:
        return obj


from tqdm import tqdm
import cv2
import tempfile
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle, Circle, Arc

# 图像处理和OCR相关
try:
    import fitz  # PyMuPDF

    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("警告: PyMuPDF未安装，无法进行DXF到PDF转换")

try:
    from paddleocr import PaddleOCR

    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False
    print("警告: PaddleOCR未安装，无法进行版面识别")



from dxf_parser_structured_v3 import DXFComprehensiveParser as DXFParserV3
# 导入v3版本的基础功能
# try:
#     from ..dxf_parser_structured_v3 import DXFComprehensiveParser as DXFParserV3
# except ImportError:
#     import sys
#     import os

    # sys.path.append(os.path.dirname(__file__))
    # from dxf_parser_structured_v3 import DXFComprehensiveParser as DXFParserV3


class DXFLayoutAnalyzer:
    """DXF版面分析器 - 基于图像识别"""

    def __init__(self):
        self.ocr = None
        if PADDLEOCR_AVAILABLE:
            # 初始化PaddleOCR，启用版面分析
            try:
                self.ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang='ch',
                    show_log=False
                )
            except Exception as e:
                print(f"PaddleOCR初始化失败: {e}")
                self.ocr = None

    def dxf_to_pdf(self, dxf_path: str, output_pdf: str = None) -> str:
        """将DXF文件转换为PDF"""
        try:
            # 创建输出PDF路径
            if output_pdf is None:
                output_pdf = dxf_path.replace('.dxf', '_layout.pdf')

            # 使用ezdxf渲染
            doc = ezdxf.readfile(dxf_path)
            self._render_dxf_to_pdf(doc, output_pdf)

            if os.path.exists(output_pdf):
                print(f"✅ DXF转PDF成功: {os.path.basename(output_pdf)}")
                return output_pdf
            else:
                print(f"❌ DXF转PDF失败")
                return None

        except Exception as e:
            print(f"DXF转PDF失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _render_dxf_to_pdf(self, doc, output_pdf: str):
        """使用ezdxf drawing addon渲染DXF到PDF"""
        try:
            from ezdxf.addons.drawing import RenderContext, Frontend
            from ezdxf.addons.drawing.matplotlib import MatplotlibBackend
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_pdf import PdfPages

            # 获取模型空间
            msp = doc.modelspace()

            # 创建渲染上下文
            ctx = RenderContext(doc)

            # 计算图纸边界
            try:
                bbox = ezdxf.bbox.extents(msp)
                if bbox.has_data:
                    width = bbox.size.x
                    height = bbox.size.y
                    center = bbox.center
                else:
                    # 默认边界
                    width, height = 1000, 1000
                    center = (0, 0)
            except:
                width, height = 1000, 1000
                center = (0, 0)

            # 设置合适的图形尺寸
            aspect_ratio = width / height if height > 0 else 1
            if aspect_ratio > 1:
                fig_width = 16
                fig_height = 16 / aspect_ratio
            else:
                fig_height = 16
                fig_width = 16 * aspect_ratio

            # 创建matplotlib后端
            fig, ax = plt.subplots(figsize=(fig_width, fig_height))
            backend = MatplotlibBackend(ax)

            # 创建前端并渲染
            frontend = Frontend(ctx, backend)
            frontend.draw_layout(msp, finalize=True)

            # 设置显示属性
            ax.set_aspect('equal')
            ax.set_facecolor('white')
            fig.patch.set_facecolor('white')

            # 移除坐标轴
            ax.set_xticks([])
            ax.set_yticks([])
            for spine in ax.spines.values():
                spine.set_visible(False)

            # 保存为PDF
            with PdfPages(output_pdf) as pdf:
                pdf.savefig(fig, bbox_inches='tight', dpi=300,
                            facecolor='white', edgecolor='none')

            plt.close(fig)

        except Exception as e:
            print(f"❌ ezdxf drawing渲染失败: {e}")
            import traceback
            traceback.print_exc()

    def pdf_to_image(self, pdf_path: str) -> List[np.ndarray]:
        """将PDF转换为图像"""
        if not PYMUPDF_AVAILABLE:
            raise ImportError("需要安装PyMuPDF: pip install PyMuPDF")

        images = []
        try:
            doc = fitz.open(pdf_path)
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                # 设置较高的分辨率以便OCR识别
                mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")

                # 转换为OpenCV格式
                nparr = np.frombuffer(img_data, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                images.append(img)

            doc.close()
            return images

        except Exception as e:
            print(f"PDF转图像失败: {e}")
            return []

    def analyze_layout(self, image: np.ndarray) -> Dict:
        """基于图像分析的版面识别"""
        try:
            print("🔍 开始版面布局分析...")

            # 1. 检测双页布局
            pages = self._detect_dual_page_layout(image)
            print(f"检测到 {len(pages)} 个页面")

            layout_result = {
                "pages": [],
                "total_pages": len(pages),
                "analysis_method": "基于图像的版面分析",
                "image_shape": image.shape
            }

            # 2. 分析每个页面的布局
            for i, page_region in enumerate(pages):
                print(f"分析页面 {i + 1}...")
                page_analysis = self._analyze_single_page_layout(image, page_region, i + 1)
                layout_result["pages"].append(page_analysis)

            return layout_result

        except Exception as e:
            print(f"版面分析失败: {e}")
            import traceback
            traceback.print_exc()
            return {"pages": [], "total_pages": 0, "analysis_method": "失败"}

    def _detect_dual_page_layout(self, image: np.ndarray) -> List[Dict]:
        """检测双页布局"""
        height, width = image.shape[:2]

        # 转换为灰度图进行分析
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # 检测垂直分割线
        mid_x = width // 2
        vertical_strip = gray[:, mid_x - 50:mid_x + 50]

        # 计算中间区域的内容密度
        non_white_pixels = np.sum(vertical_strip < 240)
        total_pixels = vertical_strip.size
        middle_density = non_white_pixels / total_pixels if total_pixels > 0 else 0

        # 如果中间区域密度很低，可能是双页布局
        if middle_density < 0.1 and width > height * 1.5:
            print("检测到双页布局")
            pages = [
                {"x": 0, "y": 0, "width": mid_x - 50, "height": height},
                {"x": mid_x + 50, "y": 0, "width": mid_x - 50, "height": height}
            ]
        else:
            print("检测到单页布局")
            pages = [{"x": 0, "y": 0, "width": width, "height": height}]

        return pages

    def _analyze_single_page_layout(self, image: np.ndarray, page_region: Dict, page_num: int) -> Dict:
        """分析单个页面的布局结构"""
        # 提取页面区域
        x, y, w, h = page_region["x"], page_region["y"], page_region["width"], page_region["height"]
        page_image = image[y:y + h, x:x + w]

        # 转换为灰度图
        if len(page_image.shape) == 3:
            gray = cv2.cvtColor(page_image, cv2.COLOR_BGR2GRAY)
        else:
            gray = page_image.copy()

        # 检测表格区域
        tables = self._detect_table_regions(gray, page_region)

        # 检测主图区域
        main_drawing = self._detect_main_drawing_region(gray, page_region, tables)

        # 检测图例区域
        legends = self._detect_legend_regions(gray, page_region, tables)

        page_analysis = {
            "page_number": page_num,
            "page_bounds": page_region,
            "main_drawing": main_drawing,
            "tables": tables,
            "legends": legends,
            "layout_summary": {
                "has_main_drawing": main_drawing is not None,
                "table_count": len(tables),
                "legend_count": len(legends)
            }
        }

        print(f"页面{page_num}: 主图={'有' if main_drawing else '无'}, 表格{len(tables)}个, 图例{len(legends)}个")

        return page_analysis

    def _detect_table_regions(self, gray_image: np.ndarray, page_region: Dict) -> List[Dict]:
        """检测表格区域"""
        height, width = gray_image.shape
        tables = []

        # 使用形态学操作检测表格结构
        kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (30, 1))
        kernel_vertical = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 30))

        # 检测水平和垂直线条
        _, binary = cv2.threshold(gray_image, 200, 255, cv2.THRESH_BINARY_INV)
        horizontal_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel_horizontal)
        vertical_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel_vertical)

        # 合并线条
        table_mask = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0.0)

        # 查找轮廓
        contours, _ = cv2.findContours(table_mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤表格候选区域
        min_table_area = width * height * 0.01  # 至少占页面1%
        max_table_area = width * height * 0.4  # 最多占页面40%

        for contour in contours:
            area = cv2.contourArea(contour)
            if min_table_area < area < max_table_area:
                x, y, w, h = cv2.boundingRect(contour)

                # 表格通常在页面下半部分
                if y > height * 0.4:
                    table_region = {
                        "x": page_region["x"] + x,
                        "y": page_region["y"] + y,
                        "width": w,
                        "height": h,
                        "area": area,
                        "position": self._classify_table_position(x, y, width, height),
                        "detection_method": "morphology"
                    }
                    tables.append(table_region)

        # 如果没有检测到表格，使用固定位置检测
        if not tables:
            tables = self._detect_tables_by_fixed_position(gray_image, page_region)

        return tables

    def _detect_tables_by_fixed_position(self, gray_image: np.ndarray, page_region: Dict) -> List[Dict]:
        """基于固定位置检测表格"""
        height, width = gray_image.shape
        tables = []

        # 工程图纸常见的表格位置
        table_positions = [
            {"x": width * 0.6, "y": height * 0.7, "w": width * 0.35, "h": height * 0.25, "pos": "右下角"},
            {"x": width * 0.05, "y": height * 0.7, "w": width * 0.35, "h": height * 0.25, "pos": "左下角"}
        ]

        for pos in table_positions:
            x, y = int(pos["x"]), int(pos["y"])
            w, h = int(pos["w"]), int(pos["h"])

            # 检查区域内容密度
            roi = gray_image[y:y + h, x:x + w]
            if roi.size > 0:
                density = np.sum(roi < 240) / roi.size

                if density > 0.05:  # 有足够内容
                    table_region = {
                        "x": page_region["x"] + x,
                        "y": page_region["y"] + y,
                        "width": w,
                        "height": h,
                        "area": w * h,
                        "position": pos["pos"],
                        "detection_method": "fixed_position"
                    }
                    tables.append(table_region)

        return tables

    def _classify_table_position(self, x: int, y: int, page_width: int, page_height: int) -> str:
        """分类表格位置"""
        if x < page_width * 0.5:
            return "左下角"
        else:
            return "右下角"

    def _detect_main_drawing_region(self, gray_image: np.ndarray, page_region: Dict, tables: List[Dict]) -> Dict:
        """检测主图区域"""
        height, width = gray_image.shape

        # 主图通常占据页面的中上部分
        main_drawing = {
            "x": page_region["x"] + int(width * 0.05),
            "y": page_region["y"] + int(height * 0.05),
            "width": int(width * 0.9),
            "height": int(height * 0.6),
            "position": "主图区域"
        }

        return main_drawing

    def _detect_legend_regions(self, gray_image: np.ndarray, page_region: Dict, tables: List[Dict]) -> List[Dict]:
        """检测图例区域"""
        # 简化处理，返回空列表
        return []


class CoordinateMapper:
    """坐标映射工具类"""

    def __init__(self, dxf_bounds: Dict, image_bounds: Dict):
        self.dxf_bounds = dxf_bounds
        self.image_bounds = image_bounds

        # 计算缩放比例
        dxf_width = dxf_bounds["x_max"] - dxf_bounds["x_min"]
        dxf_height = dxf_bounds["y_max"] - dxf_bounds["y_min"]
        image_width = image_bounds["x_max"] - image_bounds["x_min"]
        image_height = image_bounds["y_max"] - image_bounds["y_min"]

        self.scale_x = image_width / dxf_width if dxf_width > 0 else 1
        self.scale_y = image_height / dxf_height if dxf_height > 0 else 1

    def dxf_to_image(self, x: float, y: float) -> Tuple[float, float]:
        """DXF坐标转图像坐标"""
        img_x = (x - self.dxf_bounds["x_min"]) * self.scale_x
        img_y = self.image_bounds["y_max"] - (y - self.dxf_bounds["y_min"]) * self.scale_y  # Y轴翻转
        return img_x, img_y

    def image_to_dxf(self, x: float, y: float) -> Tuple[float, float]:
        """图像坐标转DXF坐标"""
        dxf_x = x / self.scale_x + self.dxf_bounds["x_min"]
        dxf_y = (self.image_bounds["y_max"] - y) / self.scale_y + self.dxf_bounds["y_min"]  # Y轴翻转
        return dxf_x, dxf_y

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "dxf_bounds": self.dxf_bounds,
            "image_bounds": self.image_bounds,
            "scale_x": self.scale_x,
            "scale_y": self.scale_y
        }


class DXFStructuredParserV4(DXFParserV3):
    """DXF结构化解析器 V4 - 集成图像版面识别"""

    def __init__(self, dxf_path: str, include_coordinates: bool = True, include_raw_data: bool = True):
        super().__init__(dxf_path, include_coordinates, include_raw_data)
        self.layout_analyzer = DXFLayoutAnalyzer()
        self.layout_results = None
        self.pdf_path = None
        self.coordinate_mapper = None

    def analyze_layout_with_ocr(self) -> Dict:
        """使用OCR进行版面分析"""
        try:
            # 1. DXF转PDF
            print("🔄 转换DXF到PDF...")
            self.pdf_path = self.layout_analyzer.dxf_to_pdf(self.dxf_path)
            if not self.pdf_path:
                print("❌ DXF转PDF失败，使用传统解析方法")
                return self.generate_comprehensive_output()

            # 2. PDF转图像
            print("🔄 转换PDF到图像...")
            images = self.layout_analyzer.pdf_to_image(self.pdf_path)
            if not images:
                print("❌ PDF转图像失败，使用传统解析方法")
                return self.generate_comprehensive_output()

            # 3. 版面布局分析
            print("🔄 进行版面布局分析...")
            layout_results = []
            for i, image in enumerate(images):
                print(f"  分析第{i + 1}页...")
                layout_result = self.layout_analyzer.analyze_layout(image)
                layout_result["original_image"] = image  # 保存原始图像用于可视化
                layout_results.append({
                    "page": i + 1,
                    "layout": layout_result
                })

            self.layout_results = layout_results

            # 4. 结合DXF解析和版面分析
            print("🔄 匹配DXF实体与版面结构...")

            # 保存中间结果和可视化
            self._save_intermediate_results(layout_results)

            return self.generate_layout_aware_output()

        except Exception as e:
            print(f"❌ 版面分析失败: {e}")
            import traceback
            traceback.print_exc()
            print("🔄 回退到传统解析方法...")
            return self.generate_comprehensive_output()

    def generate_layout_aware_output(self) -> Dict:
        """生成基于版面识别的结构化输出"""
        print("🔄 生成版面感知的解析结果...")

        # 先进行传统DXF解析
        traditional_result = self.generate_comprehensive_output()

        if not self.layout_results:
            return traditional_result

        # 基于版面识别重构图纸结构
        enhanced_result = self._integrate_layout_with_dxf_analysis(traditional_result)

        return enhanced_result

    def _integrate_layout_with_dxf_analysis(self, traditional_result: Dict) -> Dict:
        """将版面识别结果与DXF解析结果整合"""
        print("🔄 整合版面识别与DXF解析结果...")

        # 建立坐标映射关系
        self._establish_coordinate_mapping(traditional_result)

        # 创建新的结构化结果
        integrated_result = {
            "文件元数据": traditional_result.get("文件元数据", {}),
            "版面分析": {
                "分析方法": "基于图像的版面布局分析",
                "总页面数": 0,
                "布局详情": []
            },
            "图纸结构": {
                "图纸数量": 0,
                "图纸列表": []
            },
            "文本内容": traditional_result.get("文本内容", {}),
            "表格数据": [],
            "实体匹配": traditional_result.get("实体匹配", {}),
            "坐标映射信息": self.coordinate_mapper.to_dict() if self.coordinate_mapper else None
        }

        # 处理版面识别结果
        total_pages = 0
        for page_result in self.layout_results:
            layout = page_result["layout"]
            if "pages" in layout:
                total_pages += layout["total_pages"]

                for page in layout["pages"]:
                    # 版面分析详情
                    page_info = {
                        "PDF页面": page_result["page"],
                        "图纸页面": page["page_number"],
                        "主图区域": page.get("main_drawing"),
                        "表格区域": page.get("tables", []),
                        "图例区域": page.get("legends", []),
                        "布局摘要": page.get("layout_summary", {})
                    }
                    integrated_result["版面分析"]["布局详情"].append(page_info)

                    # 创建对应的图纸结构
                    drawing_structure = self._create_drawing_structure_from_layout(page, traditional_result)
                    integrated_result["图纸结构"]["图纸列表"].append(drawing_structure)

                    # 为每个表格创建表格数据
                    for table in page.get("tables", []):
                        table_data = self._create_table_data_from_layout(table, traditional_result, page["page_number"])
                        integrated_result["表格数据"].append(table_data)

        integrated_result["版面分析"]["总页面数"] = total_pages
        integrated_result["图纸结构"]["图纸数量"] = total_pages

        print(f"✅ 整合完成: {total_pages}个图纸页面, {len(integrated_result['表格数据'])}个表格")

        return integrated_result

    def _establish_coordinate_mapping(self, traditional_result: Dict):
        """建立图像坐标与DXF坐标的映射关系"""
        try:
            print("🔍 建立坐标映射...")

            # 获取DXF文档和边界
            doc = ezdxf.readfile(self.dxf_path)
            msp = doc.modelspace()

            # 计算DXF实体的边界
            try:
                bbox = ezdxf.bbox.extents(msp)
                if bbox.has_data:
                    dxf_bounds = {
                        "x_min": bbox.extmin.x,
                        "x_max": bbox.extmax.x,
                        "y_min": bbox.extmin.y,
                        "y_max": bbox.extmax.y
                    }
                else:
                    # 使用备用方法计算边界
                    dxf_bounds = self._calculate_dxf_bounds_manually(msp)
            except:
                dxf_bounds = self._calculate_dxf_bounds_manually(msp)

            # 获取图像尺寸
            if self.layout_results and self.layout_results[0]["layout"].get("image_shape"):
                image_shape = self.layout_results[0]["layout"]["image_shape"]
                image_height, image_width = image_shape[:2]

                image_bounds = {
                    "x_min": 0,
                    "x_max": image_width,
                    "y_min": 0,
                    "y_max": image_height
                }

                # 创建坐标映射器
                self.coordinate_mapper = CoordinateMapper(dxf_bounds, image_bounds)

                print(f"✅ 坐标映射建立成功:")
                print(
                    f"   DXF范围: X({dxf_bounds['x_min']:.1f} ~ {dxf_bounds['x_max']:.1f}), Y({dxf_bounds['y_min']:.1f} ~ {dxf_bounds['y_max']:.1f})")
                print(f"   图像尺寸: {image_width} x {image_height}")
                print(f"   缩放比例: X({self.coordinate_mapper.scale_x:.3f}), Y({self.coordinate_mapper.scale_y:.3f})")

        except Exception as e:
            print(f"⚠️  建立坐标映射失败: {e}")
            self.coordinate_mapper = None

    def _calculate_dxf_bounds_manually(self, msp) -> Dict:
        """手动计算DXF边界"""
        x_coords = []
        y_coords = []

        for entity in msp:
            try:
                if entity.dxftype() == 'LINE':
                    x_coords.extend([entity.dxf.start.x, entity.dxf.end.x])
                    y_coords.extend([entity.dxf.start.y, entity.dxf.end.y])
                elif entity.dxftype() in ['TEXT', 'MTEXT']:
                    x_coords.append(entity.dxf.insert.x)
                    y_coords.append(entity.dxf.insert.y)
                elif entity.dxftype() == 'CIRCLE':
                    center = entity.dxf.center
                    radius = entity.dxf.radius
                    x_coords.extend([center.x - radius, center.x + radius])
                    y_coords.extend([center.y - radius, center.y + radius])
            except:
                continue

        if x_coords and y_coords:
            return {
                "x_min": min(x_coords),
                "x_max": max(x_coords),
                "y_min": min(y_coords),
                "y_max": max(y_coords)
            }
        else:
            return {"x_min": 0, "x_max": 1000, "y_min": 0, "y_max": 1000}

    def _create_drawing_structure_from_layout(self, page_layout: Dict, traditional_result: Dict) -> Dict:
        """基于版面布局创建图纸结构"""
        page_num = page_layout["page_number"]
        page_bounds = page_layout.get("page_bounds", {})

        # 获取该页面区域内的文本和实体
        page_texts = self._get_texts_in_region(page_bounds, traditional_result)
        page_entities = self._get_entities_in_region(page_bounds)

        drawing_structure = {
            "图纸名称": f"图纸页面{page_num}",
            "页面边界": page_bounds,
            "文本数量": len(page_texts),
            "实体统计": self._count_entities_by_type(page_entities),
            "主图": None,
            "表格区域": []
        }

        # 处理主图区域
        main_drawing = page_layout.get("main_drawing")
        if main_drawing:
            main_texts = self._get_texts_in_region(main_drawing, traditional_result)
            main_entities = self._get_entities_in_region(main_drawing)

            drawing_structure["主图"] = {
                "区域边界": main_drawing,
                "文本数量": len(main_texts),
                "实体数量": len(main_entities),
                "实体统计": self._count_entities_by_type(main_entities),
                "主要文本": [t.get("text", "") for t in main_texts[:10]]
            }

        # 处理表格区域
        for i, table in enumerate(page_layout.get("tables", [])):
            table_texts = self._get_texts_in_region(table, traditional_result)
            table_entities = self._get_entities_in_region(table)

            table_info = {
                "表格名称": f"表格{i + 1}",
                "位置": table.get("position", "未知"),
                "区域边界": table,
                "文本数量": len(table_texts),
                "实体数量": len(table_entities),
                "表格文本": [t.get("text", "") for t in table_texts[:20]]
            }
            drawing_structure["表格区域"].append(table_info)

        return drawing_structure

    def _create_table_data_from_layout(self, table_layout: Dict, traditional_result: Dict, page_num: int) -> Dict:
        """基于表格布局创建表格数据"""
        table_texts = self._get_texts_in_region(table_layout, traditional_result)
        table_entities = self._get_entities_in_region(table_layout)

        return {
            "表格名称": f"页面{page_num}_{table_layout.get('position', '未知位置')}",
            "位置": table_layout.get("position", "未知"),
            "所属页面": page_num,
            "区域边界": table_layout,
            "检测方法": table_layout.get("detection_method", "未知"),
            "文本数量": len(table_texts),
            "实体数量": len(table_entities),
            "表格内容": [t.get("text", "") for t in table_texts],
            "实体统计": self._count_entities_by_type(table_entities)
        }

    def _get_texts_in_region(self, region: Dict, traditional_result: Dict) -> List[Dict]:
        """获取指定区域内的文本"""
        if not self.coordinate_mapper:
            return []

        all_texts = traditional_result.get("文本内容", {}).get("所有文本", [])
        texts_in_region = []

        # 将图像区域转换为DXF坐标
        img_x_min = region.get("x", 0)
        img_y_min = region.get("y", 0)
        img_x_max = img_x_min + region.get("width", 0)
        img_y_max = img_y_min + region.get("height", 0)

        # 转换坐标
        dxf_x_min, dxf_y_max = self.coordinate_mapper.image_to_dxf(img_x_min, img_y_min)
        dxf_x_max, dxf_y_min = self.coordinate_mapper.image_to_dxf(img_x_max, img_y_max)

        for text in all_texts:
            x = text.get("x", 0)
            y = text.get("y", 0)

            if dxf_x_min <= x <= dxf_x_max and dxf_y_min <= y <= dxf_y_max:
                texts_in_region.append(text)

        return texts_in_region

    def _get_entities_in_region(self, region: Dict) -> List[Dict]:
        """获取指定区域内的实体"""
        if not self.coordinate_mapper:
            return []

        entities = []
        try:
            doc = ezdxf.readfile(self.dxf_path)
            msp = doc.modelspace()

            # 将图像区域转换为DXF坐标
            img_x_min = region.get("x", 0)
            img_y_min = region.get("y", 0)
            img_x_max = img_x_min + region.get("width", 0)
            img_y_max = img_y_min + region.get("height", 0)

            # 转换坐标
            dxf_x_min, dxf_y_max = self.coordinate_mapper.image_to_dxf(img_x_min, img_y_min)
            dxf_x_max, dxf_y_min = self.coordinate_mapper.image_to_dxf(img_x_max, img_y_max)

            for entity in msp:
                try:
                    # 获取实体位置
                    x, y = None, None

                    if entity.dxftype() == 'LINE':
                        x = (entity.dxf.start.x + entity.dxf.end.x) / 2
                        y = (entity.dxf.start.y + entity.dxf.end.y) / 2
                    elif entity.dxftype() in ['TEXT', 'MTEXT']:
                        x = entity.dxf.insert.x
                        y = entity.dxf.insert.y
                    elif entity.dxftype() == 'CIRCLE':
                        x = entity.dxf.center.x
                        y = entity.dxf.center.y
                    elif entity.dxftype() == 'INSERT':
                        x = entity.dxf.insert.x
                        y = entity.dxf.insert.y

                    if x is not None and y is not None:
                        if dxf_x_min <= x <= dxf_x_max and dxf_y_min <= y <= dxf_y_max:
                            entities.append({
                                "type": entity.dxftype(),
                                "layer": entity.dxf.layer,
                                "x": x,
                                "y": y
                            })
                except:
                    continue

        except Exception as e:
            print(f"获取区域实体失败: {e}")

        return entities

    def _count_entities_by_type(self, entities: List[Dict]) -> Dict:
        """统计实体类型"""
        counts = {}
        for entity in entities:
            entity_type = entity.get("type", "UNKNOWN")
            counts[entity_type] = counts.get(entity_type, 0) + 1
        return counts

    def _save_intermediate_results(self, layout_results):
        """保存中间结果和可视化"""
        try:
            # 获取输出目录
            dxf_dir = os.path.dirname(self.dxf_path)
            base_name = os.path.splitext(os.path.basename(self.dxf_path))[0]
            output_dir = os.path.join(dxf_dir, f"图纸格式转化_{base_name}_test")
            os.makedirs(output_dir, exist_ok=True)

            print(f"📁 保存中间结果到: {output_dir}")

            # 1. 保存PDF文件
            if self.pdf_path and os.path.exists(self.pdf_path):
                import shutil
                pdf_dest = os.path.join(output_dir, f"{base_name}_layout.pdf")
                shutil.copy2(self.pdf_path, pdf_dest)
                print(f"   ✅ PDF文件: {os.path.basename(pdf_dest)}")

            # 2. 保存版面识别结果JSON
            layout_file = os.path.join(output_dir, f"{base_name}_layout_results.json")
            # 移除图像数据以减小文件大小
            layout_data = []
            for result in layout_results:
                clean_result = result.copy()
                if "layout" in clean_result and "original_image" in clean_result["layout"]:
                    clean_result["layout"] = clean_result["layout"].copy()
                    clean_result["layout"].pop("original_image")
                layout_data.append(clean_result)

            with open(layout_file, 'w', encoding='utf-8') as f:
                json.dump(json_serializable(layout_data), f, ensure_ascii=False, indent=2)
            print(f"   ✅ 版面识别结果: {os.path.basename(layout_file)}")

            # 3. 保存原始图像
            for i, result in enumerate(layout_results):
                if "layout" in result and "original_image" in result["layout"]:
                    image = result["layout"]["original_image"]
                    image_file = os.path.join(output_dir, f"{base_name}_page_{i + 1}_original.png")
                    cv2.imwrite(image_file, image)
                    print(f"   ✅ 原始图像: {os.path.basename(image_file)}")

            # 4. 生成并保存版面可视化
            self._generate_and_save_layout_visualization(output_dir, base_name, layout_results)

            # 5. 保存坐标映射信息
            if self.coordinate_mapper:
                mapping_file = os.path.join(output_dir, f"{base_name}_coordinate_mapping.json")
                with open(mapping_file, 'w', encoding='utf-8') as f:
                    json.dump(self.coordinate_mapper.to_dict(), f, ensure_ascii=False, indent=2)
                print(f"   ✅ 坐标映射: {os.path.basename(mapping_file)}")

        except Exception as e:
            print(f"⚠️  保存中间结果失败: {e}")
            import traceback
            traceback.print_exc()

    def _generate_and_save_layout_visualization(self, output_dir, base_name, layout_results):
        """生成并保存版面可视化"""
        try:
            for page_idx, page_result in enumerate(layout_results):
                layout = page_result["layout"]
                if "original_image" not in layout:
                    continue

                image = layout["original_image"]

                # 为每个检测到的页面创建可视化
                for sub_page_idx, page_layout in enumerate(layout.get("pages", [])):
                    fig, ax = plt.subplots(figsize=(16, 12))

                    # 显示原始图像
                    ax.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
                    ax.set_title(f'页面{page_idx + 1}-{sub_page_idx + 1} 版面识别结果',
                                 fontsize=16, fontweight='bold', pad=20)

                    # 绘制页面边界
                    page_bounds = page_layout.get("page_bounds", {})
                    if page_bounds:
                        rect = Rectangle(
                            (page_bounds["x"], page_bounds["y"]),
                            page_bounds["width"], page_bounds["height"],
                            linewidth=4, edgecolor='green', facecolor='none',
                            linestyle='--', label='页面边界'
                        )
                        ax.add_patch(rect)

                    # 绘制主图区域
                    main_drawing = page_layout.get("main_drawing")
                    if main_drawing:
                        rect = Rectangle(
                            (main_drawing["x"], main_drawing["y"]),
                            main_drawing["width"], main_drawing["height"],
                            linewidth=3, edgecolor='red', facecolor='none',
                            alpha=0.8, label='主图区域'
                        )
                        ax.add_patch(rect)

                        # 添加标签
                        ax.text(main_drawing["x"] + main_drawing["width"] / 2,
                                main_drawing["y"] - 10, '主图区域',
                                fontsize=14, color='red', fontweight='bold',
                                ha='center', va='bottom',
                                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

                    # 绘制表格区域
                    for i, table in enumerate(page_layout.get("tables", [])):
                        rect = Rectangle(
                            (table["x"], table["y"]),
                            table["width"], table["height"],
                            linewidth=2, edgecolor='blue', facecolor='none',
                            alpha=0.8
                        )
                        ax.add_patch(rect)

                        # 添加标签
                        label = f'表格{i + 1}\n{table.get("position", "")}\n{table.get("detection_method", "")}'
                        ax.text(table["x"] + table["width"] / 2,
                                table["y"] + table["height"] / 2, label,
                                fontsize=12, color='blue', fontweight='bold',
                                ha='center', va='center',
                                bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.7))

                    # 绘制图例区域
                    for i, legend in enumerate(page_layout.get("legends", [])):
                        rect = Rectangle(
                            (legend["x"], legend["y"]),
                            legend["width"], legend["height"],
                            linewidth=2, edgecolor='purple', facecolor='none',
                            alpha=0.8
                        )
                        ax.add_patch(rect)

                        ax.text(legend["x"] + legend["width"] / 2,
                                legend["y"] - 10, f'图例{i + 1}',
                                fontsize=12, color='purple', fontweight='bold',
                                ha='center', va='bottom',
                                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

                    # 添加图例
                    ax.legend(loc='upper right', fontsize=12)

                    # 设置坐标轴
                    ax.set_xlim(0, image.shape[1])
                    ax.set_ylim(image.shape[0], 0)  # 翻转Y轴
                    ax.set_xlabel('X (像素)', fontsize=12)
                    ax.set_ylabel('Y (像素)', fontsize=12)
                    ax.grid(True, alpha=0.3)

                    # 保存可视化
                    vis_file = os.path.join(output_dir,
                                            f"{base_name}_layout_visualization_page{page_idx + 1}_{sub_page_idx + 1}.png")
                    plt.savefig(vis_file, dpi=150, bbox_inches='tight', facecolor='white')
                    plt.close()

                    print(f"   ✅ 版面可视化: {os.path.basename(vis_file)}")

        except Exception as e:
            print(f"⚠️  生成版面可视化失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数 - 测试V4版本"""
    import glob

    # 测试文件路径
    dxf_dir = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test"
    output_dir = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_v4"

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 查找DXF文件
    dxf_files = glob.glob(os.path.join(dxf_dir, "*.dxf"))

    if not dxf_files:
        print(f"在 {dxf_dir} 中未找到DXF文件")
        return

    print(f"找到 {len(dxf_files)} 个DXF文件")
    print(f"输出目录: {output_dir}")

    # 解析配置
    include_coordinates = True
    include_raw_data = True
    print(f"解析配置: 坐标={include_coordinates}, 原始数据={include_raw_data}")

    success_count = 0

    # 处理每个文件
    for dxf_file in tqdm(dxf_files, desc="V4版面识别解析DXF文件"):
        try:
            print(f"\n处理文件: {os.path.basename(dxf_file)}")

            # 创建解析器
            parser = DXFStructuredParserV4(
                dxf_file,
                include_coordinates=include_coordinates,
                include_raw_data=include_raw_data
            )

            # 进行版面识别解析
            result = parser.analyze_layout_with_ocr()

            # 保存结果
            filename = os.path.basename(dxf_file).replace('.dxf', '.json')
            output_path = os.path.join(output_dir, filename)

            # 转换为JSON可序列化格式
            serializable_result = json_serializable(result)

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_result, f, ensure_ascii=False, indent=2)

            print(f"✓ 成功解析: {filename}")
            success_count += 1

        except Exception as e:
            print(f"✗ 解析失败: {os.path.basename(dxf_file)} - {e}")
            import traceback
            traceback.print_exc()

    print(f"\n=== V4版面识别解析完成 ===")
    print(f"总文件数: {len(dxf_files)}")
    print(f"成功解析: {success_count}")
    print(f"解析失败: {len(dxf_files) - success_count}")
    print(f"解析成功率: {success_count / len(dxf_files) * 100:.1f}%")


if __name__ == "__main__":
    main()
