#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DXF转PDF和图片的高质量转换脚本
支持单文件和批量转换，针对深色DXF文件优化
"""

import os
import sys
import argparse
from pathlib import Path
import ezdxf
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.backends.backend_pdf import PdfPages
import cv2
import numpy as np
from typing import Union, List, Tuple
import warnings

# 忽略matplotlib的一些警告
warnings.filterwarnings('ignore', category=UserWarning)


class DXFConverter:
    def __init__(self, dpi: int = 300, line_width: float = 0.5):
        """
        初始化DXF转换器

        Args:
            dpi: 输出图像的DPI，默认300（高质量）
            line_width: 线条宽度，默认0.5
        """
        self.dpi = dpi
        self.line_width = line_width

        # 针对深色DXF的颜色映射
        self.color_map = {
            0: '#FFFFFF',  # 白色 - 对于深色背景
            1: '#FF0000',  # 红色
            2: '#FFFF00',  # 黄色
            3: '#00FF00',  # 绿色
            4: '#00FFFF',  # 青色
            5: '#0000FF',  # 蓝色
            6: '#FF00FF',  # 洋红
            7: '#FFFFFF',  # 白色
            8: '#808080',  # 灰色
            9: '#C0C0C0',  # 浅灰色
        }

    def get_entity_color(self, entity) -> str:
        """获取实体颜色"""
        try:
            color_index = entity.dxf.color
            if color_index == 256:  # BYLAYER
                color_index = entity.dxf.layer
            if color_index == 0:
                return '#FFFFFF'  # 默认白色用于深色背景
            return self.color_map.get(color_index, '#FFFFFF')
        except:
            return '#FFFFFF'

    def extract_entities(self, doc) -> List[dict]:
        """提取DXF文件中的所有实体"""
        entities = []

        # 获取模型空间
        msp = doc.modelspace()

        for entity in msp:
            entity_data = {
                'type': entity.dxftype(),
                'color': self.get_entity_color(entity),
                'layer': getattr(entity.dxf, 'layer', '0')
            }

            try:
                if entity.dxftype() == 'LINE':
                    entity_data.update({
                        'start': (entity.dxf.start.x, entity.dxf.start.y),
                        'end': (entity.dxf.end.x, entity.dxf.end.y)
                    })

                elif entity.dxftype() == 'CIRCLE':
                    entity_data.update({
                        'center': (entity.dxf.center.x, entity.dxf.center.y),
                        'radius': entity.dxf.radius
                    })

                elif entity.dxftype() == 'ARC':
                    entity_data.update({
                        'center': (entity.dxf.center.x, entity.dxf.center.y),
                        'radius': entity.dxf.radius,
                        'start_angle': entity.dxf.start_angle,
                        'end_angle': entity.dxf.end_angle
                    })

                elif entity.dxftype() == 'POLYLINE' or entity.dxftype() == 'LWPOLYLINE':
                    points = []
                    if hasattr(entity, 'vertices'):
                        for vertex in entity.vertices:
                            points.append((vertex.dxf.location.x, vertex.dxf.location.y))
                    elif hasattr(entity, 'get_points'):
                        points = [(p[0], p[1]) for p in entity.get_points()]
                    entity_data['points'] = points

                elif entity.dxftype() == 'TEXT':
                    entity_data.update({
                        'text': entity.dxf.text,
                        'insert': (entity.dxf.insert.x, entity.dxf.insert.y),
                        'height': getattr(entity.dxf, 'height', 1.0)
                    })

                entities.append(entity_data)

            except Exception as e:
                print(f"警告: 处理实体 {entity.dxftype()} 时出错: {e}")
                continue

        return entities

    def calculate_bounds(self, entities: List[dict]) -> Tuple[float, float, float, float]:
        """计算所有实体的边界框"""
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')

        for entity in entities:
            if entity['type'] == 'LINE':
                x_coords = [entity['start'][0], entity['end'][0]]
                y_coords = [entity['start'][1], entity['end'][1]]

            elif entity['type'] in ['CIRCLE', 'ARC']:
                center_x, center_y = entity['center']
                radius = entity['radius']
                x_coords = [center_x - radius, center_x + radius]
                y_coords = [center_y - radius, center_y + radius]

            elif entity['type'] in ['POLYLINE', 'LWPOLYLINE']:
                if 'points' in entity and entity['points']:
                    x_coords = [p[0] for p in entity['points']]
                    y_coords = [p[1] for p in entity['points']]
                else:
                    continue

            elif entity['type'] == 'TEXT':
                x_coords = [entity['insert'][0]]
                y_coords = [entity['insert'][1]]

            else:
                continue

            min_x = min(min_x, min(x_coords))
            max_x = max(max_x, max(x_coords))
            min_y = min(min_y, min(y_coords))
            max_y = max(max_y, max(y_coords))

        # 添加边距
        margin = max((max_x - min_x), (max_y - min_y)) * 0.05
        return min_x - margin, min_y - margin, max_x + margin, max_y + margin

    def create_plot(self, entities: List[dict], bounds: Tuple[float, float, float, float]) -> plt.Figure:
        """创建matplotlib图形"""
        min_x, min_y, max_x, max_y = bounds

        # 计算合适的图形尺寸
        width = max_x - min_x
        height = max_y - min_y
        aspect_ratio = width / height if height != 0 else 1

        if aspect_ratio > 1:
            fig_width = 16
            fig_height = 16 / aspect_ratio
        else:
            fig_height = 16
            fig_width = 16 * aspect_ratio

        # 创建高质量图形
        fig, ax = plt.subplots(figsize=(fig_width, fig_height), dpi=self.dpi)

        # 设置深色背景以适应深色DXF文件
        fig.patch.set_facecolor('black')
        ax.set_facecolor('black')

        # 绘制实体
        for entity in entities:
            color = entity['color']

            try:
                if entity['type'] == 'LINE':
                    start = entity['start']
                    end = entity['end']
                    ax.plot([start[0], end[0]], [start[1], end[1]],
                            color=color, linewidth=self.line_width, antialiased=True)

                elif entity['type'] == 'CIRCLE':
                    center = entity['center']
                    radius = entity['radius']
                    circle = plt.Circle(center, radius, fill=False,
                                        color=color, linewidth=self.line_width)
                    ax.add_patch(circle)

                elif entity['type'] == 'ARC':
                    center = entity['center']
                    radius = entity['radius']
                    start_angle = entity['start_angle']
                    end_angle = entity['end_angle']

                    # 处理角度跨越问题
                    if end_angle < start_angle:
                        end_angle += 360

                    arc = patches.Arc(center, 2 * radius, 2 * radius,
                                      theta1=start_angle, theta2=end_angle,
                                      color=color, linewidth=self.line_width)
                    ax.add_patch(arc)

                elif entity['type'] in ['POLYLINE', 'LWPOLYLINE']:
                    if 'points' in entity and len(entity['points']) > 1:
                        points = entity['points']
                        x_coords = [p[0] for p in points]
                        y_coords = [p[1] for p in points]
                        ax.plot(x_coords, y_coords, color=color,
                                linewidth=self.line_width, antialiased=True)

                elif entity['type'] == 'TEXT':
                    ax.text(entity['insert'][0], entity['insert'][1], entity['text'],
                            color=color, fontsize=entity['height'] * 2,
                            verticalalignment='bottom')

            except Exception as e:
                print(f"警告: 绘制实体时出错: {e}")
                continue

        # 设置坐标轴
        ax.set_xlim(min_x, max_x)
        ax.set_ylim(min_y, max_y)
        ax.set_aspect('equal')
        ax.axis('off')  # 隐藏坐标轴

        # 移除边距
        plt.tight_layout(pad=0)

        return fig

    def convert_file(self, input_path: str, output_dir: str = None) -> bool:
        """
        转换单个DXF文件

        Args:
            input_path: DXF文件路径
            output_dir: 输出目录，默认为输入文件所在目录

        Returns:
            bool: 转换是否成功
        """
        try:
            input_path = Path(input_path)
            if not input_path.exists():
                print(f"错误: 文件不存在 - {input_path}")
                return False

            if output_dir is None:
                output_dir = input_path.parent
            else:
                output_dir = Path(output_dir)
                output_dir.mkdir(parents=True, exist_ok=True)

            print(f"正在处理: {input_path.name}")

            # 读取DXF文件
            try:
                doc = ezdxf.readfile(str(input_path))
            except Exception as e:
                print(f"错误: 无法读取DXF文件 - {e}")
                return False

            # 提取实体
            entities = self.extract_entities(doc)
            if not entities:
                print("警告: 未找到可绘制的实体")
                return False

            # 计算边界
            bounds = self.calculate_bounds(entities)

            # 创建图形
            fig = self.create_plot(entities, bounds)

            # 生成输出文件名
            base_name = input_path.stem

            # 保存为PDF
            pdf_path = output_dir / f"{base_name}.pdf"
            with PdfPages(str(pdf_path)) as pdf:
                pdf.savefig(fig, bbox_inches='tight', pad_inches=0,
                            facecolor='black', edgecolor='none')
            print(f"PDF已保存: {pdf_path}")

            # 保存为PNG（高质量）
            png_path = output_dir / f"{base_name}.png"
            fig.savefig(str(png_path), dpi=self.dpi, bbox_inches='tight',
                        pad_inches=0, facecolor='black', edgecolor='none',
                        format='png', transparent=False)
            print(f"PNG已保存: {png_path}")

            # 保存为JPG（使用OpenCV进行后处理以提高质量）
            jpg_path = output_dir / f"{base_name}.jpg"

            # 先保存为临时PNG
            temp_png = output_dir / f"{base_name}_temp.png"
            fig.savefig(str(temp_png), dpi=self.dpi, bbox_inches='tight',
                        pad_inches=0, facecolor='black', edgecolor='none')

            # 使用OpenCV读取并转换为JPG
            img = cv2.imread(str(temp_png))
            if img is not None:
                # 应用高质量JPEG压缩
                cv2.imwrite(str(jpg_path), img, [cv2.IMWRITE_JPEG_QUALITY, 95])
                print(f"JPG已保存: {jpg_path}")

                # 删除临时文件
                temp_png.unlink()

            plt.close(fig)  # 释放内存
            return True

        except Exception as e:
            print(f"错误: 转换文件时出错 - {e}")
            return False

    def convert_directory(self, input_dir: str, output_dir: str = None) -> int:
        """
        批量转换目录中的所有DXF文件

        Args:
            input_dir: 输入目录路径
            output_dir: 输出目录路径

        Returns:
            int: 成功转换的文件数量
        """
        input_dir = Path(input_dir)
        if not input_dir.exists() or not input_dir.is_dir():
            print(f"错误: 目录不存在或不是有效目录 - {input_dir}")
            return 0

        if output_dir is None:
            output_dir = input_dir / "converted"
        else:
            output_dir = Path(output_dir)

        output_dir.mkdir(parents=True, exist_ok=True)

        # 查找所有DXF文件
        dxf_files = list(input_dir.glob("*.dxf")) + list(input_dir.glob("*.DXF"))

        if not dxf_files:
            print(f"在目录 {input_dir} 中未找到DXF文件")
            return 0

        print(f"找到 {len(dxf_files)} 个DXF文件")

        success_count = 0
        for dxf_file in dxf_files:
            if self.convert_file(str(dxf_file), str(output_dir)):
                success_count += 1
            print("-" * 50)

        print(f"转换完成: {success_count}/{len(dxf_files)} 个文件成功转换")
        return success_count


def main():
    parser = argparse.ArgumentParser(description="DXF转PDF和图片的高质量转换工具")
    parser.add_argument("input", help="输入DXF文件路径或目录路径")
    parser.add_argument("-o", "--output", help="输出目录路径（可选）")
    parser.add_argument("--dpi", type=int, default=300, help="输出图像DPI（默认300）")
    parser.add_argument("--line-width", type=float, default=0.5, help="线条宽度（默认0.5）")

    args = parser.parse_args()

    # 创建转换器
    converter = DXFConverter(dpi=args.dpi, line_width=args.line_width)

    input_path = Path(args.input)

    if input_path.is_file():
        # 转换单个文件
        success = converter.convert_file(str(input_path), args.output)
        sys.exit(0 if success else 1)
    elif input_path.is_dir():
        # 批量转换目录
        success_count = converter.convert_directory(str(input_path), args.output)
        sys.exit(0 if success_count > 0 else 1)
    else:
        print(f"错误: 路径不存在 - {input_path}")
        sys.exit(1)


if __name__ == "__main__":
    main()
