#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速PDF转换测试脚本
用于快速测试单个DXF文件的转换效果
"""

import os
import sys
import time
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def quick_test_dxf_to_pdf(dxf_path: str, method: str = 'auto'):
    """快速测试DXF转PDF"""
    print(f"🚀 快速PDF转换测试")
    print(f"📁 输入文件: {os.path.basename(dxf_path)}")
    print(f"🔧 转换方法: {method}")
    print("-" * 50)
    
    if not os.path.exists(dxf_path):
        print(f"❌ 文件不存在: {dxf_path}")
        return False
    
    try:
        # 根据方法选择渲染器
        if method == 'ezdxf' or method == 'auto':
            print("🔄 使用ezdxf drawing渲染器...")
            from advanced_dxf_renderer import AdvancedDXFRenderer
            renderer = AdvancedDXFRenderer()
            
            output_pdf = dxf_path.replace('.dxf', '_quick_test.pdf')
            start_time = time.time()
            
            success = renderer.render_with_ezdxf_drawing(dxf_path, output_pdf)
            end_time = time.time()
            
            if success and os.path.exists(output_pdf):
                file_size = os.path.getsize(output_pdf)
                print(f"✅ 转换成功!")
                print(f"   📁 输出文件: {os.path.basename(output_pdf)}")
                print(f"   📊 文件大小: {file_size / 1024:.1f} KB")
                print(f"   ⏱️  转换耗时: {end_time - start_time:.2f} 秒")
                
                # 尝试验证PDF
                try:
                    from advanced_dxf_renderer import AdvancedDXFRenderer
                    test_renderer = AdvancedDXFRenderer()
                    # 这里可以添加PDF验证逻辑
                    print(f"   ✅ PDF文件验证通过")
                except Exception as e:
                    print(f"   ⚠️  PDF验证失败: {e}")
                
                return True, output_pdf
            else:
                print(f"❌ ezdxf渲染失败")
                
        if method == 'matplotlib' or (method == 'auto' and not success):
            print("🔄 使用自定义matplotlib渲染器...")
            from advanced_dxf_renderer import AdvancedDXFRenderer
            renderer = AdvancedDXFRenderer()
            
            output_pdf = dxf_path.replace('.dxf', '_matplotlib_test.pdf')
            start_time = time.time()
            
            success = renderer.render_with_custom_matplotlib(dxf_path, output_pdf)
            end_time = time.time()
            
            if success and os.path.exists(output_pdf):
                file_size = os.path.getsize(output_pdf)
                print(f"✅ 转换成功!")
                print(f"   📁 输出文件: {os.path.basename(output_pdf)}")
                print(f"   📊 文件大小: {file_size / 1024:.1f} KB")
                print(f"   ⏱️  转换耗时: {end_time - start_time:.2f} 秒")
                return True, output_pdf
            else:
                print(f"❌ matplotlib渲染失败")
        
        if method == 'v4' or (method == 'auto' and not success):
            print("🔄 使用V4优化渲染器...")
            from dxf_parser_structured_v4 import DXFLayoutAnalyzer
            analyzer = DXFLayoutAnalyzer()
            
            output_pdf = dxf_path.replace('.dxf', '_v4_test.pdf')
            start_time = time.time()
            
            result_pdf = analyzer.dxf_to_pdf(dxf_path, output_pdf, use_advanced=True)
            end_time = time.time()
            
            if result_pdf and os.path.exists(result_pdf):
                file_size = os.path.getsize(result_pdf)
                print(f"✅ 转换成功!")
                print(f"   📁 输出文件: {os.path.basename(result_pdf)}")
                print(f"   📊 文件大小: {file_size / 1024:.1f} KB")
                print(f"   ⏱️  转换耗时: {end_time - start_time:.2f} 秒")
                return True, result_pdf
            else:
                print(f"❌ V4渲染失败")
        
        print(f"❌ 所有渲染方法都失败了")
        return False, None
        
    except Exception as e:
        print(f"❌ 转换异常: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def analyze_dxf_file(dxf_path: str):
    """分析DXF文件的基本信息"""
    print(f"\n📊 DXF文件分析")
    print("-" * 30)
    
    try:
        import ezdxf
        doc = ezdxf.readfile(dxf_path)
        msp = doc.modelspace()
        
        # 统计实体类型
        entity_stats = {}
        for entity in msp:
            entity_type = entity.dxftype()
            entity_stats[entity_type] = entity_stats.get(entity_type, 0) + 1
        
        print(f"DXF版本: {doc.dxfversion}")
        print(f"实体总数: {sum(entity_stats.values())}")
        print(f"实体类型: {len(entity_stats)}")
        
        print(f"\n实体统计:")
        for entity_type, count in sorted(entity_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"  {entity_type}: {count}")
        
        # 计算边界
        try:
            bbox = ezdxf.bbox.extents(msp)
            if bbox.has_data:
                print(f"\n图纸边界:")
                print(f"  X: {bbox.extmin.x:.2f} ~ {bbox.extmax.x:.2f} (宽度: {bbox.size.x:.2f})")
                print(f"  Y: {bbox.extmin.y:.2f} ~ {bbox.extmax.y:.2f} (高度: {bbox.size.y:.2f})")
            else:
                print(f"\n⚠️  无法计算图纸边界")
        except Exception as e:
            print(f"\n⚠️  边界计算失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速DXF转PDF测试工具")
    print("=" * 40)
    
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python quick_pdf_test.py <dxf_file> [method]")
        print("")
        print("方法选项:")
        print("  auto      - 自动选择最佳方法 (默认)")
        print("  ezdxf     - 使用ezdxf drawing渲染器")
        print("  matplotlib - 使用自定义matplotlib渲染器")
        print("  v4        - 使用V4优化渲染器")
        print("")
        print("示例:")
        print("  python quick_pdf_test.py file.dxf")
        print("  python quick_pdf_test.py file.dxf ezdxf")
        return
    
    dxf_path = sys.argv[1]
    method = sys.argv[2] if len(sys.argv) > 2 else 'auto'
    
    # 检查依赖
    try:
        import matplotlib
        import ezdxf
        print("✅ 基础依赖检查通过")
    except ImportError as e:
        print(f"❌ 依赖检查失败: {e}")
        print("请安装必要的依赖: pip install matplotlib ezdxf")
        return
    
    # 分析DXF文件
    if analyze_dxf_file(dxf_path):
        print("")
        
        # 执行转换测试
        success, output_file = quick_test_dxf_to_pdf(dxf_path, method)
        
        if success:
            print(f"\n🎉 测试完成! 请查看生成的PDF文件:")
            print(f"   {output_file}")
            
            # 提供打开文件的建议
            if sys.platform == "darwin":  # macOS
                print(f"\n💡 在macOS上打开文件:")
                print(f"   open \"{output_file}\"")
            elif sys.platform.startswith("linux"):  # Linux
                print(f"\n💡 在Linux上打开文件:")
                print(f"   xdg-open \"{output_file}\"")
            elif sys.platform == "win32":  # Windows
                print(f"\n💡 在Windows上打开文件:")
                print(f"   start \"\" \"{output_file}\"")
        else:
            print(f"\n❌ 测试失败")

if __name__ == "__main__":
    main()
