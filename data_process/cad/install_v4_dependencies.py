#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DXF解析器V4依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {package} - {e}")
        return False

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ 已安装: {package_name}")
        return True
    except ImportError:
        print(f"❌ 未安装: {package_name}")
        return False

def main():
    """主安装流程"""
    print("🚀 DXF解析器V4依赖检查和安装")
    print("=" * 50)
    
    # 必需的依赖包
    dependencies = [
        # 基础依赖
        ("numpy", "numpy"),
        ("opencv-python", "cv2"),
        ("matplotlib", "matplotlib"),
        ("tqdm", "tqdm"),
        
        # DXF处理
        ("ezdxf", "ezdxf"),
        
        # PDF处理
        ("PyMuPDF", "fitz"),
        
        # OCR和版面识别
        ("paddlepaddle", "paddle"),
        ("paddleocr", "paddleocr"),
        
        # 图像处理增强
        ("Pillow", "PIL"),
        ("scikit-image", "skimage"),
    ]
    
    print("📋 检查现有依赖...")
    missing_packages = []
    
    for package_name, import_name in dependencies:
        if not check_package(package_name, import_name):
            missing_packages.append(package_name)
    
    if not missing_packages:
        print("\n🎉 所有依赖都已安装！")
        return
    
    print(f"\n📦 需要安装 {len(missing_packages)} 个包:")
    for pkg in missing_packages:
        print(f"  - {pkg}")
    
    # 询问是否安装
    response = input("\n是否现在安装缺失的依赖？(y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("❌ 取消安装")
        return
    
    print("\n🔧 开始安装依赖...")
    
    # 特殊处理某些包
    success_count = 0
    
    for package in missing_packages:
        print(f"\n📦 安装 {package}...")
        
        if package == "paddlepaddle":
            # PaddlePaddle需要特殊安装命令
            if install_package("paddlepaddle -i https://pypi.tuna.tsinghua.edu.cn/simple/"):
                success_count += 1
        elif package == "paddleocr":
            # PaddleOCR
            if install_package("paddleocr -i https://pypi.tuna.tsinghua.edu.cn/simple/"):
                success_count += 1
        elif package == "PyMuPDF":
            # PyMuPDF
            if install_package("PyMuPDF"):
                success_count += 1
        else:
            if install_package(package):
                success_count += 1
    
    print(f"\n📊 安装结果:")
    print(f"  成功: {success_count}/{len(missing_packages)}")
    print(f"  失败: {len(missing_packages) - success_count}/{len(missing_packages)}")
    
    if success_count == len(missing_packages):
        print("\n🎉 所有依赖安装完成！")
        print("\n📝 使用说明:")
        print("1. 运行 python dxf_parser_structured_v4.py 开始解析")
        print("2. V4版本将自动进行版面识别和结构化解析")
        print("3. 结果将包含传统DXF解析 + OCR版面分析")
    else:
        print("\n⚠️  部分依赖安装失败，请手动安装:")
        failed_packages = missing_packages[success_count:]
        for pkg in failed_packages:
            print(f"  pip install {pkg}")

def test_installation():
    """测试安装是否成功"""
    print("\n🧪 测试安装...")
    
    try:
        import numpy as np
        print("✅ NumPy 正常")
        
        import cv2
        print("✅ OpenCV 正常")
        
        import ezdxf
        print("✅ ezdxf 正常")
        
        import fitz
        print("✅ PyMuPDF 正常")
        
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR 正常")
        
        print("\n🎉 所有核心依赖测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

if __name__ == "__main__":
    main()
    
    # 测试安装
    if input("\n是否测试安装结果？(y/n): ").lower().strip() in ['y', 'yes', '是']:
        test_installation()
