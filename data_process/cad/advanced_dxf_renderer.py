#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级DXF渲染器
使用ezdxf的内置渲染功能和多种渲染后端
"""

import os
import sys
import ezdxf
import numpy as np
from pathlib import Path
from typing import Optional, Tuple, List

class AdvancedDXFRenderer:
    """高级DXF渲染器"""
    
    def __init__(self):
        self.supported_backends = self._check_backends()
        print(f"可用渲染后端: {', '.join(self.supported_backends)}")
    
    def _check_backends(self) -> List[str]:
        """检查可用的渲染后端"""
        backends = []
        
        # 检查matplotlib
        try:
            import matplotlib
            backends.append('matplotlib')
        except ImportError:
            pass
        
        # 检查ezdxf的addons
        try:
            from ezdxf.addons.drawing import RenderContext, Frontend
            backends.append('ezdxf_drawing')
        except ImportError:
            pass
        
        # 检查PyQt5/PySide2 (用于高质量渲染)
        try:
            import PyQt5
            backends.append('qt')
        except ImportError:
            try:
                import PySide2
                backends.append('qt')
            except ImportError:
                pass
        
        return backends
    
    def render_with_ezdxf_drawing(self, dxf_path: str, output_pdf: str, 
                                 dpi: int = 300) -> bool:
        """使用ezdxf的drawing addon渲染"""
        try:
            from ezdxf.addons.drawing import RenderContext, Frontend
            from ezdxf.addons.drawing.matplotlib import MatplotlibBackend
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_pdf import PdfPages
            
            # 加载DXF文档
            doc = ezdxf.readfile(dxf_path)
            msp = doc.modelspace()
            
            # 创建渲染上下文
            ctx = RenderContext(doc)
            
            # 计算图纸边界
            try:
                bbox = ezdxf.bbox.extents(msp)
                if bbox.has_data:
                    width = bbox.size.x
                    height = bbox.size.y
                    center = bbox.center
                else:
                    # 默认边界
                    width, height = 1000, 1000
                    center = (0, 0)
            except:
                width, height = 1000, 1000
                center = (0, 0)
            
            # 设置合适的图形尺寸
            aspect_ratio = width / height if height > 0 else 1
            if aspect_ratio > 1:
                fig_width = 16
                fig_height = 16 / aspect_ratio
            else:
                fig_height = 16
                fig_width = 16 * aspect_ratio
            
            # 创建matplotlib后端
            fig, ax = plt.subplots(figsize=(fig_width, fig_height))
            backend = MatplotlibBackend(ax)
            
            # 创建前端并渲染
            frontend = Frontend(ctx, backend)
            frontend.draw_layout(msp, finalize=True)
            
            # 设置显示属性
            ax.set_aspect('equal')
            ax.set_facecolor('white')
            fig.patch.set_facecolor('white')
            
            # 移除坐标轴
            ax.set_xticks([])
            ax.set_yticks([])
            for spine in ax.spines.values():
                spine.set_visible(False)
            
            # 保存为PDF
            with PdfPages(output_pdf) as pdf:
                pdf.savefig(fig, bbox_inches='tight', dpi=dpi,
                           facecolor='white', edgecolor='none')
            
            plt.close(fig)
            
            print(f"✅ ezdxf drawing渲染完成: {os.path.basename(output_pdf)}")
            return True
            
        except Exception as e:
            print(f"❌ ezdxf drawing渲染失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def render_with_custom_matplotlib(self, dxf_path: str, output_pdf: str,
                                    dpi: int = 300) -> bool:
        """使用自定义matplotlib渲染（增强版）"""
        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_pdf import PdfPages
            from matplotlib.patches import Circle, Arc, Polygon, Rectangle
            import matplotlib.patches as patches
            
            # 加载DXF文档
            doc = ezdxf.readfile(dxf_path)
            msp = doc.modelspace()
            
            # 分析所有实体，获取更准确的边界
            bounds = self._analyze_entities_bounds(msp)
            if not bounds:
                print("⚠️  无法计算图纸边界，使用默认设置")
                bounds = {'min_x': 0, 'min_y': 0, 'max_x': 1000, 'max_y': 1000}
            
            width = bounds['max_x'] - bounds['min_x']
            height = bounds['max_y'] - bounds['min_y']
            
            # 设置图形尺寸
            aspect_ratio = width / height if height > 0 else 1
            if aspect_ratio > 1:
                fig_width = 20
                fig_height = 20 / aspect_ratio
            else:
                fig_height = 20
                fig_width = 20 * aspect_ratio
            
            # 创建图形
            fig, ax = plt.subplots(figsize=(fig_width, fig_height))
            fig.patch.set_facecolor('white')
            ax.set_facecolor('white')
            
            # 渲染所有实体
            entity_stats = {}
            for entity in msp:
                entity_type = entity.dxftype()
                entity_stats[entity_type] = entity_stats.get(entity_type, 0) + 1
                
                try:
                    self._render_entity_advanced(ax, entity)
                except Exception as e:
                    print(f"渲染实体失败 {entity_type}: {e}")
                    continue
            
            # 设置显示范围
            margin = max(width, height) * 0.02
            ax.set_xlim(bounds['min_x'] - margin, bounds['max_x'] + margin)
            ax.set_ylim(bounds['min_y'] - margin, bounds['max_y'] + margin)
            ax.set_aspect('equal')
            
            # 美化显示
            ax.set_xticks([])
            ax.set_yticks([])
            for spine in ax.spines.values():
                spine.set_visible(False)
            
            # 保存PDF
            with PdfPages(output_pdf) as pdf:
                pdf.savefig(fig, bbox_inches='tight', dpi=dpi,
                           facecolor='white', edgecolor='none')
            
            plt.close(fig)
            
            print(f"✅ 自定义matplotlib渲染完成")
            print(f"   实体统计: {entity_stats}")
            return True
            
        except Exception as e:
            print(f"❌ 自定义matplotlib渲染失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _analyze_entities_bounds(self, msp):
        """分析实体边界"""
        min_x, min_y = float('inf'), float('inf')
        max_x, max_y = float('-inf'), float('-inf')
        found_entities = False
        
        for entity in msp:
            try:
                bounds = self._get_entity_bounds(entity)
                if bounds:
                    min_x = min(min_x, bounds[0])
                    min_y = min(min_y, bounds[1])
                    max_x = max(max_x, bounds[2])
                    max_y = max(max_y, bounds[3])
                    found_entities = True
            except:
                continue
        
        if not found_entities:
            return None
            
        return {
            'min_x': min_x, 'min_y': min_y,
            'max_x': max_x, 'max_y': max_y
        }
    
    def _get_entity_bounds(self, entity):
        """获取单个实体的边界"""
        entity_type = entity.dxftype()
        
        try:
            if entity_type == 'LINE':
                start, end = entity.dxf.start, entity.dxf.end
                return [min(start.x, end.x), min(start.y, end.y),
                       max(start.x, end.x), max(start.y, end.y)]
            
            elif entity_type in ['TEXT', 'MTEXT']:
                insert = entity.dxf.insert
                height = getattr(entity.dxf, 'height', 10)
                width_est = height * len(getattr(entity.dxf, 'text', '')) * 0.6
                return [insert.x, insert.y, insert.x + width_est, insert.y + height]
            
            elif entity_type == 'LWPOLYLINE':
                points = list(entity.get_points())
                if points:
                    xs, ys = zip(*[(p[0], p[1]) for p in points])
                    return [min(xs), min(ys), max(xs), max(ys)]
            
            elif entity_type == 'CIRCLE':
                center = entity.dxf.center
                radius = entity.dxf.radius
                return [center.x - radius, center.y - radius,
                       center.x + radius, center.y + radius]
            
            elif entity_type == 'ARC':
                center = entity.dxf.center
                radius = entity.dxf.radius
                return [center.x - radius, center.y - radius,
                       center.x + radius, center.y + radius]
            
            elif entity_type == 'INSERT':
                insert = entity.dxf.insert
                scale = getattr(entity.dxf, 'xscale', 1)
                size = 50 * scale
                return [insert.x, insert.y, insert.x + size, insert.y + size]
            
        except:
            pass
        
        return None
    
    def _render_entity_advanced(self, ax, entity):
        """高级实体渲染"""
        entity_type = entity.dxftype()
        
        # 获取样式属性
        color = self._get_entity_color(entity)
        linewidth = self._get_entity_linewidth(entity)
        linestyle = self._get_entity_linestyle(entity)
        
        if entity_type == 'LINE':
            start, end = entity.dxf.start, entity.dxf.end
            ax.plot([start.x, end.x], [start.y, end.y],
                   color=color, linewidth=linewidth, linestyle=linestyle)
        
        elif entity_type in ['TEXT', 'MTEXT']:
            insert = entity.dxf.insert
            text = getattr(entity.dxf, 'text', '')
            height = getattr(entity.dxf, 'height', 10)
            rotation = getattr(entity.dxf, 'rotation', 0)
            
            # 调整字体大小
            fontsize = max(2, min(height/2, 12))
            
            ax.text(insert.x, insert.y, text,
                   fontsize=fontsize, color=color,
                   rotation=np.degrees(rotation),
                   ha='left', va='bottom')
        
        elif entity_type == 'LWPOLYLINE':
            points = list(entity.get_points())
            if len(points) > 1:
                xs, ys = zip(*[(p[0], p[1]) for p in points])
                if entity.closed:
                    xs = xs + (xs[0],)
                    ys = ys + (ys[0],)
                ax.plot(xs, ys, color=color, linewidth=linewidth, linestyle=linestyle)
        
        elif entity_type == 'CIRCLE':
            center = entity.dxf.center
            radius = entity.dxf.radius
            circle = Circle((center.x, center.y), radius,
                          fill=False, color=color, linewidth=linewidth,
                          linestyle=linestyle)
            ax.add_patch(circle)
        
        elif entity_type == 'ARC':
            center = entity.dxf.center
            radius = entity.dxf.radius
            start_angle = np.degrees(entity.dxf.start_angle)
            end_angle = np.degrees(entity.dxf.end_angle)
            
            arc = Arc((center.x, center.y), 2*radius, 2*radius,
                     theta1=start_angle, theta2=end_angle,
                     color=color, linewidth=linewidth, linestyle=linestyle)
            ax.add_patch(arc)
        
        # 可以继续添加更多实体类型的支持...
    
    def _get_entity_color(self, entity):
        """获取实体颜色"""
        try:
            color_index = entity.dxf.color
            # AutoCAD标准颜色映射
            color_map = {
                1: '#FF0000',  # 红色
                2: '#FFFF00',  # 黄色
                3: '#00FF00',  # 绿色
                4: '#00FFFF',  # 青色
                5: '#0000FF',  # 蓝色
                6: '#FF00FF',  # 洋红
                7: '#FFFFFF',  # 白色
                8: '#808080',  # 灰色
                9: '#C0C0C0',  # 浅灰
            }
            return color_map.get(color_index, '#000000')
        except:
            return '#000000'
    
    def _get_entity_linewidth(self, entity):
        """获取实体线宽"""
        try:
            lineweight = getattr(entity.dxf, 'lineweight', -1)
            if lineweight > 0:
                return max(0.1, lineweight / 100)
            else:
                return 0.8
        except:
            return 0.8
    
    def _get_entity_linestyle(self, entity):
        """获取实体线型"""
        try:
            linetype = getattr(entity.dxf, 'linetype', 'CONTINUOUS')
            linestyle_map = {
                'CONTINUOUS': '-',
                'DASHED': '--',
                'DOTTED': ':',
                'DASHDOT': '-.',
            }
            return linestyle_map.get(linetype.upper(), '-')
        except:
            return '-'
    
    def render_dxf_to_pdf(self, dxf_path: str, output_pdf: str = None,
                         method: str = 'auto', dpi: int = 300) -> Optional[str]:
        """渲染DXF到PDF"""
        if output_pdf is None:
            output_pdf = dxf_path.replace('.dxf', '_advanced.pdf')
        
        print(f"🔄 渲染 {os.path.basename(dxf_path)} -> {os.path.basename(output_pdf)}")
        
        # 选择渲染方法
        if method == 'auto':
            if 'ezdxf_drawing' in self.supported_backends:
                method = 'ezdxf_drawing'
            elif 'matplotlib' in self.supported_backends:
                method = 'matplotlib'
            else:
                print("❌ 没有可用的渲染后端")
                return None
        
        success = False
        
        if method == 'ezdxf_drawing':
            success = self.render_with_ezdxf_drawing(dxf_path, output_pdf, dpi)
        elif method == 'matplotlib':
            success = self.render_with_custom_matplotlib(dxf_path, output_pdf, dpi)
        
        if success and os.path.exists(output_pdf):
            return output_pdf
        else:
            return None
