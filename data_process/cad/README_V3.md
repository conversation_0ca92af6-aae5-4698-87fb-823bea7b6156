# DXF全面解析器 V3

基于structured_v2版本优化的全面DXF图纸解析器，提供更完整的CAD图纸格式信息解析。

## 主要特性

### 🆕 V3版本新增功能

1. **更全面的实体信息提取**
   - 支持更多实体类型（TEXT, MTEXT, LINE, CIRCLE, ARC, SPLINE, HATCH等）
   - 提取完整的几何信息（坐标、尺寸、角度等）
   - 包含原始DXF属性信息

2. **可配置的解析选项**
   - `include_coordinates`: 控制是否包含坐标信息
   - `include_raw_data`: 控制是否包含原始数据
   - 灵活控制输出文件大小和信息详细程度

3. **增强的文档元数据**
   - 图层详细信息（颜色、线型、状态等）
   - 线型、文字样式、标注样式信息
   - 块定义详细信息
   - 布局信息

4. **高级图框检测**
   - 实体图框检测
   - 基于文本分布的虚拟图框
   - 四象限分析支持多图稿识别

5. **智能内容分析**
   - 消防系统关键词识别
   - 电气系统关键词识别
   - 设备编号和尺寸标注识别
   - 按图层和类型统计

6. **批量处理支持**
   - 支持单文件和文件夹批量处理
   - 进度显示和错误处理
   - 详细的处理统计信息

## 安装依赖

```bash
pip install ezdxf tqdm
```

## 使用方法

### 1. 命令行使用

```bash
# 基本用法（使用默认配置）
python dxf_parser_structured_v3.py <输入路径>

# 指定输出目录
python dxf_parser_structured_v3.py <输入路径> <输出目录>

# 完整参数
python dxf_parser_structured_v3.py <输入路径> <输出目录> <包含坐标> <包含原始数据>

# 示例
python dxf_parser_structured_v3.py ./test.dxf
python dxf_parser_structured_v3.py ./dxf_folder ./output true false
```

### 2. 编程使用

```python
from dxf_parser_structured_v3 import DXFComprehensiveParser, process_dxf_comprehensive

# 单文件解析
parser = DXFComprehensiveParser(
    "test.dxf", 
    include_coordinates=True,    # 包含坐标信息
    include_raw_data=False       # 不包含原始数据（精简模式）
)
result = parser.generate_comprehensive_output()

# 批量处理
results = process_dxf_comprehensive(
    "./dxf_folder",              # 输入路径
    output_base="./output",      # 输出目录
    include_coordinates=True,    # 包含坐标
    include_raw_data=True        # 包含原始数据
)
```

## 配置选项说明

### include_coordinates (坐标选项)
- `True`: 包含所有坐标信息（插入点、起点、终点、包围盒等）
- `False`: 不包含坐标信息，适合纯文本分析
- 影响：坐标信息会显著增加输出文件大小

### include_raw_data (原始数据选项)
- `True`: 包含原始DXF属性和详细实体数据
- `False`: 只包含处理后的结构化信息
- 影响：原始数据包含更多细节但文件更大

## 输出结构

```json
{
  "解析配置": {
    "包含坐标": true,
    "包含原始数据": true
  },
  "文档元数据": {
    "文件信息": {...},
    "图层信息": {...},
    "线型信息": {...},
    "文字样式": {...},
    "标注样式": {...},
    "块定义": {...},
    "布局信息": {...}
  },
  "实体统计": {
    "总实体数": 1234,
    "文本实体数": 456,
    "几何实体数": 789,
    "按类型统计": {...},
    "按图层统计": {...}
  },
  "图稿信息": {
    "图稿数量": 2,
    "图稿列表": [...]
  },
  "内容分析": {
    "消防系统": {...},
    "电气系统": {...},
    "设备编号": {...},
    "尺寸标注": {...}
  },
  "详细实体数据": {
    "文本实体": [...],
    "几何实体": [...],
    "标注实体": [...],
    "块实体": [...]
  }
}
```

## 使用场景

### 1. 完整信息提取（推荐用于详细分析）
```python
parser = DXFComprehensiveParser(
    "drawing.dxf",
    include_coordinates=True,
    include_raw_data=True
)
```

### 2. 文本内容分析（适合关键词搜索）
```python
parser = DXFComprehensiveParser(
    "drawing.dxf",
    include_coordinates=False,
    include_raw_data=False
)
```

### 3. 几何分析（适合空间分析）
```python
parser = DXFComprehensiveParser(
    "drawing.dxf",
    include_coordinates=True,
    include_raw_data=False
)
```

## 输出文件命名

- 单文件处理：`原文件名.json`
- 批量处理：在输入目录同级创建 `原目录名_parsed_v3` 文件夹
- 每个DXF文件对应一个同名的JSON文件

## 错误处理

- 文件读取错误：返回错误信息而不是崩溃
- 实体解析错误：记录错误但继续处理其他实体
- 批量处理：统计成功和失败的文件数量

## 性能考虑

- 包含坐标信息会增加处理时间和文件大小
- 包含原始数据会显著增加内存使用
- 大型DXF文件建议分步处理或使用精简模式

## 测试

运行测试脚本：
```bash
python test_v3_parser.py
```

## 与V2版本的区别

| 功能 | V2版本 | V3版本 |
|------|--------|--------|
| 实体类型支持 | 基础文本实体 | 全面支持各种实体类型 |
| 坐标信息 | 总是包含 | 可选包含 |
| 原始数据 | 部分包含 | 可选完整包含 |
| 几何信息 | 基础信息 | 详细几何属性 |
| 文档元数据 | 基础信息 | 完整元数据 |
| 批量处理 | 不支持 | 完整支持 |
| 错误处理 | 基础 | 增强的错误处理 |

## 注意事项

1. 确保有足够的磁盘空间存储输出文件
2. 大型文件处理时注意内存使用
3. 根据需求选择合适的配置选项
4. 定期清理输出目录避免文件堆积
