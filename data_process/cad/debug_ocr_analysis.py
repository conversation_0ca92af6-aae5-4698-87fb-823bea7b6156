#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试OCR版面分析脚本
用于调试和优化OCR识别效果
"""

import os
import sys
import cv2
import numpy as np
import json
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def debug_pdf_to_image_conversion(dxf_path: str):
    """调试PDF转图像的过程"""
    print("🔍 调试PDF转图像转换")
    print("-" * 40)
    
    try:
        from dxf_parser_structured_v4 import DXFLayoutAnalyzer
        
        analyzer = DXFLayoutAnalyzer()
        
        # 1. DXF转PDF
        print("1️⃣ DXF转PDF...")
        pdf_path = analyzer.dxf_to_pdf(dxf_path)
        if not pdf_path or not os.path.exists(pdf_path):
            print("❌ PDF转换失败")
            return None
        
        print(f"✅ PDF生成成功: {os.path.basename(pdf_path)}")
        print(f"   文件大小: {os.path.getsize(pdf_path) / 1024:.1f} KB")
        
        # 2. PDF转图像
        print("\n2️⃣ PDF转图像...")
        images = analyzer.pdf_to_image(pdf_path)
        if not images:
            print("❌ 图像转换失败")
            return None
        
        print(f"✅ 图像转换成功: {len(images)}页")
        
        # 3. 分析图像属性
        for i, image in enumerate(images):
            print(f"\n📊 页面{i+1}图像分析:")
            print(f"   尺寸: {image.shape}")
            print(f"   数据类型: {image.dtype}")
            print(f"   像素值范围: {image.min()} ~ {image.max()}")
            
            # 保存调试图像
            debug_image_path = f"debug_page_{i+1}.png"
            cv2.imwrite(debug_image_path, image)
            print(f"   调试图像已保存: {debug_image_path}")
            
            # 图像预处理分析
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            print(f"   灰度图像统计: 均值={gray.mean():.1f}, 标准差={gray.std():.1f}")
            
            # 检查是否有足够的对比度
            if gray.std() < 10:
                print("   ⚠️  图像对比度较低，可能影响OCR识别")
            
            # 检查图像是否过大
            if image.shape[0] * image.shape[1] > 10000000:  # 10M像素
                print("   ⚠️  图像尺寸较大，可能影响OCR处理速度")
        
        return images
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_ocr_recognition(images):
    """调试OCR识别过程"""
    print("\n🔍 调试OCR识别")
    print("-" * 40)
    
    try:
        from paddleocr import PaddleOCR
        
        # 初始化OCR
        print("1️⃣ 初始化PaddleOCR...")
        ocr = PaddleOCR(use_angle_cls=True, lang='ch')
        print("✅ OCR初始化成功")
        
        for i, image in enumerate(images):
            print(f"\n2️⃣ 分析页面{i+1}...")
            
            # 尝试不同的图像预处理
            test_images = {
                'original': image,
                'gray': cv2.cvtColor(image, cv2.COLOR_BGR2GRAY),
                'enhanced': enhance_image_for_ocr(image),
                'resized': resize_image_for_ocr(image)
            }
            
            for method, test_image in test_images.items():
                print(f"\n   测试方法: {method}")
                try:
                    # 如果是灰度图，转换为3通道
                    if len(test_image.shape) == 2:
                        test_image = cv2.cvtColor(test_image, cv2.COLOR_GRAY2BGR)
                    
                    result = ocr.ocr(test_image)
                    
                    if result and len(result) > 0 and result[0]:
                        text_count = len(result[0])
                        print(f"   ✅ 识别到 {text_count} 个文本区域")
                        
                        # 显示前几个识别结果
                        for j, line in enumerate(result[0][:3]):
                            if line:
                                bbox = line[0]
                                text_info = line[1]
                                if text_info and len(text_info) >= 2:
                                    text_content = text_info[0]
                                    confidence = text_info[1]
                                    print(f"     文本{j+1}: '{text_content}' (置信度: {confidence:.3f})")
                        
                        if text_count > 3:
                            print(f"     ... 还有 {text_count - 3} 个文本")
                        
                        # 保存最佳结果
                        if method == 'enhanced' or (method == 'original' and text_count > 0):
                            save_ocr_debug_result(result, f"ocr_result_page_{i+1}_{method}.json")
                    else:
                        print(f"   ❌ 未识别到文本")
                        
                except Exception as e:
                    print(f"   ❌ OCR识别失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def enhance_image_for_ocr(image):
    """增强图像以提高OCR识别率"""
    # 转换为灰度图
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()
    
    # 应用CLAHE (对比度限制自适应直方图均衡化)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    
    # 高斯模糊去噪
    enhanced = cv2.GaussianBlur(enhanced, (1, 1), 0)
    
    # 转换回BGR
    enhanced = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
    
    return enhanced

def resize_image_for_ocr(image, max_size=2048):
    """调整图像大小以优化OCR处理"""
    height, width = image.shape[:2]
    
    if max(height, width) > max_size:
        if height > width:
            new_height = max_size
            new_width = int(width * max_size / height)
        else:
            new_width = max_size
            new_height = int(height * max_size / width)
        
        resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
        return resized
    
    return image

def save_ocr_debug_result(result, filename):
    """保存OCR调试结果"""
    try:
        # 转换结果为可序列化的格式
        serializable_result = []
        if result and len(result) > 0 and result[0]:
            for line in result[0]:
                if line:
                    bbox = line[0]
                    text_info = line[1]
                    if text_info and len(text_info) >= 2:
                        serializable_result.append({
                            'bbox': bbox,
                            'text': text_info[0],
                            'confidence': float(text_info[1])
                        })
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(serializable_result, f, ensure_ascii=False, indent=2)
        
        print(f"   📁 调试结果已保存: {filename}")
        
    except Exception as e:
        print(f"   ⚠️  保存调试结果失败: {e}")

def main():
    """主函数"""
    print("🔍 OCR版面分析调试工具")
    print("=" * 50)
    
    # 使用测试文件
    dxf_path = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10.dxf"
    
    if not os.path.exists(dxf_path):
        print(f"❌ 测试文件不存在: {dxf_path}")
        return
    
    print(f"📁 测试文件: {os.path.basename(dxf_path)}")
    
    # 1. 调试PDF转图像
    images = debug_pdf_to_image_conversion(dxf_path)
    if not images:
        print("❌ PDF转图像失败，无法继续调试")
        return
    
    # 2. 调试OCR识别
    success = debug_ocr_recognition(images)
    if success:
        print("\n🎉 OCR调试完成!")
        print("请查看生成的调试文件:")
        print("  - debug_page_*.png: 调试图像")
        print("  - ocr_result_*.json: OCR识别结果")
    else:
        print("\n❌ OCR调试失败")

if __name__ == "__main__":
    main()
