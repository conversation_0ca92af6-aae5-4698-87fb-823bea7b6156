import ezdxf
import json
from collections import defaultdict


def extract_text_from_entity(entity):
    """
    从实体中提取文本内容，支持多种文本类型
    """
    entity_type = entity.dxftype()
    text_content = ""

    try:
        if entity_type == 'TEXT':
            text_content = entity.dxf.text
        elif entity_type == 'MTEXT':
            # MTEXT 可能包含格式化代码，需要清理
            text_content = entity.plain_text()
        elif entity_type == 'ATTRIB':
            # 属性文本
            text_content = entity.dxf.text
        elif entity_type == 'ATTDEF':
            # 属性定义
            text_content = entity.dxf.text
        elif entity_type == 'INSERT':
            # 块插入，可能包含属性
            if hasattr(entity, 'attribs'):
                attrib_texts = []
                for attrib in entity.attribs:
                    if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                        attrib_texts.append(f"{attrib.dxf.tag}: {attrib.dxf.text}")
                text_content = "; ".join(attrib_texts)
    except Exception as e:
        print(f"提取文本时出错 ({entity_type}): {e}")

    return text_content.strip() if text_content else ""


def parse_dxf_to_friendly_json(dxf_path, json_path):
    """
    解析DXF文件，并将其中的实体信息转换为使用中文键的、对大模型友好的JSON格式。
    优化版本：支持更多实体类型，特别是文本相关的实体。

    :param dxf_path: 输入的DXF文件路径
    :param json_path: 输出的JSON文件路径
    """
    try:
        doc = ezdxf.readfile(dxf_path)
    except IOError:
        print(f"错误: 无法找到或打开文件: {dxf_path}")
        return
    except ezdxf.DXFStructureError:
        print(f"错误: 无效或损坏的DXF文件: {dxf_path}")
        return

    msp = doc.modelspace()
    parsed_data = []

    # 统计实体类型
    entity_stats = defaultdict(int)
    text_entities = []  # 收集所有文本实体

    print("开始解析DXF文件...")

    for entity in msp:
        entity_type = entity.dxftype()
        entity_stats[entity_type] += 1

        # 使用描述性的中文作为key
        entity_data = {
            "实体类型": entity_type,
            "所在图层": entity.dxf.layer,
            "颜色索引": entity.dxf.color,
        }

        # 添加更多通用属性
        try:
            if hasattr(entity.dxf, 'linetype'):
                entity_data["线型"] = entity.dxf.linetype
            if hasattr(entity.dxf, 'lineweight'):
                entity_data["线宽"] = entity.dxf.lineweight
        except:
            pass

        # 根据不同实体类型，提取其核心几何信息
        if entity_type == 'LINE':
            entity_data['几何信息'] = {
                "起点坐标": list(entity.dxf.start),
                "终点坐标": list(entity.dxf.end)
            }
        elif entity_type == 'CIRCLE':
            entity_data['几何信息'] = {
                "圆心坐标": list(entity.dxf.center),
                "半径": entity.dxf.radius
            }
        elif entity_type == 'ARC':
            entity_data['几何信息'] = {
                "圆心坐标": list(entity.dxf.center),
                "半径": entity.dxf.radius,
                "起始角度": entity.dxf.start_angle,
                "终止角度": entity.dxf.end_angle
            }
        elif entity_type in ['TEXT', 'MTEXT', 'ATTRIB', 'ATTDEF']:
            # 处理各种文本实体
            text_content = extract_text_from_entity(entity)
            entity_data['文本信息'] = {
                "插入点坐标": list(entity.dxf.insert) if hasattr(entity.dxf, 'insert') else [],
                "文本内容": text_content,
                "文字高度": getattr(entity.dxf, 'height', 0),
                "旋转角度": getattr(entity.dxf, 'rotation', 0)
            }
            if text_content:  # 只收集有内容的文本
                text_entities.append({
                    "类型": entity_type,
                    "图层": entity.dxf.layer,
                    "内容": text_content,
                    "坐标": list(entity.dxf.insert) if hasattr(entity.dxf, 'insert') else []
                })
        elif entity_type == 'INSERT':
            # 处理块插入
            block_info = {
                "块名": entity.dxf.name,
                "插入点坐标": list(entity.dxf.insert),
                "缩放比例": [entity.dxf.xscale, entity.dxf.yscale, entity.dxf.zscale],
                "旋转角度": entity.dxf.rotation
            }

            # 提取块属性
            text_content = extract_text_from_entity(entity)
            if text_content:
                block_info["属性文本"] = text_content
                text_entities.append({
                    "类型": "INSERT_ATTRIB",
                    "图层": entity.dxf.layer,
                    "内容": text_content,
                    "坐标": list(entity.dxf.insert)
                })

            entity_data['块信息'] = block_info
        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
            try:
                points = [list(p) for p in entity.get_points()]
                entity_data['几何信息'] = {
                    "顶点坐标列表": points,
                    "是否闭合": entity.is_closed
                }
            except:
                entity_data['几何信息'] = {"错误": "无法获取顶点信息"}
        elif entity_type == 'HATCH':
            # 填充实体
            entity_data['填充信息'] = {
                "填充图案": getattr(entity.dxf, 'pattern_name', ''),
                "填充类型": getattr(entity.dxf, 'hatch_style', 0)
            }
        elif entity_type == 'SPLINE':
            # 样条曲线
            try:
                entity_data['几何信息'] = {
                    "控制点数量": len(entity.control_points),
                    "度数": entity.dxf.degree,
                    "是否闭合": entity.closed
                }
            except:
                entity_data['几何信息'] = {"错误": "无法获取样条信息"}
        elif entity_type == 'DIMENSION':
            # 尺寸标注
            try:
                dim_text = getattr(entity.dxf, 'text', '')
                entity_data['尺寸信息'] = {
                    "尺寸文本": dim_text,
                    "尺寸类型": entity.dimtype
                }
                if dim_text:
                    text_entities.append({
                        "类型": "DIMENSION",
                        "图层": entity.dxf.layer,
                        "内容": dim_text,
                        "坐标": []
                    })
            except:
                entity_data['尺寸信息'] = {"错误": "无法获取尺寸信息"}
        elif entity_type == 'LEADER':
            # 引线
            try:
                entity_data['引线信息'] = {
                    "顶点数量": len(entity.vertices) if hasattr(entity, 'vertices') else 0
                }
            except:
                entity_data['引线信息'] = {"错误": "无法获取引线信息"}

        parsed_data.append(entity_data)

    # 打印统计信息
    print(f"\n实体类型统计:")
    for entity_type, count in sorted(entity_stats.items()):
        print(f"  {entity_type}: {count}")

    print(f"\n找到 {len(text_entities)} 个包含文本内容的实体")

    # 按图层分组文本内容
    text_by_layer = defaultdict(list)
    for text_entity in text_entities:
        text_by_layer[text_entity["图层"]].append(text_entity)

    # 为了让LLM更好地理解整个文件，我们创建一个包含元数据和实体数据的根对象
    final_output = {
        "文件元数据": {
            "文件名": dxf_path,
            "DXF版本": doc.dxfversion,
            "单位": "未指定 (通常为毫米)",
            "坐标系": "世界坐标系 (WCS)",
            "实体统计": dict(entity_stats),
            "文本实体数量": len(text_entities)
        },
        "文本内容汇总": {
            "按图层分组": {layer: [t["内容"] for t in texts]
                         for layer, texts in text_by_layer.items()},
            "所有文本内容": [t["内容"] for t in text_entities if t["内容"]]
        },
        "实体列表": parsed_data
    }

    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(final_output, f, ensure_ascii=False, indent=4)

    print(f"成功将 {dxf_path} 解析并保存至 {json_path}")
    print(f"包含 {len(parsed_data)} 个实体，其中 {len([t for t in text_entities if t['内容']])} 个包含有效文本内容")


def extract_all_text_comprehensive(dxf_path: str) -> dict:
    """
    全面提取DXF文件中的所有文本内容，包括块定义
    """
    try:
        doc = ezdxf.readfile(dxf_path)
    except Exception as e:
        return {"错误": f"无法读取DXF文件: {e}"}

    all_texts = []

    def extract_from_space(entities, space_name):
        for entity in entities:
            text_content = extract_text_from_entity(entity)
            if text_content:
                all_texts.append({
                    "空间": space_name,
                    "实体类型": entity.dxftype(),
                    "图层": entity.dxf.layer,
                    "文本内容": text_content,
                    "坐标": list(entity.dxf.insert) if hasattr(entity.dxf, 'insert') else []
                })

    # 提取模型空间
    extract_from_space(doc.modelspace(), "模型空间")

    # 提取图纸空间
    for layout in doc.layouts:
        if layout.name != 'Model':
            extract_from_space(layout, f"图纸空间-{layout.name}")

    # 提取块定义
    for block in doc.blocks:
        if not block.name.startswith('*'):
            extract_from_space(block, f"块定义-{block.name}")

    return {
        "所有文本": [t["文本内容"] for t in all_texts],
        "详细信息": all_texts,
        "总数量": len(all_texts)
    }


if __name__ == '__main__':
    # 使用示例
    dxf_file = '/Users/<USER>/Downloads/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图.dxf'  # 替换为你的DXF文件路径

    # 运行原始解析
    json_file = 'output.json'
    parse_dxf_to_friendly_json(dxf_file, json_file)

    # 运行全面文本提取
    # print("\n" + "="*50)
    # print("运行全面文本提取...")
    # comprehensive_result = extract_all_text_comprehensive(dxf_file)
    #
    # # 保存全面文本结果
    # comprehensive_file = 'output_comprehensive.json'
    # with open(comprehensive_file, 'w', encoding='utf-8') as f:
    #     json.dump(comprehensive_result, f, ensure_ascii=False, indent=2)
    #
    # # 搜索特定内容
    # search_terms = ["消防设备应急电源", "消防", "应急", "电源"]
    # print(f"\n搜索结果:")
    # for term in search_terms:
    #     matches = [text for text in comprehensive_result["所有文本"] if term in text]
    #     print(f"  '{term}': {len(matches)} 条匹配")
    #     for match in matches[:3]:  # 显示前3条
    #         print(f"    - {match}")
    #     if len(matches) > 3:
    #         print(f"    ... 还有 {len(matches) - 3} 条")
    #
    # print(f"\n全面解析结果已保存到: {comprehensive_file}")
    # print(f"总共提取到 {comprehensive_result['总数量']} 条文本内容")