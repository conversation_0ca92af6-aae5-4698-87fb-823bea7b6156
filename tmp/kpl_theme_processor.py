from typing import Dict, Any, List
import requests
import json
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class KplThemeProcessor:
    """开盘啦题材处理类，专注于获取和清洗题材数据"""
    
    def __init__(self):
        """初始化API基础配置"""
        self.base_url = "https://applhb.longhuvip.com/w1/api/index.php"
        self.headers = {
            "PhoneOSNew": "1",
            "DeviceID": "625d4c1d-cda0-3e23-9bf9-27cbce5c49db",
            "VerSion": "********",
            "Token": "9d8a4a57fbb0421c5f7837ceb1f6d811",
            "UserID": "2511251",
            "apiv": "w38"
        }
        logger.debug("KplThemeProcessor 初始化完成")
    
    def get_theme_detail(self, theme_id: str) -> Dict[str, Any]:
        """获取单个题材详情"""
        params = {
            "a": "InfoGet",
            "c": "Theme",
            "ID": theme_id,
            **self.headers
        }
        
        try:
            response = requests.get(self.base_url, params=params)
            response.raise_for_status()
            data = response.json()
            logger.debug(f"题材详情响应: {data}")
            return data
        except requests.RequestException as e:
            logger.error(f"请求题材详情失败: {str(e)}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"解析题材详情JSON失败: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"获取题材详情时发生未知错误: {str(e)}")
            raise
    
    def get_theme_intro(self, theme_id: str) -> Dict[str, Any]:
        """获取题材详情介绍"""
        params = {
            "a": "InfoIntr",
            "c": "Theme",
            "ID": theme_id,
            **self.headers
        }
        
        try:
            response = requests.get(self.base_url, params=params)
            response.raise_for_status()
            data = response.json()
            logger.debug(f"题材介绍响应: {data}")
            return data
        except Exception as e:
            logger.error(f"获取题材介绍失败: {str(e)}")
            raise
    
    def clean_theme_data(self, theme_data: Dict[str, Any], intro_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """清洗题材数据，只保留需要的字段"""
        if not theme_data:
            return None
        
        # 提取主要字段
        cleaned_data = {
            'Id': theme_data.get('ID'),
            'Name': theme_data.get('Name'),
            'UpdateTime': theme_data.get('UpdateTime'),
            'Type': theme_data.get('TypeName'),
            'hot_value': theme_data.get('HotValue'),
            'MainStock': [],
            'RelatedIndustries': []
        }
        
        # 提取主要股票
        if 'MainStock' in theme_data and isinstance(theme_data['MainStock'], list):
            for stock in theme_data['MainStock']:
                if isinstance(stock, dict):
                    cleaned_data['MainStock'].append({
                        'Code': stock.get('Code'),
                        'Name': stock.get('Name'),
                        'Price': stock.get('Price')
                    })
        
        # 提取相关行业
        if 'RelatedIndustries' in theme_data and isinstance(theme_data['RelatedIndustries'], list):
            for industry in theme_data['RelatedIndustries']:
                if isinstance(industry, dict):
                    cleaned_data['RelatedIndustries'].append({
                        'ID': industry.get('ID'),
                        'Name': industry.get('Name')
                    })
        
        # 添加介绍信息
        if intro_data:
            cleaned_data['Intro'] = intro_data.get('Introduction', '')
        
        return cleaned_data
    
    def get_user_themes(self) -> List[Dict[str, Any]]:
        """获取用户自选题材列表并清洗"""
        params = {
            "a": "USTList",
            "c": "ThemeSubscribe",
            **self.headers
        }
        
        try:
            response = requests.get(self.base_url, params=params)
            data = response.json()
            
            result = []
            if 'List' in data and isinstance(data['List'], list):
                for theme in data['List']:
                    cleaned_theme = {
                        'Id': theme.get('ID'),
                        'Name': theme.get('Name'),
                        'Type': theme.get('TypeName')
                    }
                    result.append(cleaned_theme)
            
            return result
        except Exception as e:
            logger.error(f"获取自选题材列表失败: {str(e)}")
            raise
    
    def get_theme_details_clean(self, start_id: int, end_id: int, include_intro: bool = True) -> Dict[str, Any]:
        """批量获取并清洗题材详情数据"""
        logger.info(f"开始批量获取题材详情 (ID范围: {start_id}-{end_id})")
        
        results = {
            'success': [],
            'errors': [],
            'meta': {
                'start_id': start_id,
                'end_id': end_id,
                'include_intro': include_intro,
                'process_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        success_count = 0
        empty_name_count = 0
        error_count = 0
        
        for theme_id in range(start_id, end_id + 1):
            try:
                logger.info(f"正在获取题材ID: {theme_id}")
                theme_data = self.get_theme_detail(str(theme_id))
                
                # 检查返回数据中的Name字段
                theme_name = theme_data.get('Name', '').strip()
                if not theme_name:
                    logger.info(f"题材ID {theme_id} 的Name为空，跳过")
                    empty_name_count += 1
                    continue
                
                # 获取介绍信息（可选）
                intro_data = None
                if include_intro:
                    try:
                        intro_data = self.get_theme_intro(str(theme_id))
                    except Exception as e:
                        logger.warning(f"获取题材ID {theme_id} 介绍失败: {str(e)}")
                
                # 清洗数据
                cleaned_data = self.clean_theme_data(theme_data, intro_data)
                
                if cleaned_data:
                    results['success'].append(cleaned_data)
                    success_count += 1
                    logger.info(f"成功获取并清洗题材ID: {theme_id} - {theme_name}")
                else:
                    logger.warning(f"题材ID {theme_id} 返回数据异常")
                    results['errors'].append({
                        'id': theme_id,
                        'error': '返回数据异常或清洗失败'
                    })
                    error_count += 1
                
            except Exception as e:
                logger.error(f"获取题材ID {theme_id} 失败: {str(e)}")
                results['errors'].append({
                    'id': theme_id,
                    'error': str(e)
                })
                error_count += 1
            
            # 添加短暂延迟以避免请求过于频繁
            import time
            time.sleep(0.5)
        
        # 添加统计信息
        results['stats'] = {
            'success_count': success_count,
            'empty_name_count': empty_name_count,
            'error_count': error_count,
            'total_processed': (end_id - start_id + 1)
        }
        
        logger.info(f"批量获取题材详情完成: 成功 {success_count}，空名称 {empty_name_count}，失败 {error_count}")
        
        return results

# 如果直接运行此脚本
if __name__ == "__main__":
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='开盘啦题材数据处理工具')
    parser.add_argument('--start', type=int, default=1, help='起始题材ID')
    parser.add_argument('--end', type=int, default=10, help='结束题材ID')
    parser.add_argument('--no-intro', action='store_true', help='不获取题材介绍')
    parser.add_argument('--output', type=str, help='输出结果到JSON文件（可选）')
    parser.add_argument('--favorites', action='store_true', help='获取自选题材列表')
    args = parser.parse_args()
    
    # 初始化处理器
    processor = KplThemeProcessor()
    
    if args.favorites:
        # 获取自选题材列表
        themes = processor.get_user_themes()
        print(f"\n获取到 {len(themes)} 个自选题材:")
        for i, theme in enumerate(themes):
            print(f"[{i+1}] ID: {theme['Id']} - {theme['Name']} ({theme['Type']})")
        
        # 如果指定了输出文件，保存结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(themes, f, ensure_ascii=False, indent=2)
            print(f"\n自选题材列表已保存到: {args.output}")
    else:
        # 批量获取题材详情
        include_intro = not args.no_intro
        results = processor.get_theme_details_clean(args.start, args.end, include_intro)
        
        # 打印统计信息
        print(f"\n题材数据获取完成:")
        print(f"- 成功获取: {results['stats']['success_count']}")
        print(f"- 空名称跳过: {results['stats']['empty_name_count']}")
        print(f"- 获取失败: {results['stats']['error_count']}")
        print(f"- 总计处理: {results['stats']['total_processed']}")
        
        # 如果指定了输出文件，保存结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"- 结果已保存到: {args.output}")
        else:
            # 打印前5个结果
            if results['success']:
                print("\n前5个题材数据示例:")
                for i, theme in enumerate(results['success'][:5]):
                    print(f"\n[{i+1}] ID: {theme['Id']} - {theme['Name']} ({theme['Type']})")
                    print(f"    热度: {theme['hot_value']}")
                    print(f"    主要股票: {', '.join(['{0}({1})'.format(s['Name'], s['Code']) for s in theme['MainStock'][:3]]) if theme['MainStock'] else '无'}") 