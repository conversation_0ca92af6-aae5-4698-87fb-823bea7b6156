from typing import Dict, Any, List
import requests
import json
import re
from datetime import datetime

class KplReportProcessorSimple:
    """开盘啦研报处理类 - 简化版，专注于获取和清洗研报数据"""
    
    def __init__(self):
        """初始化API基础配置"""
        self.base_url = "https://apparticle.longhuvip.com/w1/api/index.php"
        self.headers = {
            "PhoneOSNew": "1",
            "DeviceID": "625d4c1d-cda0-3e23-9bf9-27cbce5c49db",
            "VerSion": "********",
            "Token": "9d8a4a57fbb0421c5f7837ceb1f6d811",
            "UserID": "2511251",
            "apiv": "w38"
        }
        print("KplReportProcessorSimple 初始化完成")

    def get_report_detail(self, report_id: str) -> Dict[str, Any]:
        """获取单个研报详情"""
        params = {
            "a": "GetInfo",
            "c": "ForumsTuyere",
            "TuyID": report_id,
            **self.headers
        }
        
        response = requests.get(self.base_url, params=params, timeout=5)
        if response.status_code != 200:
            return None
            
        data = response.json()

        # 检查返回数据中是否存在Msg字段
        if 'Msg' not in data:
            return None

        # 添加文章URL
        data['Msg']['art_url'] = f'https://apppage.longhuvip.com/w41/share/summary.html?id={report_id}'

        return data
    
    def _extract_stock_codes(self, content: str) -> List[str]:
        """从研报内容中提取股票代码"""
        # 使用正则表达式提取 data-id 属性值，这是股票代码
        pattern = r'data-id="(\d+)"'
        matches = re.findall(pattern, content)
        return matches
    
    def _format_datetime(self, timestamp) -> str:
        """将时间戳转换为标准日期格式"""
        if isinstance(timestamp, (int, float)):
            # 如果是时间戳
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(timestamp, str):
            # 如果是字符串格式，尝试解析
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        else:
            return str(timestamp)
    
    def clean_report_data(self, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗研报数据，只保留需要的字段"""
        if not report_data or 'Msg' not in report_data:
            return None
        
        msg = report_data['Msg']
        
        # 提取需要的字段
        cleaned_data = {
            'Id': msg.get('ID'),
            'Title': msg.get('Title'),
            'CreateTime': self._format_datetime(msg.get('CreateTime')),
            'Label': msg.get('Label'),
            'Conts': msg.get('Conts', '')
        }
        
        # 从内容中提取股票代码
        cleaned_data['code_list'] = self._extract_stock_codes(cleaned_data['Conts'])
        
        return cleaned_data
    
    def get_report_details_clean(self, start_id: int, end_id: int) -> Dict[str, Any]:
        """批量获取并清洗研报详情数据"""
        print(f"开始批量获取研报详情 (ID范围: {start_id}-{end_id})")
        
        results = {
            'success': [],
            'errors': [],
            'meta': {
                'start_id': start_id,
                'end_id': end_id,
                'process_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        success_count = 0
        error_count = 0
        consecutive_failures = 0
        max_consecutive_failures = 50  # 增加连续失败阈值
        batch_size = 10  # 每10个打印一次进度
        
        for i, report_id in enumerate(range(start_id, end_id + 1)):
            # 每10个请求打印一次进度
            if i % batch_size == 0:
                print(f"进度: {i}/{end_id - start_id + 1} (ID: {report_id})")
            
            report_data = self.get_report_detail(str(report_id))
            
            # 如果获取失败，直接跳过
            if not report_data:
                consecutive_failures += 1
                error_count += 1
                
                # 检查连续失败次数
                if consecutive_failures >= max_consecutive_failures:
                    print(f"连续失败 {consecutive_failures} 次，停止遍历")
                    break
                continue
            
            # 清洗数据
            cleaned_data = self.clean_report_data(report_data)
            
            if cleaned_data:
                results['success'].append(cleaned_data)
                success_count += 1
                consecutive_failures = 0  # 重置连续失败计数
            else:
                consecutive_failures += 1
                error_count += 1
            
            # 减少延迟时间
            import time
            time.sleep(0.1)  # 从0.5秒减少到0.1秒
        
        # 添加统计信息
        results['stats'] = {
            'success_count': success_count,
            'error_count': error_count,
            'total_processed': (report_id - start_id + 1)
        }
        
        print(f"批量获取研报详情完成: 成功 {success_count}，失败 {error_count}")
        
        return results

# 如果直接运行此脚本
if __name__ == "__main__":
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='开盘啦研报数据处理工具 - 简化版')
    parser.add_argument('--start', type=int, default=212800, help='起始研报ID')
    parser.add_argument('--end', type=int, default=212810, help='结束研报ID')
    parser.add_argument('--output', type=str, help='输出结果到JSON文件（可选）')
    args = parser.parse_args()
    
    # 初始化处理器并获取清洗后的数据
    processor = KplReportProcessorSimple()
    results = processor.get_report_details_clean(args.start, args.end)
    
    # 过滤掉内容为空的数据
    data_json = results['success']
    data_json = [item for item in data_json if len(item['Conts']) > 1]
    data_json_str = json.dumps(data_json, ensure_ascii=False)
    
    # 打印统计信息
    print(f"\n研报数据获取完成:")
    print(f"- 成功获取: {results['stats']['success_count']}")
    print(f"- 获取失败: {results['stats']['error_count']}")
    print(f"- 总计处理: {results['stats']['total_processed']}")
    print(f"- 有效数据: {len(data_json)}")
    
    # 如果指定了输出文件，保存结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"- 结果已保存到: {args.output}")
    else:
        # 打印前3个结果（减少显示数量）
        if data_json:
            print("\n前3个研报数据示例:")
            for i, report in enumerate(data_json[:3]):
                print(f"\n[{i+1}] ID: {report['Id']} - {report['Title']}")
                print(f"    发布时间: {report['CreateTime']}")
                print(f"    标签: {report['Label']}")
                print(f"    内容: {report['Conts'][:50]}...")  # 只显示前50个字符
                print(f"    提及股票: {', '.join(report['code_list']) if report['code_list'] else '无'}") 