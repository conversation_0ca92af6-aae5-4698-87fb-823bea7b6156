
from _ctypes import Array
from typing import Dict, Any, List
import requests
import json
import re
from datetime import datetime

from minio.datatypes import Object
# 配置日志
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)



class KplStockInfoProcessor:
    """开盘啦股票信息处理类，基于报告中的股票代码获取实时信息"""

    def __init__(self):
        """初始化配置"""
        self.xq_a_token = "b9e815146f0656f1029972614b1333adbabcccea"
        self.headers = {
            "cookie": f"xq_a_token={self.xq_a_token};",
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 "
                          "(KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
        }
        self.session = requests.Session()
        print("KplStockInfoProcessor 初始化完成")

    def _convert_timestamp(self, timestamp_ms: int) -> str:
        """时间戳转换为字符串时间"""
        timestamp_s = timestamp_ms / 1000
        datetime_obj = datetime.fromtimestamp(timestamp_s)
        return datetime_obj.strftime("%Y-%m-%d %H:%M:%S")

    def _get_stock_symbol(self, stock_code: str) -> str:
        """将股票代码转换为雪球格式的symbol"""
        # 如果是6位数字，需要添加交易所前缀
        if len(stock_code) == 6:
            # 根据股票代码判断交易所
            if stock_code.startswith(('600', '601', '603', '688', '900')):
                return f"SH{stock_code}"
            elif stock_code.startswith(('000', '002', '003', '300')):
                return f"SZ{stock_code}"
            else:
                # 默认上海
                return f"SH{stock_code}"
        return stock_code

    def get_stock_info(self, stock_code: str) -> Dict[str, Any]:
        """获取单个股票实时信息，返回kv字典格式"""
        symbol = self._get_stock_symbol(stock_code)
        self.session.get(url="https://xueqiu.com", headers=self.headers)
        url = f"https://stock.xueqiu.com/v5/stock/quote.json?symbol={symbol}&extend=detail"
        try:
            response = self.session.get(url, headers=self.headers, timeout=5)
            if response.status_code != 200:
                print(f"获取股票 {stock_code} 信息失败: HTTP {response.status_code}")
                return None
            json_data = response.json()
            if 'data' not in json_data or 'quote' not in json_data['data']:
                print(f"股票 {stock_code} 返回数据格式异常")
                return None
            quote_data = json_data['data']['quote']
            column_name_map = {
                "acc_unit_nav": "累计净值",
                "amount": "成交额",
                "amplitude": "振幅",
                "avg_price": "均价",
                "chg": "涨跌",
                "currency": "货币",
                "current": "现价",
                "current_year_percent": "今年以来涨幅",
                "dividend": "股息(TTM)",
                "dividend_yield": "股息率(TTM)",
                "eps": "每股收益",
                "exchange": "交易所",
                "float_market_capital": "流通值",
                "float_shares": "流通股",
                "found_date": "成立日期",
                "goodwill_in_net_assets": "净资产中的商誉",
                "high": "最高",
                "high52w": "52周最高",
                "iopv": "参考净值",
                "issue_date": "发行日期",
                "last_close": "昨收",
                "limit_down": "跌停",
                "limit_up": "涨停",
                "lot_size": "最小交易单位",
                "low": "最低",
                "low52w": "52周最低",
                "market_capital": "资产净值/总市值",
                "name": "名称",
                "nav_date": "净值日期",
                "navps": "每股净资产",
                "open": "今开",
                "pb": "市净率",
                "pe_forecast": "市盈率(动)",
                "pe_lyr": "市盈率(静)",
                "pe_ttm": "市盈率(TTM)",
                "percent": "涨幅",
                "premium_rate": "溢价率",
                "psr": "市销率",
                "symbol": "代码",
                "total_shares": "基金份额/总股本",
                "turnover_rate": "周转率",
                "unit_nav": "单位净值",
                "volume": "成交量",
                "time": "时间",
            }
            # 字段映射+只保留含中文字段
            zh_dict = {}
            for k, v in quote_data.items():
                zh_name = column_name_map.get(k, None)
                if zh_name and any('\u4e00' <= c <= '\u9fff' for c in zh_name):
                    zh_dict[zh_name] = v
            # 时间字段转换
            for tkey in ["时间", "发行日期"]:
                if tkey in zh_dict and zh_dict[tkey]:
                    try:
                        zh_dict[tkey] = self._convert_timestamp(int(zh_dict[tkey]))
                    except Exception:
                        pass
            # 附加原始代码和雪球代码
            zh_dict["原始代码"] = stock_code
            zh_dict["雪球代码"] = symbol
            return zh_dict
        except Exception as e:
            print(f"获取股票 {stock_code} 信息异常: {str(e)}")
            return None

    def extract_stock_codes_from_reports(self, reports_data: List[Dict[str, Any]]) -> List[str]:
        """从报告数据中提取所有股票代码并去重"""
        all_codes = []

        for report in reports_data:
            if 'code_list' in report and isinstance(report['code_list'], list):
                all_codes.extend(report['code_list'])

        # 去重并排序
        unique_codes = sorted(list(set(all_codes)))
        print(f"从报告中提取到 {len(all_codes)} 个股票代码，去重后 {len(unique_codes)} 个")

        return unique_codes

    def get_stocks_info_batch(self, stock_codes: List[str]) -> Dict[str, Any]:
        """批量获取股票信息"""
        print(f"开始批量获取股票信息 (共 {len(stock_codes)} 个股票)")

        results = {
            'success': [],
            'errors': [],
            'meta': {
                'total_codes': len(stock_codes),
                'process_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }

        success_count = 0
        error_count = 0
        batch_size = 10  # 每10个打印一次进度

        for i, stock_code in enumerate(stock_codes):
            # 每10个请求打印一次进度
            if i % batch_size == 0:
                print(f"进度: {i}/{len(stock_codes)} (代码: {stock_code})")

            stock_info = self.get_stock_info(stock_code)

            if stock_info:
                results['success'].append(stock_info)
                success_count += 1
            else:
                results['errors'].append({
                    'code': stock_code,
                    'error': '获取失败'
                })
                error_count += 1

            # 添加短暂延迟
            import time
            time.sleep(0.1)

        # 添加统计信息
        results['stats'] = {
            'success_count': success_count,
            'error_count': error_count,
            'total_processed': len(stock_codes)
        }

        print(f"批量获取股票信息完成: 成功 {success_count}，失败 {error_count}")

        return results

    def process_reports_and_get_stocks(self, reports_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理报告数据并获取相关股票信息"""
        print("开始处理报告数据并提取股票代码...")

        # 从报告中提取股票代码
        stock_codes = self.extract_stock_codes_from_reports(reports_data)

        if not stock_codes:
            print("未从报告中提取到股票代码")
            return {
                'stock_codes': [],
                'stock_info': {
                    'success': [],
                    'errors': [],
                    'stats': {'success_count': 0, 'error_count': 0, 'total_processed': 0}
                }
            }

        # 获取股票信息
        stock_info = self.get_stocks_info_batch(stock_codes)

        return {
            'stock_codes': stock_codes,
            'stock_info': stock_info
        }


def __main(datax_str ) :
    # import re
    #
    # fixed_string = re.sub(r'"result":\s*"(\[.*?\])"', r'"result": \1', datax_str, flags=re.DOTALL)
    #
    # reports_result = json.loads(fixed_string)
    # print(reports_result)
    logger.warning(f"开始解析")
    logger.warning(datax_str)
    datax = [item for item in datax_str['result'] if len(item['Conts']) > 1]
    if not datax:
        return {"datax": [], "股票实时信息": []}

    # 2. 提取所有股票代码去重
    all_codes = set()
    for item in datax:
        if 'code_list' in item and isinstance(item['code_list'], list):
            all_codes.update(item['code_list'])
    all_codes = sorted(list(all_codes))
    if not all_codes:
        return {"datax": datax, "股票实时信息": []}

    # 3. 获取所有股票实时信息，组装成列表
    stock_api = KplStockInfoProcessor()
    stock_info_list = stock_api.get_stocks_info_batch(all_codes)['success']

    # 4. 返回结构
    # merge_data = {"研报信息": datax, "相关股票实时信息": stock_info_list}
    return datax,stock_info_list


# return {
#     "result": arg1 + arg2,
# }

def robust_fix_and_parse(broken_json_str:  Array[Object]):
    """
    专门用于修复那种 result 字段的值是一个损坏的、未被正确包裹的 JSON 字符串的情况。
    """
    # 1. 定位到关键问题的起点，即 "result": 之后的部分
    try:
        # 找到 "result": 的位置
        result_key_pos = broken_json_str.find('"result"')
        if result_key_pos == -1:
            raise ValueError("JSON中未找到 'result' 键")

        # 找到冒号的位置
        colon_pos = broken_json_str.find(':', result_key_pos)

        # 提取出 result 键之后的所有内容，并去除前后的空白符
        value_str = broken_json_str[colon_pos + 1:].strip()

        # 2. 我们知道这个 value_str 是一个损坏的 "伪数组"，它应该被当做一个字符串来处理
        #    - 它以 `[` 开始，以 `]` 结束
        if not (value_str.startswith('[') and value_str.endswith(']')):
            # 如果不是这种模式，我们的修复逻辑不适用
            raise ValueError("result 的值不是预期的 [ ... ] 格式")

        # 3. 构造一个合法的、需要二次解析的 JSON
        #    - 将整个 value_str 用双引号包裹起来，使其成为一个合法的 JSON 字符串
        #    - 在包裹之前，必须对 value_str 内部的 \ 和 " 进行转义
        escaped_value_str = value_str.replace('\\', '\\\\').replace('"', '\\"')

        #    - 重新组合成一个完整的、可以被解析的 JSON 字符串
        #      注意 "result": 和新生成的字符串值之间的空格
        fixed_json_str = broken_json_str[:colon_pos + 1] + ' "' + escaped_value_str + '"'

        # 4. 现在可以安全地进行两次解析了
        data_level1 = json.loads(fixed_json_str)
        final_data = json.loads(data_level1['result'])

        return final_data

    except Exception as e:
        print(f"修复和解析失败: {e}")
        return None


if __name__ == '__main__':

    fixed_string='''{
  "result": "[{\"Id\": 212802, \"Title\": \"【CT电新】诺力股份交流反馈：全面向机器人升级，估值较低建议关注\", \"CreateTime\": \"2025-06-26 06:22:07\", \"Label\": \"专家交流\", \"Conts\": \"<p>【CT电新】<a href=\\\"javascript:void('603611')\\\" target=\\\"_self\\\" style=\\\"color:#548dd8;font-weight:bold;text-decoration:none;\\\" data-id=\\\"603611\\\" data-name=\\\"诺力股份\\\" onclick=\\\"redirect_app(&#39;603611&#39;,&#39;诺力股份&#39;)\\\">诺力股份</a>交流反馈：全面向<a href=\\\"javascript:void('300024')\\\" target=\\\"_self\\\" style=\\\"color:#548dd8;font-weight:bold;text-decoration:none;\\\" data-id=\\\"300024\\\" data-name=\\\"机器人\\\" onclick=\\\"redirect_app(&#39;300024&#39;,&#39;机器人&#39;)\\\">机器人</a>升级，估值较低建议关注&nbsp;</p><p>无人叉车：&nbsp;</p><p>收入：24年1000多辆销售，收入6000万，今年目标2亿。\\r\\n技术：基于AGV升级，AGV数十年积累目前已经做到4亿收入，铜箔领域做到国内第一。&nbsp;</p><p>场景：国内目前主要钢铁、家具、饮料；法国是化工、化妆品、小电商。&nbsp;</p><p>搬运<a href=\\\"javascript:void('300024')\\\" target=\\\"_self\\\" style=\\\"color:#548dd8;font-weight:bold;text-decoration:none;\\\" data-id=\\\"300024\\\" data-name=\\\"机器人\\\" onclick=\\\"redirect_app(&#39;300024&#39;,&#39;机器人&#39;)\\\">机器人</a>：&nbsp;</p><p>产品：以叉车为基础增加手臂、视觉等\\r\\n进展：目前已有原型机，产品预计3Q25进行展示，同时希望获取小批量订单\\r\\n技术：大模型招人 浙大合作，硬件主要外采\\r\\n功能：实现货架取货、分拣等操作，进一步降低人工成本\\r\\n优势：叉车、AGV均有深厚技术积累，同时即将分拆上市的中鼎自带仓储等场景，实现从搬运<a href=\\\"javascript:void('300024')\\\" target=\\\"_self\\\" style=\\\"color:#548dd8;font-weight:bold;text-decoration:none;\\\" data-id=\\\"300024\\\" data-name=\\\"机器人\\\" onclick=\\\"redirect_app(&#39;300024&#39;,&#39;机器人&#39;)\\\">机器人</a>到工厂自动化的全产业链布局。</p><p>&nbsp;核心观点：<a href=\\\"javascript:void('300024')\\\" target=\\\"_self\\\" style=\\\"color:#548dd8;font-weight:bold;text-decoration:none;\\\" data-id=\\\"300024\\\" data-name=\\\"机器人\\\" onclick=\\\"redirect_app(&#39;300024&#39;,&#39;机器人&#39;)\\\">机器人</a>多形态多场景落地大势所趋，公司企业定位向搬运<a href=\\\"javascript:void('300024')\\\" target=\\\"_self\\\" style=\\\"color:#548dd8;font-weight:bold;text-decoration:none;\\\" data-id=\\\"300024\\\" data-name=\\\"机器人\\\" onclick=\\\"redirect_app(&#39;300024&#39;,&#39;机器人&#39;)\\\">机器人</a>倾斜，估值有较大提升空间（参考中力估值有 80%空间）</p>\", \"code_list\": [\"603611\", \"300024\", \"300024\", \"300024\", \"300024\", \"300024\"]}, {\"Id\": 212804, \"Title\": \"商业航天交流初步总结：\", \"CreateTime\": \"2025-06-26 06:22:06\", \"Label\": \"专家交流\", \"Conts\": \"<p>商业航天交流初步总结：\\r\\n会场现场火爆，干货满满&nbsp;</p><p>a. 行业专家认为：25H2-26H1发射有望提速。</p><p>&nbsp;1）25H2采购及订单有望放量，25H2-26H1有望提速；其中某公司反馈，其发现年内蓝箭的坑位已被预定完毕（表明发射任务饱满）；</p><p>&nbsp;2）商业航天事关国家安全，也是后续我国弯道超车的方向；卫星轨道频段有限，先到先得，争分夺秒；</p><p>&nbsp;3）技术突破带来降本：火箭回收等技术带来发射成本下降（从10万/KG降低到2万/kG），卫星小型化、模块化设计、规模化量产等将带来成本端显著下降；</p><p>&nbsp;4）体制内密集下海创业。一级市场对商业航天追捧热度较高。科创板第五套规则适用后将会来带产业链企业上市提速。&nbsp;</p><p>b. <a href=\\\"javascript:void('605598')\\\" target=\\\"_self\\\" style=\\\"color:#548dd8;font-weight:bold;text-decoration:none;\\\" data-id=\\\"605598\\\" data-name=\\\"上海港湾\\\" onclick=\\\"redirect_app(&#39;605598&#39;,&#39;上海港湾&#39;)\\\">上海港湾</a>卫星业务更新：24年1千万订单，25年1-5月订单已达4千万元；在卫星能源分系统场景已有较高市场份额。钙钛矿太阳帆测试验证进展顺利，转化率已达23.5%，后续仍有提升空间。</p>\", \"code_list\": [\"605598\"]}]"
}'''
    import re

    my_dict = eval(fixed_string)

    # 执行修复和解析
    # parsed_data = robust_fix_and_parse(fixed_string)
    #
    # if parsed_data:
    #     print("修复和解析成功！")
    #     # 打印第一个元素的标题以验证
    #     print("第一个元素的标题:", parsed_data[0]['Title'])

    #
    # datax_str= cleaned_str = re.sub(r'\n', '', datax_str)
    # print(datax_str)
    #
    # fixed_string = re.sub(r'"result":\s*"(\[.*?\])"', r'"result": \1', datax_str, flags=re.DOTALL)

    try:
        data = json.loads(fixed_string)
    except json.JSONDecodeError as e:
        print(f"Error at line {e.lineno}, column {e.colno}: {e.msg}")
        print(f"Problematic snippet: {fixed_string[max(0, e.pos - 20):e.pos + 20]}")
    # json.loads(fixed_string)
    datax,stock_info_list =  __main(fixed_string)
    datax = json.dumps(datax,ensure_ascii=False)
    stock_info_list = json.dumps(stock_info_list,ensure_ascii=False)

    return {
        "result": [datax, stock_info_list]
    }