import requests
import json


def to_numeric(value):
    """
    一个辅助函数，尝试将输入值转换为浮点数。
    如果值已经是数字，则直接返回。
    如果转换失败（例如，值是'-'或空字符串），则返回 None。

    :param value: 需要转换的值
    :return: 浮点数或 None
    """
    if isinstance(value, (int, float)):
        return value
    try:
        return float(value)
    except (ValueError, TypeError):
        return None


def get_stock_zh_index_spot_em_hs_important():
    """
    从东方财富网获取“沪深重要指数”的实时行情数据。
    功能等同于原代码中的 ak.stock_zh_index_spot_em(symbol="沪深重要指数")。
    不使用 pandas 库。

    :return: 包含指数行情数据的字典列表。如果请求失败或无数据，则返回空列表。
    :rtype: list[dict]
    """
    # 东方财富网-行情中心-沪深重要指数的API地址
    url = "https://33.push2.eastmoney.com/api/qt/clist/get"

    # 请求参数，与原代码中的参数保持一致
    params = {
        "pn": "1",
        "pz": "100",  # 获取最多100条数据
        "po": "1",
        "np": "1",
        "ut": "bd1d9ddb04089700cf9c27f6f7426281",
        "fltt": "2",
        "invt": "2",
        "dect": "1",
        "wbp2u": "|0|0|0|web",
        "fid": "",
        "fs": "b:MK0010",  # fs=b:MK0010 代表沪深重要指数板块
        "fields": "f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,"
                  "f23,f24,f25,f26,f22,f11,f62,f128,f136,f115,f152",
        "_": "1704327268532",  # 时间戳参数，使用固定的也可正常请求
    }

    # 字段名映射表，将API返回的'f'开头的字段映射为可读的中文名称
    # 参照原代码中的 rename 和列选择操作
    column_map = {
        "f12": "代码",
        "f14": "名称",
        "f2": "最新价",
        "f3": "涨跌幅",
        "f4": "涨跌额",
        "f5": "成交量",
        "f6": "成交额",
        "f7": "振幅",
        "f15": "最高",
        "f16": "最低",
        "f17": "今开",
        "f18": "昨收",
        "f10": "量比",
    }

    # 定义最终输出的字段顺序
    output_order = [
        "序号", "代码", "名称", "最新价", "涨跌幅", "涨跌额",
        "成交量", "成交额", "振幅", "最高", "最低", "今开", "昨收", "量比"
    ]

    try:
        # 发送GET请求
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()  # 如果请求失败(状态码不是2xx), 则抛出异常

        # 解析返回的JSON数据
        data_json = response.json()

        # 提取核心数据列表，如果不存在则默认为空列表
        source_data = data_json.get("data", {}).get("diff", [])

        if not source_data:
            print("未能从API获取到数据或数据为空。")
            return []

        processed_data = []
        # 遍历原始数据列表
        for index, record in enumerate(source_data):
            # 创建一个新字典来存放处理后的单条记录
            new_record = {"序号": index + 1}

            # 根据映射表，填充新字典
            for f_key, readable_name in column_map.items():
                original_value = record.get(f_key, None)

                # '代码' 和 '名称' 保持为字符串，其他字段尝试转换为数字
                if readable_name in ["代码", "名称"]:
                    new_record[readable_name] = original_value
                else:
                    new_record[readable_name] = to_numeric(original_value)

            # 按照指定的顺序构建最终的记录字典
            ordered_record = {key: new_record.get(key) for key in output_order}
            processed_data.append(ordered_record)

        return processed_data

    except requests.exceptions.RequestException as e:
        print(f"网络请求错误: {e}")
        return []
    except json.JSONDecodeError:
        print("解析返回数据失败，非法的JSON格式。")
        return []
    except Exception as e:
        print(f"处理数据时发生未知错误: {e}")
        return []


if __name__ == "__main__":
    # 调用函数获取数据
    data = get_stock_zh_index_spot_em_hs_important()

    json.dumps(data, ensure_ascii=False)
    # 检查是否成功获取数据
    if data:
        print(f"成功获取到 {len(data)} 条沪深重要指数行情数据。")
        print("-" * 50)

        # 打印第一条数据作为示例，使用 json.dumps 美化输出
        print("第一条数据示例:")
        print(json.dumps(data[0], indent=4, ensure_ascii=False))

        print("-" * 50)
        # 打印最后一条数据作为示例
        print("最后一条数据示例:")
        print(json.dumps(data[-1], indent=4, ensure_ascii=False))
    else:
        print("未能获取到数据。")