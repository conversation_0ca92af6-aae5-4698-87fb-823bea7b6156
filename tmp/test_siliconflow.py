import os
import openai

# --- 1. 配置你的 API Key ---
# 强烈建议使用环境变量，但为了简单测试，可以直接在此处粘贴
# api_key = os.environ.get("SILICONFLOW_API_KEY")
# 如果不使用环境变量，请取消下面一行的注释，并替换成你的 Key
api_key = "sk-uoexfaefldbdizwjfhlnghfiwxzzdloirgxtfbmdxqweclwe"

# 检查 API Key 是否已设置
if not api_key or api_key == "YOUR_SILICONFLOW_API_KEY":
    print("错误：请在代码中设置您的硅基流动 API Key。")
    exit()

# --- 2. 配置 API 客户端 ---
# 创建一个指向硅基流动 API 端点的 OpenAI 客户端
try:
    client = openai.OpenAI(
        api_key=api_key,
        base_url="https://api.siliconflow.cn/v1",  # 这是硅基流动的官方 OpenAI 兼容端点
    )
except Exception as e:
    print(f"创建 API 客户端时出错: {e}")
    exit()


# --- 3. 定义要进行向量化的文本和模型 ---
text_to_embed = "你好，世界"
model_name = "BAAI/bge-m3"

# --- 4. 发送请求并处理响应 ---
print(f"正在使用模型 '{model_name}' 为文本 '{text_to_embed}' 生成向量...")

try:
    # 调用 aPI 的 embedding 接口
    response = client.embeddings.create(
        model=model_name,
        input=[text_to_embed] # 注意：输入必须是一个列表
    )

    # 提取向量数据
    embedding = response.data[0].embedding

    print("\n✅ 成功！API 通路正常。")
    print("---------------------------------")
    print(f"模型名称: {response.model}")
    print(f"向量维度 (Dimensions): {len(embedding)}")
    print(f"向量前5个值 (First 5 values): {embedding[:5]}")
    print("---------------------------------")

except openai.APIAuthenticationError as e:
    print("\n❌ 失败！API 认证错误。")
    print("请检查您的 API Key 是否正确且有效。")
    print(f"错误详情: {e}")
except openai.NotFoundError as e:
    print(f"\n❌ 失败！模型 '{model_name}' 未找到。")
    print("请检查模型名称是否拼写正确，或者您的账户是否有权访问该模型。")
    print(f"错误详情: {e}")
except Exception as e:
    print(f"\n❌ 失败！发生未知错误。")
    print("请检查网络连接或 API 端点地址是否正确。")
    print(f"错误详情: {e}")