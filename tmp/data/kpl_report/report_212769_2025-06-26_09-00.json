{"Msg": {"ID": 212769, "Title": "从华为384超节点看下一代AI系统级算力 | 中信计算机", "CreateTime": 1750899617, "Author": {"ID": "1525360", "Name": "<PERSON><PERSON><PERSON>", "IconUrl": "https://appimgcdn.longhuvip.com/SummaryManage/AuthorIconFormal/ua_78.jpg", "Tag": "", "HeadPic": "https://appimgcdn.longhuvip.com/SummaryManage/AuthorIconFormal/ua_78.jpg"}, "Label": "机构观点", "Themes": [{"ID": "36", "Name": "算力"}, {"ID": "42", "Name": "数据中心"}], "Imgs": [], "Files": [], "Discuss": 0, "VoteCount": "2", "ShareCount": "4", "Like": 0, "Share": 0, "Coll": 0, "Conts": "<p>从华为384超节点看下一代AI系统级算力 | 中信计算机\r\n——————————————————</p><p>系统级算力有望成为下一代AI算力基础设施。底层基础设施的通用性就是为了前瞻性地应对未来的模型发展。当前AI产业发展迅速，Scaling law在后训练、在线推理等阶段快速发展。采用类似推理集群的形式未来有望成为主流，计算节点有望通过提升计算密度满足推理需求。系统级算力料将成为下一代AI算力基础设施。</p><p>系统级算力需要系统级能力。</p><p>1）芯片层面，受限制于制程等，单芯片能力的竞争并无直接优势。</p><p>2）互连层面，国产芯片采用自研技术方案助力系统集群发展。</p><p>3）网络层面，系统算力采用RDMA技术实现远程内存访问。</p><p>4）整机层面，通过系统设计、规划的有机整体，与传统AI服务器相比更需垂直融合能力。</p><p>5）生态层面，CPU GPU 互连 网络 整机 系统交付成为入局门槛，海外巨头通过收并购构筑产业生态。&nbsp;&nbsp;</p><p>技术角度，英伟达NVL72、华为CloudMatrix384超节点先行示范。构建大集群的方式主要两种：</p><p>1.Scale up（纵向扩展），增加单节点的资源数量；</p><p>2.Scale out（横向扩展），增加节点数量。相较于Scale out网络，Scale up能够提供更大的带宽、更低的通信时延，和更大的缓存一致性内存空间，为行业发展提供思路。</p><p>产业维度，半导体行业通常以收并购方式进行技术整合与市场拓展。英伟达通过收购Mellanox，将原有的NVLink（主要用于Scale up）连接技术，扩展至IB等RDMA网络（用于Scale out）；AMD通过收购ZT Systems获取了系统架构设计能力以及数据中心解决方案交付经验。未来产业发展或将从分散走向集中。\r\n\r\n[烟花]投资策略：底层基础设施朝着更大集群的方向发展，单芯片的算力提升在先进制程的影响下未来迭代速度料将放缓，而系统级节点有望通过解决互连、网络、内存墙等问题成为AI算力发展的重要方向。</p><p>建议关注：</p><p>&nbsp;1）英伟达NVL72等系统级产品出货情况，建议关注<a href=\"javascript:void('601138')\" target=\"_self\" style=\"color:#548dd8;font-weight:bold;text-decoration:none;\" data-id=\"601138\" data-name=\"工业富联\" onclick=\"redirect_app(&#39;601138&#39;,&#39;工业富联&#39;)\">工业富联</a>、<a href=\"javascript:void('000977')\" target=\"_self\" style=\"color:#548dd8;font-weight:bold;text-decoration:none;\" data-id=\"000977\" data-name=\"浪潮信息\" onclick=\"redirect_app(&#39;000977&#39;,&#39;浪潮信息&#39;)\">浪潮信息</a>；</p><p>&nbsp;2）以华为CloudMatrix384超节为代表的国产系统级产品进展，建议关注国内产业链相关公司。\r\n———————————</p><p><br/></p>"}, "errcode": "0", "t": 0.036184}