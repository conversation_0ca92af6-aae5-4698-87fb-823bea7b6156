{"ID": "189", "Name": "AIGC应用", "BriefIntro": "AIGC（AI-Generated Content，人工智能生产内容），代表AI技术发展的新趋势，通过大量的训练数据和生成算法模型，自动生成文本、图片、音乐、视频、3D交互内容等各种形式的内容。", "ClassLayer": "2", "Desc": "", "PlateSwitch": "2", "StkSwitch": "2", "Introduction": "<p>题材介绍：<br/></p><p>一、AIGC概述</p><p>1、概念</p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">AIGC（AI-Generated Content，人工智能生产内容），狭义概念是利用AI自动生产内容的生产方式，但广义上AIGC已在实现人工智能从感知理解世界到生成创造世界的进击；</span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">AIGC代表AI技术发展的新趋势，过去传统人工智能偏向分析能力，而现在人工智能正在生成新内容，通过大量的训练数据和生成算法模型，自动生成文本、图片、音乐、视频、3D交互内容等各种形式的内容；</span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">换言之，AIGC正在加速成为AI领域的商业新边界；AIGC也会带来内容创作的变革，如智能数字内容孪生能力、智能数字内容编辑能力、智能数字内容创作能力。</span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708245106450674.jpg\" title=\"1708243683184852.jpg\" alt=\"2d06b22a136b2c4d90ce2de8d044fdf4_v2-5c9dda8439270f612d98df0eaded2cea_r.jpg\"/></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">2、发展历程</span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">伴随人工智能发展演进，AIGC发展可分为三阶段，早期萌芽阶段（20世纪50年代至90年代中期）、沉淀累积阶段（20世纪90年代至21世纪10年代中期）、快速发展阶段（21世纪10年代中期至今）。</span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708245106710810.jpg\" title=\"1708243731404348.jpg\" alt=\"d5a4722007104e096a96e4f6ba69fd48_v2-186bcaace253a7dc5d9c7c3c633f1ea4_r.jpg\"/></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">二、应用场景</span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">AIGC按内容生成类别可划分为文本、代码、图像、音视频四类，根据红杉资本预测，2023年文本、代码生成有望得以成熟应用，其中文本生成可实现垂直领域文案的精确调整，达到科研论文精度，代码生成可覆盖多语种多垂直领域；图像、音视频生成的成熟度相对较低，目前尚处于生成基础初稿的阶段，2030年有望得以成熟应用。</span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708245106485013.png\" title=\"1708243810897162.png\" alt=\"image.png\"/></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">1、文本生成</span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">1）应用型文本</span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">大多为结构化写作，以客服类的聊天问答、新闻撰写等为核心场景。最为典型的是基于结构化数据或规范格式，在特定情景类型下的文本生成，如体育新闻、金融新闻、公司财报、重大灾害等简讯写作。Narrative Science创始人甚至曾预测，到2030年，90%以上的新闻将由机器人完成。</span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708245106495836.png\" title=\"1708243948435729.png\" alt=\"image.png\"/></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">2）创作型文本</span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">主要适用于剧情续写、营销文本等细分场景等，具有更高的文本开放度和自由度，需要一定的创意和个性化，对生成能力的技术要求更高。我们使用了市面上的小说续写、文章生成等AIGC工具。</span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">发现长篇幅文字的内部逻辑仍然存在较明显的问题、且生成稳定性不足，尚不适合直接进行实际使用。除去本身的技术能力之外，由于人类对文字内容的消费并不是单纯理性和基于事实的，创作型文本还需要特别关注情感和语言表达艺术。</span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">3）文本辅助<br/></span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">除去端到端进行文本创作外，辅助文本写作其实是目前国内供给及落地最为广泛的场景。主要为基于素材爬取的协助作用，例如定向采集信息素材、文本素材预处理、自动聚类去重，并根据创作者的需求提供相关素材。</span></span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708245106329440.png\" title=\"1708244023166973.png\" alt=\"image.png\"/></span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">4）文本互换</span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">例如虚拟伴侣、游戏中的NPC个性化交互等。2022年夏季上线的社交AIGC叙事平台Hidden Door以及基于GPT-3开发的文本探索类游戏AIdungeon均已获得了不错的消费者反馈。</span></span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">案例：小冰发布小冰岛APP，每个用户均可创造自己的岛屿，并连带拥有一个功能类似于微信和LINE等社交产品的完整社交交互界面。用户不仅能在岛屿中体验丰富的视觉和自然音场，与人工智能个体进行对话，还可以再造完整的一对一对话、群聊、朋友圈和技能生态体验。</span></span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708245106794140.png\" title=\"1708244091414239.png\" alt=\"image.png\"/></span></span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">2、音频生成</span></span></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">1）TTS（Text-to-speech）场景</span></span></span><span style=\"background-color: rgb(255, 255, 255); color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium;\">泛应用于客服及硬件机器人、有声读物制作、语音播报等任务。例如倒映有声与音频客户端“云听”APP合作打造AI新闻主播，提供音频内容服务的一站式解决方案，以及喜马拉雅运用TTS技术重现单田芳声音版本的《毛氏三兄弟》和历史类作品。这种场景为文字内容的有声化提供了规模化能力。</span></p><p><span style=\"background-color: rgb(255, 255, 255); color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium;\">随着内容媒体的变迁，短视频内容配音已成为重要场景。部分软件能够基于文档自动生成解说配音，上线有150+款包括不同方言和音色的AI智能配音主播。</span></p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708245106184066.png\" title=\"1708244304807067.png\" alt=\"image.png\"/></p><p>2）乐/歌曲生成</p><p><span style=\"background-color: rgb(255, 255, 255); color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium;\">AIGC在词曲创作中的功能可被逐步拆解为作词（NLP中的文本创作/续写）、作曲、编曲、人声录制和整体混音。目前而言，AIGC已经支持基于开头旋律、图片、文字描述、音乐类型、情绪类型等生成特定乐曲。</span></p><p><span style=\"background-color: rgb(255, 255, 255); color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium;\">通过这一功能，创作者即可得到AI创作的纯音乐或乐曲中的主旋律。2021年末，贝多芬管弦乐团在波恩首演人工智能谱写完成的贝多芬未完成之作《第十交响曲》，即为AI基于对贝多芬过往作品的大量学习，进行自动续写。</span></p><p><span style=\"background-color: rgb(255, 255, 255); color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium;\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708245106168326.png\" title=\"1708244790726976.png\" alt=\"image.png\"/></span></p><p>3、图像生成</p><p>1）图像属性及部分编辑</p><p><span style=\"background-color: rgb(255, 255, 255); color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium;\">属性编辑部分，可以直观的将其理解为经AI降低门槛的PhotoShop。目前而言，图片去水印、自动调整光影、设置滤镜（如Prisma、Versa、Vinci和Deepart）、修改颜色纹理（如DeepAI）、复刻/修改图像风格（DALL·E2已经可以仅凭借单张图像进行风格复刻、NightCafe等）、提升分辨率等已经常见。</span></p><p><span style=\"background-color: rgb(255, 255, 255); color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium;\">图像部分编辑部分，指部分更改图像部分构成（如英伟达CycleGAN支持将图内的斑马和马进行更改）、修改面部特征（Metaphysics，可调节自身照片的情绪、年龄、微笑等；以Deepfake为代表的图像换脸）。</span></p><p><span style=\"background-color: rgb(255, 255, 255); color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium;\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708245106388374.png\" title=\"1708244877315223.png\" alt=\"image.png\"/></span></p><p><span style=\"background-color: rgb(255, 255, 255); color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium;\">2）图像端到端生成</span></p><p><span style=\"background-color: rgb(255, 255, 255); color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium;\"><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">此处则主要指基于草图生成完整图像（VansPortrait、谷歌Chimerapainter可画出怪物、英伟达GauGAN可画出风景、基于草图生成人脸的DeepFaceDrawing）、有机组合多张图像生成新图像（Artbreeder）、根据指定属性生成目标图像（如Rosebud.ai支持生成虚拟的模特面部）等。</span></span></p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708245106706426.jpg\" title=\"1708244983427867.jpg\" alt=\"51c812205f0c4531920ebbdd154e2046_v2-c5f25228c279bae6d8e349ac4429f6ed_r.jpg\"/></p><p>4、视频生成：</p><p>1）视频属性编辑</p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\">视频画质修复、删除画面中特定主体、自动跟踪主题剪辑、生成视频特效、自动添加特定内容、视频自动美颜等。</span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708245106838306.png\" title=\"1708245086114103.png\" alt=\"image.png\"/></span></p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"></span></p><p>2）视频自动剪辑</p><p>基于视频中的画面、声音等多模态信息的特征融合进行学习，按照氛围、情绪等高级语义限定，对满足条件片段进行检测并合成。目前还主要在技术尝试阶段。</p><p>典型案例包括Adobe与斯坦福共同研发的AI视频剪辑系统、IBM Watson自动剪辑电影预告片、以及Flow Machine。我国的影谱科技推出了相关产品，能够基于视频中的画面、声音等多模态信息的特征融合进行学习，按照氛围、情绪等高级语义限定，对满足条件片段进行检测并合成。</p><p>3）视频部分生成（以Deepfake为典型代表）</p><p>视频到视频生成技术的本质是基于目标图像或视频对源视频进行编辑及调试，通过基于语音等要素逐帧复刻，能够完成人脸替换、人脸再现（人物表情或面部特征的改变）、人脸合成（构建全新人物）甚至全身合成、虚拟环境合成等功能。</p><p><span style=\"color: rgb(25, 27, 31); font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif; font-size: medium; background-color: rgb(255, 255, 255);\"></span><br/></p>", "CreateTime": "1708223832", "UpdateTime": "0", "Table": [{"Level1": {"ID": "2198", "Name": "视频生成", "ZSCode": "0", "Stocks": [{"StockID": "300781", "IsZz": "2", "IsHot": "0", "Reason": "公司InsightGPT具备文生文、视频智能剪辑、图生视频等功能，目前正在开发文生视频功能。在现有图生视频等技术框架下，nsightGPT目前可生成20秒以上的视频。", "prod_name": "因赛集团", "Hot": 1179}, {"StockID": "603189", "IsZz": "2", "IsHot": "0", "Reason": "公司致力于视频智能化技术研发，将AIGC功能融入到视频生产发布平台业务中，技术赋能视频行业企业的生产、运营、审核及分发业务", "prod_name": "网达软件", "Hot": 1104}, {"StockID": "300624", "IsZz": "2", "IsHot": "0", "Reason": "2023年10月30日，公司旗下AI视频创作软件Wondershare Filmora 13全球发布。", "prod_name": "万兴科技", "Hot": 1061}, {"StockID": "600640", "IsZz": "2", "IsHot": "0", "Reason": "在文生视频方面，已内部开发基于大模型的AIGC数字内容生成平台，利用多模态技术，使用自有的版权内容，生成文字、音频、图片、视频等影视素材，进行内容的创作生产。", "prod_name": "国脉文化", "Hot": 952}, {"StockID": "301171", "IsZz": "2", "IsHot": "0", "Reason": "公司AIGC产品KreadoAI包含了多模态模型的融合，包括文本生成、图生图、文本生成视频、语音生成、数字人生成等，也包括了文字到广告创意图片及视频的生成能力。", "prod_name": "易点天下", "Hot": 843}, {"StockID": "300079", "IsZz": "2", "IsHot": "0", "Reason": "公司在视频内容领域通过AI技术对视频内容分辨率进行智能提高，自动生产获取高于成像系统分辨率的图像，利用AI算法AIGC自动生产高清、超高清视频内容。", "prod_name": "数码视讯", "Hot": 758}, {"StockID": "688039", "IsZz": "2", "IsHot": "0", "Reason": "公司目前可为电视台、新媒体、互联网、泛媒体行业等客户提供通过AI技术进行短视频、长视频的生产/加工等解决方案。", "prod_name": "当虹科技", "Hot": 652}, {"StockID": "603888", "IsZz": "2", "IsHot": "0", "Reason": "公司发起和制定了机器智能内容生产的标准，推出了MAGIC平台，基于自研的短视频智能生产技术，该平台能够快速，海量的自动化生产出，丰富多样的短视频。", "prod_name": "新华网  ", "Hot": 632}, {"StockID": "300182", "IsZz": "2", "IsHot": "0", "Reason": "公司加大在AIGC、版权监管领域的技术开发和应用力度，推动AIGC相关技术在内容创意、影视剧内容制作、短视频创作等领域的深度应用。", "prod_name": "捷成股份", "Hot": 496}]}, "Level2": []}, {"Level1": {"ID": "2195", "Name": "文本生成", "ZSCode": "0", "Stocks": [{"StockID": "300364", "IsZz": "2", "IsHot": "0", "Reason": "公司已推出AI绘画功能和AI文字辅助创作功能，其中AI文字辅助创作功能已上线，该功能已向公司旗下17K文学平台全部作者开放。", "prod_name": "中文在线", "Hot": 2335}, {"StockID": "600986", "IsZz": "2", "IsHot": "0", "Reason": "上市公司官方账号发布：浙文米塔用AIGC打造内容创作的最强辅助。", "prod_name": "浙文互联", "Hot": 1607}, {"StockID": "300002", "IsZz": "2", "IsHot": "0", "Reason": "公司智能写作产品—“合同审核智能写作软件”，能在提供自动写作服务的同时为第三方团队或个人提供创业、创新的机会，最终构建一个智能写作生态系统。", "prod_name": "神州泰岳", "Hot": 706}, {"StockID": "301052", "IsZz": "2", "IsHot": "0", "Reason": "果麦AI创作机器人可实现通过采集互联网大数据精选文章、本地文件导入转化为自己的内容库，有机训练段落、词句、文章、知识四维AI技能。", "prod_name": "果麦文化", "Hot": 667}, {"StockID": "301270", "IsZz": "2", "IsHot": "0", "Reason": "公司开发出了行业领先的AI造字技术，该技术使得公司在生产效率、质量、创新上都有着较强的竞争力，能够有效提高C端字体的生产效率及效果。", "prod_name": "汉仪股份", "Hot": 551}]}, "Level2": []}, {"Level1": {"ID": "2196", "Name": "音频生成", "ZSCode": "0", "Stocks": [{"StockID": "002467", "IsZz": "2", "IsHot": "1", "Reason": "公司制作的虚拟数字人在语音交互功能中应用了基于AIGC技术的NLP问答技术。", "prod_name": "二六三", "Hot": 7786}, {"StockID": "000810", "IsZz": "2", "IsHot": "0", "Reason": "公司基于ChatGPT等国外、国内AIGC生成式大模型于数字智能机顶盒上革命性、创新性、多维度、多功能等的智能AI应用。", "prod_name": "创维数字", "Hot": 2156}, {"StockID": "300418", "IsZz": "2", "IsHot": "0", "Reason": "公司在AIGC方向的布局有支持AI生成文本的天工妙笔，AI生成代码的天工智码，AI生成图像的天工巧绘，AI生成音乐的天工乐府。", "prod_name": "昆仑万维", "Hot": 1857}, {"StockID": "300229", "IsZz": "2", "IsHot": "0", "Reason": "公司聚焦NLP、知识图谱、图像检索三大核心技术，结合机器人流程自动化技术，为广大用户提供文本、音视频、多模态等全栈AI服务能力。", "prod_name": "拓尔思", "Hot": 1376}, {"StockID": "002230", "IsZz": "2", "IsHot": "0", "Reason": "公司是世界领先的智能语音技术企业，专业从事语音及语言、自然语言理解、机器学习推理及自主学习等人工智能核心技术研究。", "prod_name": "科大讯飞", "Hot": 1239}, {"StockID": "002722", "IsZz": "2", "IsHot": "0", "Reason": "公司参股投资的北京灵伴基于自主研发的智能语音语言核心技术，面向智能语音服务、AIGC长音频内容智能生产及虚拟数字人等场景", "prod_name": "物产金轮", "Hot": 981}]}, "Level2": []}, {"Level1": {"ID": "2197", "Name": "图像生成", "ZSCode": "0", "Stocks": [{"StockID": "000681", "IsZz": "2", "IsHot": "0", "Reason": "公司从前期的确权场景扩展到了交易环节，品类从摄影作品扩展到了美术、文学甚至音乐、AI生成内容等，发行模式也进入到了由虚到实、虚实结合的阶段。", "prod_name": "视觉中国", "Hot": 2358}, {"StockID": "300299", "IsZz": "2", "IsHot": "0", "Reason": "公司目前已搭建本地化AI应用工具，其中AI绘图（美术）工具已在部分在研项目应用，实现一定的降本增效，并积极探索AIGC在程序、测试优化等环节实现应用工具。", "prod_name": "富春股份", "Hot": 1975}, {"StockID": "603322", "IsZz": "2", "IsHot": "0", "Reason": "公司投资七火山Seven Volcanoes，凭借海外上线发行及取得东南亚TOP3榜单的MiniTV平台开展视频内容新载体创造探索和短视频巨头内容AI化战略服务商等战略。", "prod_name": "超讯通信", "Hot": 1451}, {"StockID": "300058", "IsZz": "2", "IsHot": "0", "Reason": "蓝色光标销博特发布“创意画廊”——一键生成抽象画平台，用户使用“康定斯基模型”输入文本即可在6分钟内生成一幅抽象画作。", "prod_name": "蓝色光标", "Hot": 1231}, {"StockID": "002517", "IsZz": "2", "IsHot": "0", "Reason": "公司及部分战略投资企业已在应用相关AI工具并尝试逐步建立内部AI数据库，AIGC技术在美术等领域的降本增效已经体现出明显的潜力，未来公司将会研究加大应用相关AIGC工具的力度。", "prod_name": "恺英网络", "Hot": 788}, {"StockID": "603888", "IsZz": "2", "IsHot": "0", "Reason": "互动易：新华智云的妙笔是AI文本生成，生花就是AI文生图，这两个今年上线的新产品都是AIGC内容生产工具。", "prod_name": "新华网  ", "Hot": 632}]}, "Level2": []}, {"Level1": {"ID": "2199", "Name": "北交所股票", "ZSCode": "0", "Stocks": [{"StockID": "835305", "IsZz": "2", "IsHot": "0", "Reason": "公司在生成式AI领域（AIGC领域）陆续推出cGPT系列产品，打造智能内容生成、理解、搜索等应用环境，构建GPT行业应用新生态。", "prod_name": "*ST云创", "Hot": 1107}]}, "Level2": []}], "Stocks": [], "StockList": [{"StockID": "300182", "Tag": [{"ID": "2198", "Name": "视频生成", "Reason": "公司加大在AIGC、版权监管领域的技术开发和应用力度，推动AIGC相关技术在内容创意、影视剧内容制作、短视频创作等领域的深度应用。"}], "prod_name": "捷成股份", "HotNum": 496}, {"StockID": "688039", "Tag": [{"ID": "2198", "Name": "视频生成", "Reason": "公司目前可为电视台、新媒体、互联网、泛媒体行业等客户提供通过AI技术进行短视频、长视频的生产/加工等解决方案。"}], "prod_name": "当虹科技", "HotNum": 652}, {"StockID": "603888", "Tag": [{"ID": "2198", "Name": "视频生成", "Reason": "公司发起和制定了机器智能内容生产的标准，推出了MAGIC平台，基于自研的短视频智能生产技术，该平台能够快速，海量的自动化生产出，丰富多样的短视频。"}, {"ID": "2197", "Name": "图像生成", "Reason": "互动易：新华智云的妙笔是AI文本生成，生花就是AI文生图，这两个今年上线的新产品都是AIGC内容生产工具。"}], "prod_name": "新华网  ", "HotNum": 632}, {"StockID": "300079", "Tag": [{"ID": "2198", "Name": "视频生成", "Reason": "公司在视频内容领域通过AI技术对视频内容分辨率进行智能提高，自动生产获取高于成像系统分辨率的图像，利用AI算法AIGC自动生产高清、超高清视频内容。"}], "prod_name": "数码视讯", "HotNum": 758}, {"StockID": "300418", "Tag": [{"ID": "2196", "Name": "音频生成", "Reason": "公司在AIGC方向的布局有支持AI生成文本的天工妙笔，AI生成代码的天工智码，AI生成图像的天工巧绘，AI生成音乐的天工乐府。"}], "prod_name": "昆仑万维", "HotNum": 1857}, {"StockID": "002722", "Tag": [{"ID": "2196", "Name": "音频生成", "Reason": "公司参股投资的北京灵伴基于自主研发的智能语音语言核心技术，面向智能语音服务、AIGC长音频内容智能生产及虚拟数字人等场景"}], "prod_name": "物产金轮", "HotNum": 981}, {"StockID": "000810", "Tag": [{"ID": "2196", "Name": "音频生成", "Reason": "公司基于ChatGPT等国外、国内AIGC生成式大模型于数字智能机顶盒上革命性、创新性、多维度、多功能等的智能AI应用。"}], "prod_name": "创维数字", "HotNum": 2156}, {"StockID": "300229", "Tag": [{"ID": "2196", "Name": "音频生成", "Reason": "公司聚焦NLP、知识图谱、图像检索三大核心技术，结合机器人流程自动化技术，为广大用户提供文本、音视频、多模态等全栈AI服务能力。"}], "prod_name": "拓尔思", "HotNum": 1376}, {"StockID": "002230", "Tag": [{"ID": "2196", "Name": "音频生成", "Reason": "公司是世界领先的智能语音技术企业，专业从事语音及语言、自然语言理解、机器学习推理及自主学习等人工智能核心技术研究。"}], "prod_name": "科大讯飞", "HotNum": 1239}, {"StockID": "000681", "Tag": [{"ID": "2197", "Name": "图像生成", "Reason": "公司从前期的确权场景扩展到了交易环节，品类从摄影作品扩展到了美术、文学甚至音乐、AI生成内容等，发行模式也进入到了由虚到实、虚实结合的阶段。"}], "prod_name": "视觉中国", "HotNum": 2358}, {"StockID": "300058", "Tag": [{"ID": "2197", "Name": "图像生成", "Reason": "蓝色光标销博特发布“创意画廊”——一键生成抽象画平台，用户使用“康定斯基模型”输入文本即可在6分钟内生成一幅抽象画作。"}], "prod_name": "蓝色光标", "HotNum": 1231}, {"StockID": "300299", "Tag": [{"ID": "2197", "Name": "图像生成", "Reason": "公司目前已搭建本地化AI应用工具，其中AI绘图（美术）工具已在部分在研项目应用，实现一定的降本增效，并积极探索AIGC在程序、测试优化等环节实现应用工具。"}], "prod_name": "富春股份", "HotNum": 1975}, {"StockID": "603322", "Tag": [{"ID": "2197", "Name": "图像生成", "Reason": "公司投资七火山Seven Volcanoes，凭借海外上线发行及取得东南亚TOP3榜单的MiniTV平台开展视频内容新载体创造探索和短视频巨头内容AI化战略服务商等战略。"}], "prod_name": "超讯通信", "HotNum": 1451}, {"StockID": "002517", "Tag": [{"ID": "2197", "Name": "图像生成", "Reason": "公司及部分战略投资企业已在应用相关AI工具并尝试逐步建立内部AI数据库，AIGC技术在美术等领域的降本增效已经体现出明显的潜力，未来公司将会研究加大应用相关AIGC工具的力度。"}], "prod_name": "恺英网络", "HotNum": 788}, {"StockID": "002467", "Tag": [{"ID": "2196", "Name": "音频生成", "Reason": "公司制作的虚拟数字人在语音交互功能中应用了基于AIGC技术的NLP问答技术。"}], "prod_name": "二六三", "HotNum": 7786}, {"StockID": "600640", "Tag": [{"ID": "2198", "Name": "视频生成", "Reason": "在文生视频方面，已内部开发基于大模型的AIGC数字内容生成平台，利用多模态技术，使用自有的版权内容，生成文字、音频、图片、视频等影视素材，进行内容的创作生产。"}], "prod_name": "国脉文化", "HotNum": 952}, {"StockID": "300781", "Tag": [{"ID": "2198", "Name": "视频生成", "Reason": "公司InsightGPT具备文生文、视频智能剪辑、图生视频等功能，目前正在开发文生视频功能。在现有图生视频等技术框架下，nsightGPT目前可生成20秒以上的视频。"}], "prod_name": "因赛集团", "HotNum": 1179}, {"StockID": "301171", "Tag": [{"ID": "2198", "Name": "视频生成", "Reason": "公司AIGC产品KreadoAI包含了多模态模型的融合，包括文本生成、图生图、文本生成视频、语音生成、数字人生成等，也包括了文字到广告创意图片及视频的生成能力。"}], "prod_name": "易点天下", "HotNum": 843}, {"StockID": "603189", "Tag": [{"ID": "2198", "Name": "视频生成", "Reason": "公司致力于视频智能化技术研发，将AIGC功能融入到视频生产发布平台业务中，技术赋能视频行业企业的生产、运营、审核及分发业务"}], "prod_name": "网达软件", "HotNum": 1104}, {"StockID": "300624", "Tag": [{"ID": "2198", "Name": "视频生成", "Reason": "2023年10月30日，公司旗下AI视频创作软件Wondershare Filmora 13全球发布。"}], "prod_name": "万兴科技", "HotNum": 1061}, {"StockID": "835305", "Tag": [{"ID": "2199", "Name": "北交所股票", "Reason": "公司在生成式AI领域（AIGC领域）陆续推出cGPT系列产品，打造智能内容生成、理解、搜索等应用环境，构建GPT行业应用新生态。"}], "prod_name": "*ST云创", "HotNum": 1107}, {"StockID": "301270", "Tag": [{"ID": "2195", "Name": "文本生成", "Reason": "公司开发出了行业领先的AI造字技术，该技术使得公司在生产效率、质量、创新上都有着较强的竞争力，能够有效提高C端字体的生产效率及效果。"}], "prod_name": "汉仪股份", "HotNum": 551}, {"StockID": "300364", "Tag": [{"ID": "2195", "Name": "文本生成", "Reason": "公司已推出AI绘画功能和AI文字辅助创作功能，其中AI文字辅助创作功能已上线，该功能已向公司旗下17K文学平台全部作者开放。"}], "prod_name": "中文在线", "HotNum": 2335}, {"StockID": "300002", "Tag": [{"ID": "2195", "Name": "文本生成", "Reason": "公司智能写作产品—“合同审核智能写作软件”，能在提供自动写作服务的同时为第三方团队或个人提供创业、创新的机会，最终构建一个智能写作生态系统。"}], "prod_name": "神州泰岳", "HotNum": 706}, {"StockID": "301052", "Tag": [{"ID": "2195", "Name": "文本生成", "Reason": "果麦AI创作机器人可实现通过采集互联网大数据精选文章、本地文件导入转化为自己的内容库，有机训练段落、词句、文章、知识四维AI技能。"}], "prod_name": "果麦文化", "HotNum": 667}, {"StockID": "600986", "Tag": [{"ID": "2195", "Name": "文本生成", "Reason": "上市公司官方账号发布：浙文米塔用AIGC打造内容创作的最强辅助。"}], "prod_name": "浙文互联", "HotNum": 1607}], "Power": 0, "Subscribe": 0, "ZT": {"002467": ["1", "9.95", "1751007782"]}, "IsGood": 0, "GoodNum": 276, "ComNum": 618, "errcode": "0", "t": 0.012923000000000018}