{"ID": "317", "Name": "离境退税概念", "BriefIntro": "2025年4月8日，国家税务总局发布《关于推广境外旅客购物离境退税“即买即退”服务措施的公告》指出，境外旅客在“即买即退”商店购买退税物品可在现场立即领取等额退税款。当前该服务已在上海、北京、广东、四川、浙江等地进行试点，并有望未来推广至全国。", "ClassLayer": "3", "Desc": "", "PlateSwitch": "1", "StkSwitch": "2", "Introduction": "<p>题材相关新闻：</p><p>2025年4月8日，国家税务总局发布《关于推广境外旅客购物离境退税“即买即退”服务措施的公告》。</p><p>公告内容指出，境外旅客在“即买即退”商店购买退税物品可在现场立即领取等额退税款。当前该服务已在上海、北京、广东、四川、浙江等地进行试点，并有望未来推广至全国。</p><p><br/></p><p>“即买即退”服务将退税环节提前，优化了入境游旅客的消费体验，刺激旅客消费欲望以及退税款再消费，有助于充分释放入境游消费潜力。带动机场、免税、百货、退税店等相关行业发展</p><p><br/></p><p>题材相关介绍</p><p><br/></p><p><span style=\"color: rgba(0, 0, 0, 0.85); font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255);\">离境退税是国家为促进境外旅客消费而推出的税收优惠政策，允许符合条件的境外游客在购买指定商品后，离境时退还部分增值税。</span></p><p><span style=\"color: rgba(0, 0, 0, 0.85); font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255);\"><br/></span></p><p><span style=\"color: rgba(0, 0, 0, 0.85); font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255);\"></span></p><h3 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h3-line-height); margin-top: var(--md-box-samantha-h3-margin-top); font-size: var(--md-box-samantha-h3-font-size); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h3-margin-bottom) !important; padding-bottom: 0px !important;\">一、核心要素</h3><p><span style=\"color: rgba(0, 0, 0, 0.85); font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255);\"></span></p><h4 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h4-line-height); margin-top: var(--md-box-samantha-h4-margin-top); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h4-margin-bottom) !important; padding-bottom: 0px !important;\">1.&nbsp;<span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto; color: var(--md-box-samantha-deep-text-color) !important;\">适用对象</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">身份限制</span>：在中国境内连续居住不超过 183 天的外国人和港澳台同胞（以护照或通行证为准）</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">消费门槛</span>：同一境外旅客同一日在同一退税商店购买退税物品金额≥500 元人民币</p></li></ul><h4 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h4-line-height); margin-top: var(--md-box-samantha-h4-margin-top); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h4-margin-bottom) !important; padding-bottom: 0px !important;\">2.&nbsp;<span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto; color: var(--md-box-samantha-deep-text-color) !important;\">退税物品范围</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">可退物品</span>：服装、化妆品、电子产品、工艺品等个人消费品（需保留完整包装且未使用）</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">不可退物品</span>：免税品、消耗品（如食品、烟酒）、禁止 / 限制出境物品（如文物、濒危动植物制品）</p></li></ul><h4 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h4-line-height); margin-top: var(--md-box-samantha-h4-margin-top); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h4-margin-bottom) !important; padding-bottom: 0px !important;\">3.&nbsp;<span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto; color: var(--md-box-samantha-deep-text-color) !important;\">退税率与计算</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">基础退税率</span>：适用 13% 税率的商品退税率为 11%，适用 9% 税率的商品退税率为 8%</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">实际到账金额</span>：需扣除 2% 的退税手续费，例如购买 1000 元商品（含税），实退金额 = 1000×11% - 1000×2% = 90 元</p></li></ul><h3 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h3-line-height); margin-top: var(--md-box-samantha-h3-margin-top); font-size: var(--md-box-samantha-h3-font-size); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h3-margin-bottom) !important; padding-bottom: 0px !important;\">二、操作流程详解</h3><h4 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h4-line-height); margin-top: var(--md-box-samantha-h4-margin-top); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h4-margin-bottom) !important; padding-bottom: 0px !important;\">1.&nbsp;<span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto; color: var(--md-box-samantha-deep-text-color) !important;\">传统退税模式</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">购物环节</span>：在贴有 “退税商店” 标识的店铺购买商品，索要《境外旅客购物离境退税申请单》和增值税普通发票</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">离境核验</span>：在机场 / 港口海关出示退税物品、申请单、发票及护照，海关验核后在申请单上签章</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">退税领取</span>：</p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">现金退税</span>：在离境口岸隔离区内的退税代理机构办理，单笔≤1 万元可选择现金，超 1 万元需银行转账</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">信用卡 / 支付宝退税</span>：填写收款信息后，退税款通常在 10-30 个工作日到账</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202504/1744184886884183.png\" title=\"1744184136828977.png\" alt=\"image.png\"/></p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202504/1744184886733267.png\" title=\"1744184153407632.png\" alt=\"image.png\"/><br/></p></li></ul></ul><h4 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h4-line-height); margin-top: var(--md-box-samantha-h4-margin-top); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h4-margin-bottom) !important; padding-bottom: 0px !important;\">2.&nbsp;<span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto; color: var(--md-box-samantha-deep-text-color) !important;\">“即买即退” 新模式</span>（全国推广）</h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">政策亮点</span>：境外旅客在试点商店购物时，签订协议并办理信用卡预授权后，可当场领取退税款（金额为含税价 ×11%），离境时仅需海关验核物品</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">操作步骤</span>：</p></li><ol class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: lower-roman;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">商店预付</span>：商店冻结信用卡相应额度并支付退税款（如购买 5000 元商品，现场领取 550 元）</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">海关验核</span>：离境时向海关出示物品、申请单及发票，海关验核后签章</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">解除预授权</span>：退税代理机构审核通过后，解除信用卡冻结额度；若未离境，代理机构将从信用卡扣除已退税款</p></li></ol></ul><h3 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h3-line-height); margin-top: var(--md-box-samantha-h3-margin-top); font-size: var(--md-box-samantha-h3-font-size); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h3-margin-bottom) !important; padding-bottom: 0px !important;\">三、中国政策实践</h3><h4 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h4-line-height); margin-top: var(--md-box-samantha-h4-margin-top); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h4-margin-bottom) !important; padding-bottom: 0px !important;\">1.&nbsp;<span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto; color: var(--md-box-samantha-deep-text-color) !important;\">全国推广 “即买即退”</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">覆盖范围</span>：2025 年 4 月起，北京、上海、广东、四川、浙江等试点地区的 “即买即退” 服务扩展至全国，各省可根据实际制定实施方案</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">商店名单</span>：截至 2025 年 3 月，上海已公布 23 批退税商店名单，北京 SKP、成都王府井百货等大型商场均在列</p></li><li><p><span style=\"color: rgb(62, 62, 62); letter-spacing: 0.578px;\">成都全市共有离境退税商店429家、同比增长34%，</span>其中49家“即买即退”。<br/></p></li></ul><h4 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h4-line-height); margin-top: var(--md-box-samantha-h4-margin-top); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h4-margin-bottom) !important; padding-bottom: 0px !important;\">2.&nbsp;<span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto; color: var(--md-box-samantha-deep-text-color) !important;\">技术赋能</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">智能核验</span>：部分机场引入区块链技术，退税单验核时间从 45 分钟压缩至 90 秒</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">支付便利</span>：微信支付、支付宝支持 “即买即退” 现金预付，2024 年北京地区退税交易中微信支付占比达 42%</p></li></ul><h3 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h3-line-height); margin-top: var(--md-box-samantha-h3-margin-top); font-size: var(--md-box-samantha-h3-font-size); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h3-margin-bottom) !important; padding-bottom: 0px !important;\">四、国际对比与趋势</h3><h4 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h4-line-height); margin-top: var(--md-box-samantha-h4-margin-top); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h4-margin-bottom) !important; padding-bottom: 0px !important;\">1.&nbsp;<span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto; color: var(--md-box-samantha-deep-text-color) !important;\">欧洲主流模式</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">退税率</span>：德国（14.5%）、法国（12%）、意大利（17.5%），退税率与消费金额挂钩</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">流程差异</span>：</p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">现金退税</span>：需在机场排队办理，手续费较高（约 3%-5%）</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">回国退税</span>：部分国家（如德国）支持回国后通过顺丰邮寄退税单，5 个工作日到账</p></li></ul></ul><h4 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h4-line-height); margin-top: var(--md-box-samantha-h4-margin-top); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h4-margin-bottom) !important; padding-bottom: 0px !important;\">2.&nbsp;<span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto; color: var(--md-box-samantha-deep-text-color) !important;\">亚洲特色政策</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">韩国</span>：明洞商圈支持 “现场退税”，退税款直接抵扣商品价格，离境时仅需提交退税单</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">日本</span>：药妆店普遍支持免税与退税一体化，部分商店接受支付宝实时退税</p></li></ul><h3 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h3-line-height); margin-top: var(--md-box-samantha-h3-margin-top); font-size: var(--md-box-samantha-h3-font-size); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h3-margin-bottom) !important; padding-bottom: 0px !important;\">五、实用建议</h3><h4 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h4-line-height); margin-top: var(--md-box-samantha-h4-margin-top); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h4-margin-bottom) !important; padding-bottom: 0px !important;\">1.&nbsp;<span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto; color: var(--md-box-samantha-deep-text-color) !important;\">操作技巧</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">集中购物</span>：同一商店单日消费≥500 元可合并开单，减少退税次数</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">预留时间</span>：传统退税需提前 3 小时到达机场，“即买即退” 可节省 1 小时以上</p></li></ul><h4 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h4-line-height); margin-top: var(--md-box-samantha-h4-margin-top); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h4-margin-bottom) !important; padding-bottom: 0px !important;\">2.&nbsp;<span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto; color: var(--md-box-samantha-deep-text-color) !important;\">风险提示</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">物品保管</span>：退税物品需保持包装完整，若在境内使用可能被海关拒绝退税</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">汇率波动</span>：现金退税可能面临汇率损失，建议选择信用卡或支付宝退税</p></li></ul><h4 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h4-line-height); margin-top: var(--md-box-samantha-h4-margin-top); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h4-margin-bottom) !important; padding-bottom: 0px !important;\">3.&nbsp;<span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto; color: var(--md-box-samantha-deep-text-color) !important;\">查询渠道</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">官方信息</span>：国家税务总局官网（<a class=\"link-_G3LTk\" target=\"_blank\" href=\"http://www.chinatax.gov.cn/\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); text-decoration-line: none; background-color: transparent; overflow-anchor: auto;\">http://www.chinatax.gov.cn</a>）或 “i 口岸” 小程序可查询退税商店名单及政策细则</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">企业动态</span>：关注王府井集团（600859.SH）、中国中免（601888.SH）等上市公司公告，获取 “即买即退” 商店布局信息</p></li></ul><h3 class=\"header-uGmlgo auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: var(--md-box-samantha-h3-line-height); margin-top: var(--md-box-samantha-h3-margin-top); font-size: var(--md-box-samantha-h3-font-size); overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-h3-margin-bottom) !important; padding-bottom: 0px !important;\">六、未来趋势</h3><ol class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: var(--md-box-samantha-li-margin) !important; margin-top: var(--md-box-samantha-li-margin) !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">技术深化</span>：AI 图像识别技术将应用于海关验核，自动匹配退税物品与发票信息</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">区域拓展</span>：中国计划形成 “点线圈” 立体退税网络，郊区商圈（如北京丽泽商务区）将增设退税网点</p></li><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; color: var(--md-box-samantha-deep-text-color) !important; font-size: var(--md-box-samantha-normal-text-font-size); line-height: var(--md-box-samantha-normal-text-line-height); overflow-anchor: auto;\">国际协同</span>：与 “一带一路” 国家探索跨境退税互认，简化多国游退税流程</p><p><br/></p></li></ol><p><span style=\"font-family: 宋体; text-align: justify; text-indent: 32px; background-color: rgb(255, 255, 255);\">商务部消费促进司司长李刚在商务部专题发布会上介绍，全球优质消费资源在相关城市加速集聚。2024年，相关城市入境的外国人数量比上年增长约1倍，占全国比重约三成；离境退税商店数量占全国比重约六成，离境退税销售额占全国比重超七成。</span></p><p><br/></p><p><br/></p><p><span style=\"color: rgba(0, 0, 0, 0.85); font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255);\"><br/></span><br/></p>", "CreateTime": "1744175273", "UpdateTime": "0", "Table": [{"Level1": {"ID": "3636", "Name": "浙江", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "3660", "Name": "退税店", "ZSCode": "0", "Stocks": [{"StockID": "301066", "IsZz": "2", "IsHot": "0", "Reason": "万事利丝绸成杭州首批离境退税“即买即退”试点商店", "prod_name": "万事利", "Hot": 1518}, {"StockID": "002404", "IsZz": "2", "IsHot": "0", "Reason": "公司全资子公司浙江嘉欣金三塔丝绸服饰有限公司前期获得浙江省首批离境退税资格", "prod_name": "嘉欣丝绸", "Hot": 675}]}]}, {"Level1": {"ID": "3641", "Name": "海南", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "3668", "Name": "退税店", "ZSCode": "0", "Stocks": [{"StockID": "000564", "IsZz": "2", "IsHot": "0", "Reason": "公司旗下在琼企业海南望海国际是海南省首批境外旅客购物离境退税定点商店", "prod_name": "供销大集", "Hot": 1970}, {"StockID": "000796", "IsZz": "2", "IsHot": "0", "Reason": "与海南驻地企业供销大集签署战略合作，在离境退税和免税、产品分销推广、供应链打造方面优势互补", "prod_name": "凯撒旅业", "Hot": 641}, {"StockID": "600515", "IsZz": "2", "IsHot": "0", "Reason": "公司自持物业望海国际广场为海南首家也是唯一一家被国家商务部评定为“金鼎百货店”的商业企业，是海南省首批境外旅客购物离境退税定点商店", "prod_name": "海南机场", "Hot": 405}]}, {"ID": "3669", "Name": "免税", "ZSCode": "801471", "Stocks": [{"StockID": "603069", "IsZz": "2", "IsHot": "0", "Reason": "公司拟收购海南旅投免税品有限公司100%股权", "prod_name": "海汽集团", "Hot": 1098}, {"StockID": "002163", "IsZz": "2", "IsHot": "0", "Reason": "2025年3月互动易:控股股东海南控股于2021年作出免税资产注入承诺，旨在推动公司向免税业务转型。", "prod_name": "海南发展", "Hot": 935}, {"StockID": "000886", "IsZz": "2", "IsHot": "0", "Reason": "海南国资委旗下，海南省最大的高速公路经营管理及房地产开发企业，是海汽集团的第二大股东，海旅免税为海汽集团子公司。", "prod_name": "海南高速", "Hot": 791}, {"StockID": "000796", "IsZz": "2", "IsHot": "0", "Reason": "子公司海南海橡国际健康文旅投资集团有限公司经营范围包括免税商店商品销售。", "prod_name": "凯撒旅业", "Hot": 641}]}, {"ID": "3671", "Name": "机场", "ZSCode": "0", "Stocks": [{"StockID": "600515", "IsZz": "2", "IsHot": "0", "Reason": "公司通过参股和自持物业参与海南离岛免税店业务，持有海南海航中免49.9%股权，拥有多个机场口岸免税店。此外，公司控股7家机场。", "prod_name": "海南机场", "Hot": 405}]}]}, {"Level1": {"ID": "3633", "Name": "北京", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "3644", "Name": "退税店", "ZSCode": "0", "Stocks": [{"StockID": "000882", "IsZz": "2", "IsHot": "0", "Reason": "北京市离境退税“即买即退”试点商店名单（2024版）中包含公司", "prod_name": "华联股份", "Hot": 2016}, {"StockID": "600859", "IsZz": "2", "IsHot": "0", "Reason": "北京市离境退税“即买即退”试点商店名单（2024版）中包含公司", "prod_name": "王府井  ", "Hot": 818}, {"StockID": "601888", "IsZz": "2", "IsHot": "0", "Reason": "公司开设市内购物退税店", "prod_name": "中国中免", "Hot": 724}]}, {"ID": "3646", "Name": "免税店", "ZSCode": "0", "Stocks": [{"StockID": "000785", "IsZz": "2", "IsHot": "0", "Reason": "居然之家海南三亚免税店已于2020年年底开业", "prod_name": "居然智家", "Hot": 1832}, {"StockID": "600859", "IsZz": "2", "IsHot": "0", "Reason": "公司自2020年获得免税品经营资质，运营1家离岛免税店和7家跨境电商体验店", "prod_name": "王府井  ", "Hot": 818}, {"StockID": "601888", "IsZz": "2", "IsHot": "0", "Reason": "全球第四大免税业务运营商，我国免税店龙头企业", "prod_name": "中国中免", "Hot": 724}]}, {"ID": "3643", "Name": "百货", "ZSCode": "0", "Stocks": [{"StockID": "603123", "IsZz": "2", "IsHot": "0", "Reason": "北京海淀国资委旗下的商业龙头企业，主要产品包括百货业务", "prod_name": "翠微股份", "Hot": 14719}]}, {"ID": "3645", "Name": "机场", "ZSCode": "0", "Stocks": [{"StockID": "601111", "IsZz": "2", "IsHot": "0", "Reason": "公司与北京市国家税务局联手正式启动了离境退税政策境外宣传项目", "prod_name": "中国国航", "Hot": 414}]}]}, {"Level1": {"ID": "3634", "Name": "广东", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "3648", "Name": "退税店", "ZSCode": "0", "Stocks": [{"StockID": "600828", "IsZz": "2", "IsHot": "0", "Reason": "公司深圳地区的所有门店和成都地区的人东店、光华店均为离境退税店", "prod_name": "茂业商业", "Hot": 2511}, {"StockID": "002187", "IsZz": "2", "IsHot": "0", "Reason": "公司旗下广百百货广州多店于2022年被列入首批离境退税“即买即退”试点商店", "prod_name": "广百股份", "Hot": 1700}]}, {"ID": "3649", "Name": "免税店", "ZSCode": "0", "Stocks": [{"StockID": "000524", "IsZz": "2", "IsHot": "0", "Reason": "控股股东岭南集团书面表示该集团正在推进申请免税品经营资质的相关工作，并已向广州市政府相关部门正式提交关于支持其申请免税业务经营牌照的请示。", "prod_name": "岭南控股", "Hot": 1438}, {"StockID": "600185", "IsZz": "2", "IsHot": "0", "Reason": "公司位于珠海市，珠海免税已成为公司的控股子公司， 公司战略定位变更为以免税业务为核心", "prod_name": "珠免集团", "Hot": 796}, {"StockID": "001979", "IsZz": "2", "IsHot": "0", "Reason": "公司持有深圳市招商免税商品有限公司49%股权，深圳市国有免税商品（集团）有限公司持有其51%股权。", "prod_name": "招商蛇口", "Hot": 457}]}, {"ID": "3651", "Name": "百货", "ZSCode": "0", "Stocks": [{"StockID": "002187", "IsZz": "2", "IsHot": "0", "Reason": "公司位于广州市，主要业务为百货零售，经营业态包括百货商场、购物中心、超市、专业店以及线上商品销售等。", "prod_name": "广百股份", "Hot": 1700}, {"StockID": "002356", "IsZz": "2", "IsHot": "0", "Reason": "公司位于深圳市，从事国际品牌服装、鞋帽、箱包等商品的零售业务，通过在百货商场、购物中心等开设终端实体门店或线上店铺实现销售。", "prod_name": "赫美集团", "Hot": 981}, {"StockID": "002419", "IsZz": "2", "IsHot": "0", "Reason": "公司位于深圳市，公司的平台型业务为百货和购物中心，以联营、租赁等合作模式为主；", "prod_name": "天虹股份", "Hot": 671}]}, {"ID": "3650", "Name": "机场", "ZSCode": "0", "Stocks": [{"StockID": "600004", "IsZz": "2", "IsHot": "0", "Reason": "公司位于广州市，为白云机场的管理和运营机构， 白云机场是国内三大国际枢纽机场之一", "prod_name": "XD白云机", "Hot": 342}, {"StockID": "000089", "IsZz": "2", "IsHot": "0", "Reason": "公司为深圳宝安国际机场的运营管理机构，主要经营航空主业以及航空主业延伸出的非航空业务", "prod_name": "深圳机场", "Hot": 333}]}]}, {"Level1": {"ID": "3632", "Name": "上海", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "3638", "Name": "退税店", "ZSCode": "0", "Stocks": [{"StockID": "600628", "IsZz": "2", "IsHot": "0", "Reason": "为上海首批 27 家退税定点消费商店之一，为境外旅客提供离境退税率为 11%的服务", "prod_name": "新世界  ", "Hot": 735}, {"StockID": "600838", "IsZz": "2", "IsHot": "0", "Reason": "为第22批上海境外旅客购物离境退税商店之一", "prod_name": "上海九百", "Hot": 686}, {"StockID": "600612", "IsZz": "2", "IsHot": "0", "Reason": "上海老凤祥银楼总店等 5 家上海地区老凤祥银楼连锁店成为国内“境外旅客离境退税政策试点门店”", "prod_name": "老凤祥  ", "Hot": 616}, {"StockID": "002561", "IsZz": "2", "IsHot": "0", "Reason": "旗下港汇恒隆广场等商城均有离境退税服务点", "prod_name": "徐家汇", "Hot": 570}, {"StockID": "600655", "IsZz": "2", "IsHot": "0", "Reason": "豫园商城华宝楼和天裕楼成为首批离境退税店", "prod_name": "豫园股份", "Hot": 437}, {"StockID": "600833", "IsZz": "2", "IsHot": "0", "Reason": "公司凭借旗下第一医药商店经营服务特色及市场影响力, 获得了上海市首批境外旅客购物离境退税定点商店的资质", "prod_name": "第一医药", "Hot": 423}, {"StockID": "600827", "IsZz": "2", "IsHot": "0", "Reason": "公司打造国际友好型商场，在常规服务基础上，配备双语导购、离境退税等服务", "prod_name": "百联股份", "Hot": 366}]}, {"ID": "3647", "Name": "免税店", "ZSCode": "0", "Stocks": [{"StockID": "600018", "IsZz": "2", "IsHot": "0", "Reason": "子公司上海港国际客运中心开发有限公司参股上海港中免免税品有限公司49%股份", "prod_name": "上港集团", "Hot": 390}, {"StockID": "601021", "IsZz": "2", "IsHot": "0", "Reason": "公司的参股公司上海春秋中免免税品有限公司有免税牌照", "prod_name": "春秋航空", "Hot": 333}]}, {"ID": "3642", "Name": "百货", "ZSCode": "0", "Stocks": [{"StockID": "600838", "IsZz": "2", "IsHot": "0", "Reason": "上海静安国资委旗下，主营商业零售、商铺租赁、洗染服务", "prod_name": "上海九百", "Hot": 686}, {"StockID": "600827", "IsZz": "2", "IsHot": "0", "Reason": "位于上海，公司以零售商业为主，以综合百货、连锁超市、专业连锁等三大业态为核心业务。", "prod_name": "百联股份", "Hot": 366}]}, {"ID": "3639", "Name": "机场", "ZSCode": "0", "Stocks": [{"StockID": "600115", "IsZz": "2", "IsHot": "0", "Reason": "我国三大国有骨干航空运输集团之一", "prod_name": "中国东航", "Hot": 423}, {"StockID": "600009", "IsZz": "2", "IsHot": "0", "Reason": "中国最大的三个国际中转枢纽航空港之一", "prod_name": "上海机场", "Hot": 358}, {"StockID": "601021", "IsZz": "2", "IsHot": "0", "Reason": "中国低成本航空的先行者和领跑者，中国首批民营航空公司之一", "prod_name": "春秋航空", "Hot": 333}, {"StockID": "603885", "IsZz": "2", "IsHot": "0", "Reason": "以上海为主运营基地开展航空客货运输业务", "prod_name": "吉祥航空", "Hot": 327}]}]}, {"Level1": {"ID": "3635", "Name": "四川", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "3656", "Name": "退税店", "ZSCode": "0", "Stocks": [{"StockID": "000568", "IsZz": "2", "IsHot": "0", "Reason": "泸州老窖景区内的专卖店支持退税，旅客购买白酒后可直接在景区内完成退税申请", "prod_name": "泸州老窖", "Hot": 684}]}, {"ID": "3657", "Name": "免税", "ZSCode": "801471", "Stocks": [{"StockID": "601888", "IsZz": "2", "IsHot": "0", "Reason": "公司在四川省成都市天府国际机场开设有出境免税店，并已取得成都双流国际机场免税店经营权及有税项目经营权。", "prod_name": "中国中免", "Hot": 724}]}, {"ID": "3658", "Name": "百货", "ZSCode": "0", "Stocks": [{"StockID": "600828", "IsZz": "2", "IsHot": "0", "Reason": "公司位于四川省，旗下实体零售门店数量共 23 家，经营业态包括百货商场、购物中心、奥特莱斯及超市；", "prod_name": "茂业商业", "Hot": 2511}, {"StockID": "002697", "IsZz": "2", "IsHot": "0", "Reason": "公司的主要产品为日用百货、烟酒、食品。", "prod_name": "红旗连锁", "Hot": 889}]}]}, {"Level1": {"ID": "3640", "Name": "福建", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "3664", "Name": "退税店", "ZSCode": "0", "Stocks": [{"StockID": "600693", "IsZz": "2", "IsHot": "0", "Reason": "福建东方百货管理有限公司和福建东百元洪购物广场有限公司为福建省首批退税商店名单", "prod_name": "东百集团", "Hot": 4493}, {"StockID": "600859", "IsZz": "2", "IsHot": "0", "Reason": "旗下福州王府井百货有限责任公司为福建省首批退税商店名单", "prod_name": "王府井  ", "Hot": 818}, {"StockID": "002264", "IsZz": "2", "IsHot": "0", "Reason": "公司旗下福建新华都综合百货有限公司在福建省离境退税商店名单中", "prod_name": "新华都", "Hot": 696}, {"StockID": "600436", "IsZz": "2", "IsHot": "0", "Reason": "漳州片仔癀药业股份有限公司为福建省首批退税商店名单:", "prod_name": "片仔癀  ", "Hot": 438}]}, {"ID": "3665", "Name": "免税", "ZSCode": "801471", "Stocks": [{"StockID": "600693", "IsZz": "2", "IsHot": "0", "Reason": "公司向福建省相关部门递交了关于支持公司申请免税品经营资质的请示；控股子公司兰州东方友谊向甘肃省相关部门递交了免税品经营资质的请示。", "prod_name": "东百集团", "Hot": 4493}]}, {"ID": "3667", "Name": "机场", "ZSCode": "0", "Stocks": [{"StockID": "600897", "IsZz": "2", "IsHot": "0", "Reason": "厦门机场旅客吞吐量列全国第13位,境外旅客吞吐量列全国第9位,在海西地区机场中稳居龙头。", "prod_name": "厦门空港", "Hot": 319}]}]}, {"Level1": {"ID": "3672", "Name": "其他地区退税", "ZSCode": "0", "Stocks": [{"StockID": "600088", "IsZz": "2", "IsHot": "0", "Reason": "公司无锡影视基地分公司于2025年3月成功备案成为境外旅客购物离境退税商店，目前景区仅个别商店经营可退税商品，对景区整体经营影响较小。退税商店并非免税牌照。", "prod_name": "中视传媒", "Hot": 2228}, {"StockID": "600865", "IsZz": "2", "IsHot": "0", "Reason": "公司被列为浙江省第一批离境退税商店", "prod_name": "百大集团", "Hot": 1224}, {"StockID": "000417", "IsZz": "2", "IsHot": "0", "Reason": "公司百货大楼、百大CBD、百大鼓楼名品中心金座三家门店被列为安徽省首批境外旅客购物离境退税商店", "prod_name": "合百集团", "Hot": 654}, {"StockID": "600682", "IsZz": "2", "IsHot": "0", "Reason": "2015 年 12 月 28 日, 南京新百芜湖店被授予安徽省首批离境旅客退税商店", "prod_name": "南京新百", "Hot": 484}]}, "Level2": []}], "Stocks": [], "StockList": [{"StockID": "600088", "Tag": [{"ID": "3672", "Name": "其他地区退税", "Reason": "公司无锡影视基地分公司于2025年3月成功备案成为境外旅客购物离境退税商店，目前景区仅个别商店经营可退税商品，对景区整体经营影响较小。退税商店并非免税牌照。"}], "prod_name": "中视传媒", "HotNum": 2228}, {"StockID": "002187", "Tag": [{"ID": "3648", "Name": "退税店", "Reason": "公司旗下广百百货广州多店于2022年被列入首批离境退税“即买即退”试点商店"}, {"ID": "3651", "Name": "百货", "Reason": "公司位于广州市，主要业务为百货零售，经营业态包括百货商场、购物中心、超市、专业店以及线上商品销售等。"}], "prod_name": "广百股份", "HotNum": 1700}, {"StockID": "000564", "Tag": [{"ID": "3668", "Name": "退税店", "Reason": "公司旗下在琼企业海南望海国际是海南省首批境外旅客购物离境退税定点商店"}], "prod_name": "供销大集", "HotNum": 1970}, {"StockID": "002404", "Tag": [{"ID": "3660", "Name": "退税店", "Reason": "公司全资子公司浙江嘉欣金三塔丝绸服饰有限公司前期获得浙江省首批离境退税资格"}], "prod_name": "嘉欣丝绸", "HotNum": 675}, {"StockID": "600828", "Tag": [{"ID": "3648", "Name": "退税店", "Reason": "公司深圳地区的所有门店和成都地区的人东店、光华店均为离境退税店"}, {"ID": "3658", "Name": "百货", "Reason": "公司位于四川省，旗下实体零售门店数量共 23 家，经营业态包括百货商场、购物中心、奥特莱斯及超市；"}], "prod_name": "茂业商业", "HotNum": 2511}, {"StockID": "002264", "Tag": [{"ID": "3664", "Name": "退税店", "Reason": "公司旗下福建新华都综合百货有限公司在福建省离境退税商店名单中"}], "prod_name": "新华都", "HotNum": 696}, {"StockID": "000417", "Tag": [{"ID": "3672", "Name": "其他地区退税", "Reason": "公司百货大楼、百大CBD、百大鼓楼名品中心金座三家门店被列为安徽省首批境外旅客购物离境退税商店"}], "prod_name": "合百集团", "HotNum": 654}, {"StockID": "301066", "Tag": [{"ID": "3660", "Name": "退税店", "Reason": "万事利丝绸成杭州首批离境退税“即买即退”试点商店"}], "prod_name": "万事利", "HotNum": 1518}, {"StockID": "000796", "Tag": [{"ID": "3668", "Name": "退税店", "Reason": "与海南驻地企业供销大集签署战略合作，在离境退税和免税、产品分销推广、供应链打造方面优势互补"}, {"ID": "3669", "Name": "免税", "Reason": "子公司海南海橡国际健康文旅投资集团有限公司经营范围包括免税商店商品销售。"}], "prod_name": "凯撒旅业", "HotNum": 641}, {"StockID": "600655", "Tag": [{"ID": "3638", "Name": "退税店", "Reason": "豫园商城华宝楼和天裕楼成为首批离境退税店"}], "prod_name": "豫园股份", "HotNum": 437}, {"StockID": "600827", "Tag": [{"ID": "3638", "Name": "退税店", "Reason": "公司打造国际友好型商场，在常规服务基础上，配备双语导购、离境退税等服务"}, {"ID": "3642", "Name": "百货", "Reason": "位于上海，公司以零售商业为主，以综合百货、连锁超市、专业连锁等三大业态为核心业务。"}], "prod_name": "百联股份", "HotNum": 366}, {"StockID": "600115", "Tag": [{"ID": "3639", "Name": "机场", "Reason": "我国三大国有骨干航空运输集团之一"}], "prod_name": "中国东航", "HotNum": 423}, {"StockID": "600009", "Tag": [{"ID": "3639", "Name": "机场", "Reason": "中国最大的三个国际中转枢纽航空港之一"}], "prod_name": "上海机场", "HotNum": 358}, {"StockID": "603885", "Tag": [{"ID": "3639", "Name": "机场", "Reason": "以上海为主运营基地开展航空客货运输业务"}], "prod_name": "吉祥航空", "HotNum": 327}, {"StockID": "601111", "Tag": [{"ID": "3645", "Name": "机场", "Reason": "公司与北京市国家税务局联手正式启动了离境退税政策境外宣传项目"}], "prod_name": "中国国航", "HotNum": 414}, {"StockID": "600859", "Tag": [{"ID": "3644", "Name": "退税店", "Reason": "北京市离境退税“即买即退”试点商店名单（2024版）中包含公司"}, {"ID": "3664", "Name": "退税店", "Reason": "旗下福州王府井百货有限责任公司为福建省首批退税商店名单"}, {"ID": "3646", "Name": "免税店", "Reason": "公司自2020年获得免税品经营资质，运营1家离岛免税店和7家跨境电商体验店"}], "prod_name": "王府井  ", "HotNum": 818}, {"StockID": "600838", "Tag": [{"ID": "3642", "Name": "百货", "Reason": "上海静安国资委旗下，主营商业零售、商铺租赁、洗染服务"}, {"ID": "3638", "Name": "退税店", "Reason": "为第22批上海境外旅客购物离境退税商店之一"}], "prod_name": "上海九百", "HotNum": 686}, {"StockID": "002561", "Tag": [{"ID": "3638", "Name": "退税店", "Reason": "旗下港汇恒隆广场等商城均有离境退税服务点"}], "prod_name": "徐家汇", "HotNum": 570}, {"StockID": "600185", "Tag": [{"ID": "3649", "Name": "免税店", "Reason": "公司位于珠海市，珠海免税已成为公司的控股子公司， 公司战略定位变更为以免税业务为核心"}], "prod_name": "珠免集团", "HotNum": 796}, {"StockID": "001979", "Tag": [{"ID": "3649", "Name": "免税店", "Reason": "公司持有深圳市招商免税商品有限公司49%股权，深圳市国有免税商品（集团）有限公司持有其51%股权。"}], "prod_name": "招商蛇口", "HotNum": 457}, {"StockID": "600693", "Tag": [{"ID": "3664", "Name": "退税店", "Reason": "福建东方百货管理有限公司和福建东百元洪购物广场有限公司为福建省首批退税商店名单"}, {"ID": "3665", "Name": "免税", "Reason": "公司向福建省相关部门递交了关于支持公司申请免税品经营资质的请示；控股子公司兰州东方友谊向甘肃省相关部门递交了免税品经营资质的请示。"}], "prod_name": "东百集团", "HotNum": 4493}, {"StockID": "600004", "Tag": [{"ID": "3650", "Name": "机场", "Reason": "公司位于广州市，为白云机场的管理和运营机构， 白云机场是国内三大国际枢纽机场之一"}], "prod_name": "XD白云机", "HotNum": 342}, {"StockID": "600436", "Tag": [{"ID": "3664", "Name": "退税店", "Reason": "漳州片仔癀药业股份有限公司为福建省首批退税商店名单:"}], "prod_name": "片仔癀  ", "HotNum": 438}, {"StockID": "000089", "Tag": [{"ID": "3650", "Name": "机场", "Reason": "公司为深圳宝安国际机场的运营管理机构，主要经营航空主业以及航空主业延伸出的非航空业务"}], "prod_name": "深圳机场", "HotNum": 333}, {"StockID": "603123", "Tag": [{"ID": "3643", "Name": "百货", "Reason": "北京海淀国资委旗下的商业龙头企业，主要产品包括百货业务"}], "prod_name": "翠微股份", "HotNum": 14719}, {"StockID": "601888", "Tag": [{"ID": "3644", "Name": "退税店", "Reason": "公司开设市内购物退税店"}, {"ID": "3646", "Name": "免税店", "Reason": "全球第四大免税业务运营商，我国免税店龙头企业"}, {"ID": "3657", "Name": "免税", "Reason": "公司在四川省成都市天府国际机场开设有出境免税店，并已取得成都双流国际机场免税店经营权及有税项目经营权。"}], "prod_name": "中国中免", "HotNum": 724}, {"StockID": "002419", "Tag": [{"ID": "3651", "Name": "百货", "Reason": "公司位于深圳市，公司的平台型业务为百货和购物中心，以联营、租赁等合作模式为主；"}], "prod_name": "天虹股份", "HotNum": 671}, {"StockID": "000882", "Tag": [{"ID": "3644", "Name": "退税店", "Reason": "北京市离境退税“即买即退”试点商店名单（2024版）中包含公司"}], "prod_name": "华联股份", "HotNum": 2016}, {"StockID": "002356", "Tag": [{"ID": "3651", "Name": "百货", "Reason": "公司位于深圳市，从事国际品牌服装、鞋帽、箱包等商品的零售业务，通过在百货商场、购物中心等开设终端实体门店或线上店铺实现销售。"}], "prod_name": "赫美集团", "HotNum": 981}, {"StockID": "000568", "Tag": [{"ID": "3656", "Name": "退税店", "Reason": "泸州老窖景区内的专卖店支持退税，旅客购买白酒后可直接在景区内完成退税申请"}], "prod_name": "泸州老窖", "HotNum": 684}, {"StockID": "600515", "Tag": [{"ID": "3668", "Name": "退税店", "Reason": "公司自持物业望海国际广场为海南首家也是唯一一家被国家商务部评定为“金鼎百货店”的商业企业，是海南省首批境外旅客购物离境退税定点商店"}, {"ID": "3671", "Name": "机场", "Reason": "公司通过参股和自持物业参与海南离岛免税店业务，持有海南海航中免49.9%股权，拥有多个机场口岸免税店。此外，公司控股7家机场。"}], "prod_name": "海南机场", "HotNum": 405}, {"StockID": "601021", "Tag": [{"ID": "3639", "Name": "机场", "Reason": "中国低成本航空的先行者和领跑者，中国首批民营航空公司之一"}, {"ID": "3647", "Name": "免税店", "Reason": "公司的参股公司上海春秋中免免税品有限公司有免税牌照"}], "prod_name": "春秋航空", "HotNum": 333}, {"StockID": "000785", "Tag": [{"ID": "3646", "Name": "免税店", "Reason": "居然之家海南三亚免税店已于2020年年底开业"}], "prod_name": "居然智家", "HotNum": 1832}, {"StockID": "600628", "Tag": [{"ID": "3638", "Name": "退税店", "Reason": "为上海首批 27 家退税定点消费商店之一，为境外旅客提供离境退税率为 11%的服务"}], "prod_name": "新世界  ", "HotNum": 735}, {"StockID": "600897", "Tag": [{"ID": "3667", "Name": "机场", "Reason": "厦门机场旅客吞吐量列全国第13位,境外旅客吞吐量列全国第9位,在海西地区机场中稳居龙头。"}], "prod_name": "厦门空港", "HotNum": 319}, {"StockID": "600682", "Tag": [{"ID": "3672", "Name": "其他地区退税", "Reason": "2015 年 12 月 28 日, 南京新百芜湖店被授予安徽省首批离境旅客退税商店"}], "prod_name": "南京新百", "HotNum": 484}, {"StockID": "600612", "Tag": [{"ID": "3638", "Name": "退税店", "Reason": "上海老凤祥银楼总店等 5 家上海地区老凤祥银楼连锁店成为国内“境外旅客离境退税政策试点门店”"}], "prod_name": "老凤祥  ", "HotNum": 616}, {"StockID": "600833", "Tag": [{"ID": "3638", "Name": "退税店", "Reason": "公司凭借旗下第一医药商店经营服务特色及市场影响力, 获得了上海市首批境外旅客购物离境退税定点商店的资质"}], "prod_name": "第一医药", "HotNum": 423}, {"StockID": "002697", "Tag": [{"ID": "3658", "Name": "百货", "Reason": "公司的主要产品为日用百货、烟酒、食品。"}], "prod_name": "红旗连锁", "HotNum": 889}, {"StockID": "600018", "Tag": [{"ID": "3647", "Name": "免税店", "Reason": "子公司上海港国际客运中心开发有限公司参股上海港中免免税品有限公司49%股份"}], "prod_name": "上港集团", "HotNum": 390}, {"StockID": "600865", "Tag": [{"ID": "3672", "Name": "其他地区退税", "Reason": "公司被列为浙江省第一批离境退税商店"}], "prod_name": "百大集团", "HotNum": 1224}, {"StockID": "603069", "Tag": [{"ID": "3669", "Name": "免税", "Reason": "公司拟收购海南旅投免税品有限公司100%股权"}], "prod_name": "海汽集团", "HotNum": 1098}, {"StockID": "000886", "Tag": [{"ID": "3669", "Name": "免税", "Reason": "海南国资委旗下，海南省最大的高速公路经营管理及房地产开发企业，是海汽集团的第二大股东，海旅免税为海汽集团子公司。"}], "prod_name": "海南高速", "HotNum": 791}, {"StockID": "002163", "Tag": [{"ID": "3669", "Name": "免税", "Reason": "2025年3月互动易:控股股东海南控股于2021年作出免税资产注入承诺，旨在推动公司向免税业务转型。"}], "prod_name": "海南发展", "HotNum": 935}, {"StockID": "000524", "Tag": [{"ID": "3649", "Name": "免税店", "Reason": "控股股东岭南集团书面表示该集团正在推进申请免税品经营资质的相关工作，并已向广州市政府相关部门正式提交关于支持其申请免税业务经营牌照的请示。"}], "prod_name": "岭南控股", "HotNum": 1438}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 669, "ComNum": 709, "errcode": "0", "t": 0.006971999999999978}