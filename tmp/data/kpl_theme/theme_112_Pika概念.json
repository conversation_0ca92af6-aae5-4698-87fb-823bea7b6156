{"ID": "112", "Name": "Pika概念", "BriefIntro": "2023年11月29日AI视频生成公司Pika Labs官宣了最新的视频生成模型Pika1.0，并推出了新网站。", "ClassLayer": "2", "Desc": "", "PlateSwitch": "2", "StkSwitch": "2", "Introduction": "<p><strong>题材相关新闻及介绍:</strong></p><p>一、AI视频产业链动态</p><p>1、2023年12月12日</p><p>李飞飞的斯坦福团队同谷歌合作，推出了用于生成逼真视频的扩散模型W.A.L.T。英伟达高级科学家Jim Fan转发评论道:2022年是影像之年，2023是声波之年，而2024，是视频之年!</p><p>2、2023年11月29日</p><p>AI视频生成公司Pika Labs官宣了最新的视频生成模型Pika1.0，并推出了新网站。至今，成立仅6个月的Pika已经完成了三轮融资，总金额5500万美元，估值超10亿元人民币。</p><p>二、Pika公司介绍</p><p>Pika Labs的创始团队目前只有4个人。联合创始人郭文景，是来自杭州的一位“95后”女孩，曾被誉为“天才少女”。郭文景与另一位联合创始人孟晨琳同为斯坦福AI Lab的华人女博士，今年4月，二人一起从斯坦福退学创业。</p><p>郭文景的父亲，是A股上市公司信雅达实控人郭华强。信雅达发布公告，承认郭文景与郭华强的父女关系，但“除上述关系外，公司与Pika无其他关系。截至目前，郭文景未在公司担任任何职务，公司未投资Pika，也未与Pika有任何业务往来。&quot;</p><p>三、Pika应用功能介绍</p><p>Pika1.0能够生成和编辑3D动画、动漫、卡通和电影。几乎不需要任何门槛，用户只需要输入一句话，就可以生成想要的各种风格的视频。此外，用户还能够通过Pika实现画布延展、局部修改、视频时长拓展等编辑需求。</p><p>在宣传片中，Pika1.0拥有强大的语义理解能力，输入“马斯克穿着太空服，3D动画”的关键词，一个身穿太空服的卡通马斯克便出现了，身后还有SpaceX的火箭。文生视频的清晰度和连贯性，远超市面上其他AI视频生成产品。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202312/1702370814532073.png\" title=\"1701943339615661.png\" alt=\"hjdhjd.png\"/></p><p>四、Pika应用历程</p><p>最初，Pika只支持文生视频，后来逐渐支持图片转视频、相机控制、文字和Logo嵌入视频中等。Pika1.0宣传片中的许多功能，目前Discord上的版本并不支持，只能等网页版开放测评后验证。Pika也并非第一次在众人前亮相。</p><p>今年11月初，《流浪地球3》的发布会上，电影工业化实验室G!Lab言宣成立。郭帆导演介绍了一批战略合作的科技公司，包括商汤科技、小米、华为等，还有Pika Labs。</p>", "CreateTime": "1701943042", "UpdateTime": "0", "Table": [{"Level1": {"ID": "968", "Name": "视频数据", "ZSCode": "0", "Stocks": [{"StockID": "000681", "IsZz": "2", "IsHot": "0", "Reason": "图片数据资源类国内第一", "prod_name": "视觉中国", "Hot": 2358}, {"StockID": "300182", "IsZz": "2", "IsHot": "0", "Reason": "影视版权运营、版权分发市占率第一", "prod_name": "捷成股份", "Hot": 496}]}, "Level2": []}, {"Level1": {"ID": "969", "Name": "视频转码", "ZSCode": "0", "Stocks": [{"StockID": "603189", "IsZz": "2", "IsHot": "0", "Reason": "视频转码相关营收A股第二，1.47亿", "prod_name": "网达软件", "Hot": 1104}, {"StockID": "300079", "IsZz": "2", "IsHot": "0", "Reason": "高清视频解决方案营收2.37亿", "prod_name": "数码视讯", "Hot": 758}, {"StockID": "688039", "IsZz": "2", "IsHot": "0", "Reason": "视频转码相关营收A股第一，3.01亿", "prod_name": "当虹科技", "Hot": 652}]}, "Level2": []}, {"Level1": {"ID": "970", "Name": "视频会议", "ZSCode": "0", "Stocks": [{"StockID": "002771", "IsZz": "2", "IsHot": "0", "Reason": "视频会议相关营收A股第四，4.61亿", "prod_name": "真视通", "Hot": 1650}, {"StockID": "603660", "IsZz": "2", "IsHot": "0", "Reason": "视频会议相关营收A股第二，7.57亿", "prod_name": "苏州科达", "Hot": 1316}, {"StockID": "300578", "IsZz": "2", "IsHot": "0", "Reason": "视频会议相关营收A股第三，6.46亿", "prod_name": "会畅通讯", "Hot": 1302}, {"StockID": "300264", "IsZz": "2", "IsHot": "0", "Reason": "视频会议相关营收A股第五，1.37亿", "prod_name": "佳创视讯", "Hot": 550}, {"StockID": "300628", "IsZz": "2", "IsHot": "0", "Reason": "视频会议相关营收A股第一，12.99亿", "prod_name": "亿联网络", "Hot": 545}]}, "Level2": []}, {"Level1": {"ID": "971", "Name": "视频创作", "ZSCode": "0", "Stocks": [{"StockID": "600571", "IsZz": "2", "IsHot": "0", "Reason": "公司董事长女儿旗下AI视频应用“Pika”已获5500万美元融资", "prod_name": "信雅达  ", "Hot": 2067}, {"StockID": "300624", "IsZz": "2", "IsHot": "0", "Reason": "万兴喵影、万兴录演、万兴播爆视频创作应用", "prod_name": "万兴科技", "Hot": 1061}, {"StockID": "301171", "IsZz": "2", "IsHot": "0", "Reason": "旗下应用Kreado AI可实现视频换脸功能", "prod_name": "易点天下", "Hot": 843}]}, "Level2": []}, {"Level1": {"ID": "1060", "Name": "模型算法", "ZSCode": "0", "Stocks": [{"StockID": "002657", "IsZz": "2", "IsHot": "0", "Reason": "公司智能客服采用Transformer神经网络算法", "prod_name": "中科金财", "Hot": 9955}, {"StockID": "300229", "IsZz": "2", "IsHot": "0", "Reason": "拓天大模型采用Transformer技术架构", "prod_name": "拓尔思", "Hot": 1376}, {"StockID": "002362", "IsZz": "2", "IsHot": "0", "Reason": "汉王天地模型采用Transformer技术架构", "prod_name": "汉王科技", "Hot": 1069}, {"StockID": "301078", "IsZz": "2", "IsHot": "0", "Reason": "KidsGPT大模型采用transformer技术架构", "prod_name": "孩子王", "Hot": 1014}, {"StockID": "300002", "IsZz": "2", "IsHot": "0", "Reason": "人工智能催收方案“泰岳小催”采用Transformer架构", "prod_name": "神州泰岳", "Hot": 706}]}, "Level2": []}], "Stocks": [], "StockList": [{"StockID": "300182", "Tag": [{"ID": "968", "Name": "视频数据", "Reason": "影视版权运营、版权分发市占率第一"}], "prod_name": "捷成股份", "HotNum": 496}, {"StockID": "000681", "Tag": [{"ID": "968", "Name": "视频数据", "Reason": "图片数据资源类国内第一"}], "prod_name": "视觉中国", "HotNum": 2358}, {"StockID": "688039", "Tag": [{"ID": "969", "Name": "视频转码", "Reason": "视频转码相关营收A股第一，3.01亿"}], "prod_name": "当虹科技", "HotNum": 652}, {"StockID": "603189", "Tag": [{"ID": "969", "Name": "视频转码", "Reason": "视频转码相关营收A股第二，1.47亿"}], "prod_name": "网达软件", "HotNum": 1104}, {"StockID": "300079", "Tag": [{"ID": "969", "Name": "视频转码", "Reason": "高清视频解决方案营收2.37亿"}], "prod_name": "数码视讯", "HotNum": 758}, {"StockID": "300628", "Tag": [{"ID": "970", "Name": "视频会议", "Reason": "视频会议相关营收A股第一，12.99亿"}], "prod_name": "亿联网络", "HotNum": 545}, {"StockID": "603660", "Tag": [{"ID": "970", "Name": "视频会议", "Reason": "视频会议相关营收A股第二，7.57亿"}], "prod_name": "苏州科达", "HotNum": 1316}, {"StockID": "300578", "Tag": [{"ID": "970", "Name": "视频会议", "Reason": "视频会议相关营收A股第三，6.46亿"}], "prod_name": "会畅通讯", "HotNum": 1302}, {"StockID": "002771", "Tag": [{"ID": "970", "Name": "视频会议", "Reason": "视频会议相关营收A股第四，4.61亿"}], "prod_name": "真视通", "HotNum": 1650}, {"StockID": "300264", "Tag": [{"ID": "970", "Name": "视频会议", "Reason": "视频会议相关营收A股第五，1.37亿"}], "prod_name": "佳创视讯", "HotNum": 550}, {"StockID": "600571", "Tag": [{"ID": "971", "Name": "视频创作", "Reason": "公司董事长女儿旗下AI视频应用“Pika”已获5500万美元融资"}], "prod_name": "信雅达  ", "HotNum": 2067}, {"StockID": "300624", "Tag": [{"ID": "971", "Name": "视频创作", "Reason": "万兴喵影、万兴录演、万兴播爆视频创作应用"}], "prod_name": "万兴科技", "HotNum": 1061}, {"StockID": "301171", "Tag": [{"ID": "971", "Name": "视频创作", "Reason": "旗下应用Kreado AI可实现视频换脸功能"}], "prod_name": "易点天下", "HotNum": 843}, {"StockID": "002362", "Tag": [{"ID": "1060", "Name": "模型算法", "Reason": "汉王天地模型采用Transformer技术架构"}], "prod_name": "汉王科技", "HotNum": 1069}, {"StockID": "300229", "Tag": [{"ID": "1060", "Name": "模型算法", "Reason": "拓天大模型采用Transformer技术架构"}], "prod_name": "拓尔思", "HotNum": 1376}, {"StockID": "301078", "Tag": [{"ID": "1060", "Name": "模型算法", "Reason": "KidsGPT大模型采用transformer技术架构"}], "prod_name": "孩子王", "HotNum": 1014}, {"StockID": "002657", "Tag": [{"ID": "1060", "Name": "模型算法", "Reason": "公司智能客服采用Transformer神经网络算法"}], "prod_name": "中科金财", "HotNum": 9955}, {"StockID": "300002", "Tag": [{"ID": "1060", "Name": "模型算法", "Reason": "人工智能催收方案“泰岳小催”采用Transformer架构"}], "prod_name": "神州泰岳", "HotNum": 706}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 33, "ComNum": 435, "errcode": "0", "t": 0.009353999999999973}