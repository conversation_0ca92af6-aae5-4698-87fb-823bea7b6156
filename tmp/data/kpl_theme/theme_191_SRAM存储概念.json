{"ID": "191", "Name": "SRAM存储概念", "BriefIntro": "2025年02月1日，晶圆级AI芯片厂商Cerebras Systems宣布，它将在美国服务器上托管 DeepSeek 的突破性 R1 人工智能模型，承诺速度比基于 GPU 的解决方案快 57 倍。\n官方资料显示，WSE-3依然是采用了一整张12英寸晶圆来制作，基于台积电5nm制程，芯片面积为46225平方毫米，拥有的晶体管数量达到了4万亿个，拥有90万个AI核心，44GB片上SRAM，整体的内存带宽为21PB/s，结构带宽高达214PB/s。使得WSE-3具有125 FP16 PetaFLOP", "ClassLayer": "1", "Desc": "", "PlateSwitch": "1", "StkSwitch": "2", "Introduction": "<p><strong>题材相关新闻：</strong></p><p><br/></p><p>2025年02月1日，<span style=\"box-sizing: border-box;vertical-align: inherit;\"><span style=\"box-sizing: border-box;vertical-align: inherit;\"><span leaf=\"\">Cerebras Systems</span></span></span><span style=\"box-sizing: border-box;vertical-align: inherit;\"><span style=\"box-sizing: border-box;vertical-align: inherit;\"><span leaf=\"\">宣布，它将</span></span></span><span style=\"box-sizing: border-box;vertical-align: inherit;\"><span style=\"box-sizing: border-box;vertical-align: inherit;\"><span leaf=\"\">在美国服务器上托管 DeepSeek 的突破性 R1 人工智能模型</span></span></span><span style=\"box-sizing: border-box;vertical-align: inherit;\"><span style=\"box-sizing: border-box;vertical-align: inherit;\"><span leaf=\"\">，承诺速度比基于 GPU 的解决方案快 57 倍，官方资料显示，WSE-3依然是采用了一整张12英寸晶圆来制作，基于台积电5nm制程，芯片面积为46225平方毫米，拥有的晶体管数量达到了4万亿个，拥有90万个AI核心，44GB片上SRAM，整体的内存带宽为21PB/s，结构带宽高达214PB/s。使得WSE-3具有125 FP16 PetaFLOPS的峰值性能，相比上一代的WSE-2提升了1倍。</span></span></span></p><p>世界最快大模型Grog登场!每秒500 token破纪录，自研LPU是英伟达GPU的10倍，Grog的LPU在其系统中没有采用高带宽存储器 (HBM)。它使用的是SRAM，其速度比GPU所用的存储器快约20倍。</p><p><strong>题材介绍：</strong></p><p>1、SRAM释义</p><p>SRAM（Static Random Access Memory）静态随机存储器，是一种随机存储器，只要保持通电，里面的数据就可以一直保存。</p><p>2、SRAM相较DRAM的优势</p><p>RAM主要分为两类，SRAM（Static RAM）、DRAM（Dynamic RAM），其中，SRAM的S是Static的缩写，全称是静态随机存取存储器；而DRAM的D是Dynamic的缩写，全称是动态随机存取存储器。</p><p>1)从成本角度与AI的关系</p><p>从成本角度来看，SRAM和DRAM在 AI 领域的应用中扮演着不同的角色，SRAM 由于其较高的成本，通常用于高速缓存中，以提高 CPU与其他存储设备之间的数据交换速度。</p><p>而DRAM则因其较低的成本(仅需一个场效应管和一个电容)，被广泛应用于内存中，尤其是在内存条中。这是因为 DRAM能够提供更大的存储容量，对于需要大量数据存储的AI 应用来说，这种大容量存储的成本效益更为显著。</p><p>在AI 的发展过程中，随着核心数量的增加，对DRAM通道数量的需求也随之增加。这是因为DRAM相比SRAM 更具成本效益，能够更有效地管理和利用存储资源。</p><p>此外，DRAM的低功耗特性也使得它成为AI 芯片设计中的一个重要考虑因素，尤其是在追求能效比的背景下，然而，尽管DRAM在成本上具有优势，但 SRAM的高存取速度和较低的制造成本使其在某些特定场景下仍然有其独特的价值。</p><p>例如，在更高的频率下，SRAM的功耗与DRAM一样多，这使得它在实现节能AI 方面发挥了作用，且SRAM作为一种不需要刷新电路即可保存数据的技术，对于那些需要周期性更新数据的 AI 应用来说，可能是一个更优的选择。</p><p>综上所述，从成本角度来看，DRAM在AI应用中主要由于其成本效益而被广泛采用，尤其是在需要较大存储容量和高效管理存储资源的情况下。而SRAM则因其较高的存取速度和较低的制造成本，在某些特定的应用场景下仍然具有不可替代的地位。</p><p>2）在AI芯片设计中的能效</p><p>SRAM 的主要优点在于其高能效比和高精度。随着存内逻辑技术的发展，SRAM在保持高速度的同时，还能实现高精度的操作。此外，SRAM不需要刷新电路即可保存内部存储的数据，而DRAM则需要刷新充电一次，否则内部的数据即会丢失。这意味着在AI 芯片设计中，SRAM能够更有效地利用有限的电力资源，提高整体的能效比。</p><p>3、在AI领域中的最新应用案例</p><p>基于 SRAM的存内计算技术，可以助力实现节能AI的应用。这种技术的应用范围广泛，从智能扬声器等消费电子产品到自动化工厂等工业应用都需要关注其能源效率 。这说明 SRAM不仅在传统的AI 应用中发挥作用，也在节能和能效优化方面展现出潜力。</p><p>4、未来的发展趋势</p><p>1）在AI应用中的应用与挑战</p><p>SRAM作为一种存算一体片，其研究现状与发展趋势显示，尽管面临技术挑战，但仍被视为AI 应用的重要组成部分。特别是在NVIDIA公司的CUDA生态系统主导的大部分AI 应用开发中，GPU因其性能优势而占据主导地位。</p><p>这表明，尽管SRAM在AI应用中存在一定的挑战，如内存壁瓶颈等问题，但其未来发展仍然值得关注。</p><p>2）从SRAM的角度来看，虽然没有直接提及其成本效益，但可以推测，随着人工智能技术的发展和应用，对存储器的需求将会增加，这可能会影响到SRAM的成本和效益。</p><p>特别是在考虑到AI技术对能源消耗巨大的背景下，SRAM作为一种低功耗、高可靠性的存储解决方案，可能会因其节能特性而变得更加重要。</p>", "CreateTime": "1708416716", "UpdateTime": "0", "Table": [], "Stocks": [{"StockID": "603776", "IsZz": "2", "IsHot": "0", "Reason": "公司称其投资的磁存储芯片（PMRAM）是新型存储器的一种， 未来将是 SRAM、DRAM 的发展方向，特别适合在 AI 产品上应用，现产品目前已经完成试生产", "prod_name": "永安行  ", "Hot": 5435}, {"StockID": "001287", "IsZz": "2", "IsHot": "0", "Reason": "招股书显示公司的存储器产品包含SRAM、DRAM、Flash等。", "prod_name": "中电港", "Hot": 3990}, {"StockID": "301306", "IsZz": "2", "IsHot": "0", "Reason": "据公司招股书，电子元器件检测筛选方面可实现对EEPROM、SRAM、NOR fl ash等存储芯片读写擦除功能的自动测试。", "prod_name": "西测测试", "Hot": 3396}, {"StockID": "300223", "IsZz": "2", "IsHot": "0", "Reason": "北京矽成为控股公司，其经营实体主要为ISSI，ISSI主营业务为集成电路存储芯片、模拟芯片及其衍生产品的研发和销售，是全球DRAM、SRAM行业龙头之一。", "prod_name": "北京君正", "Hot": 2513}, {"StockID": "603986", "IsZz": "2", "IsHot": "0", "Reason": "公司在成立的时候，最早做的是SRAM产品。", "prod_name": "兆易创新", "Hot": 2433}, {"StockID": "002180", "IsZz": "2", "IsHot": "0", "Reason": "互动易回复：公司拥有大容量Flash、SRAM以及丰富内外设的高性能、高适配型MCU系列新品。", "prod_name": "纳思达", "Hot": 1785}, {"StockID": "688049", "IsZz": "2", "IsHot": "0", "Reason": "公司积累了丰富的端侧 AI 芯片技术储备，通过基于 SRAM 的模数混合设计的存内计算（MMSCIM）技术和三核异构架构设计，实现了高效能、低功耗的端侧 AI 芯片", "prod_name": "炬芯科技", "Hot": 829}, {"StockID": "300053", "IsZz": "2", "IsHot": "0", "Reason": "公司高可靠数据存储器产品主要包括SRAM系列产品及超大容量 NANDFLASH 模块系列产品。", "prod_name": "航宇微", "Hot": 814}, {"StockID": "301095", "IsZz": "2", "IsHot": "0", "Reason": "在电性测试设备方面，公司新一代通用型高性能半导体参数测试设备（T4000型号），可覆盖LOGIC，CIS,DRAM, SRAM, FLASH,BCD等所有产品的测试需求。", "prod_name": "广立微", "Hot": 792}, {"StockID": "300227", "IsZz": "2", "IsHot": "0", "Reason": "公司将通过半导体全资子公司与南京初芯开展全面的战略合作，将SRAM的技术革新导入到本公司现有客户", "prod_name": "光韵达", "Hot": 753}, {"StockID": "300455", "IsZz": "2", "IsHot": "0", "Reason": "子公司轩宇空间开发了SRAM、A1013Q、SOC2012等存储芯片产品。", "prod_name": "航天智装", "Hot": 621}, {"StockID": "688709", "IsZz": "2", "IsHot": "0", "Reason": "招股书显示公司拥有建立高性能、高性价比千万门级SRAM型FPGA器件自主研发与工程化应用能力。", "prod_name": "成都华微", "Hot": 612}, {"StockID": "688053", "IsZz": "2", "IsHot": "0", "Reason": "公司开发了静态存储芯片SRAM的测试筛选技术，实现对随机SRAM高覆盖率、高效的测试，可高效实现对随机静态存储芯片SRAM测试筛选试验目的", "prod_name": "思科瑞  ", "Hot": 433}], "StockList": [{"StockID": "300223", "Tag": [], "prod_name": "北京君正", "HotNum": 2513}, {"StockID": "001287", "Tag": [], "prod_name": "中电港", "HotNum": 3990}, {"StockID": "688709", "Tag": [], "prod_name": "成都华微", "HotNum": 612}, {"StockID": "301095", "Tag": [], "prod_name": "广立微", "HotNum": 792}, {"StockID": "300053", "Tag": [], "prod_name": "航宇微", "HotNum": 814}, {"StockID": "301306", "Tag": [], "prod_name": "西测测试", "HotNum": 3396}, {"StockID": "688053", "Tag": [], "prod_name": "思科瑞  ", "HotNum": 433}, {"StockID": "002180", "Tag": [], "prod_name": "纳思达", "HotNum": 1785}, {"StockID": "300455", "Tag": [], "prod_name": "航天智装", "HotNum": 621}, {"StockID": "603986", "Tag": [], "prod_name": "兆易创新", "HotNum": 2433}, {"StockID": "300227", "Tag": [], "prod_name": "光韵达", "HotNum": 753}, {"StockID": "688049", "Tag": [], "prod_name": "炬芯科技", "HotNum": 829}, {"StockID": "603776", "Tag": [], "prod_name": "永安行  ", "HotNum": 5435}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 158, "ComNum": 641, "errcode": "0", "t": 0.010041999999999995}