{"ID": "195", "Name": "半导体存储器", "BriefIntro": "存储器是用来存储和读取各种数据信息的记忆部件，按掉电后数据是否保存分为：易失性存储（RAM）、非易失性存储（ROM）。", "ClassLayer": "3", "Desc": "", "PlateSwitch": "1", "StkSwitch": "2", "Introduction": "<p><strong>题材介绍：</strong></p><p>一、半导体存储：数据的蓄水池<strong><br/></strong></p><p>1、存储产业链</p><p>存储器是指利用磁性材料或半导体等材料作为介质进行信息存储的器件。半导体存储器利用半导体介质贮存电荷以实现信息存储，存储与读取过程体现为电荷的贮存或释放，半导体存储是集成电路的重要分支。</p><p>根据世界半导体贸易统计组织(WSTS)数据，2022年全球集成电路产业规模为 4744.02亿美元，其中存储芯片规模为1297.67 亿美元，约占集成电路产业总体规模的 22.6%，与逻辑芯片共同构成集成电路产业的两大支柱。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457319887209.png\" title=\"1709455884156805.png\" alt=\"image.png\"/></p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457319498597.png\" title=\"1709455915157505.png\" alt=\"image.png\"/></p><p>2、存储分类</p><p>存储芯片作为全球半导体行业第一大细分领域可分为易失性和非易失性两类，易失性存储又可分为动态随机存储 (DRAM)和静态随机存储 (SRAM)。</p><p>其中DRAM具备集成度高、低功耗、低成本、体积小等显著优势，通常用于智能手机及服务器内存。非易失性存储主要包括NAND FLASH和NOR FLASH，其中NAND被广泛用于SSD、eMMC/EMCP、U盘等高端大容量产品，NOR则主要用于智能穿戴、汽车电子、AMOLED等领域。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457319163655.png\" title=\"1709456046802525.png\" alt=\"image.png\"/></p><p>1）易失性存储（RAM）</p><p>易失性存储是运行程序临时数据的存储媒介，供CPU读写处理，占存储市场的57%，RAM需要维持通电以临时保存数据供主系统CPU读写和处理。</p><p>由于RAM可以实现对数据的高速读写，可作为操作系统或其他正在运行中的程序的临时数据存储媒介，在此基础上，RAM根据是否需要周期性刷新以维持数据存储，进一步分为动态随机存取存储器 (DRAM)和静态随机存取存储器 (SRAM)。</p><p>动态随机存取存储器(DRAM)需要在通电状态下通过周期性刷新来维持数据，静态随机存取存储器(SRAM)则不需要周期性刷新。SRAM的速度更快、耗电量较低但存储器容量较低且制造成本较高，通常用于缓存;DRAM 的成本更低，密度更大，主要用作主处理器存储器。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457319958499.png\" title=\"1709456203875089.png\" alt=\"image.png\"/></p><p>2）非易失性存储（ROM）</p><p>非易失性存储主要指只读存储器 (ROM)，无需持续通电亦能长久保存数据的存储器，占存储市场的43%。</p><p>ROM包括掩膜只读存储器 (Mask ROM)、可编程只读存储器(PROM)、可编程可擦除只读存储器 (EPROM)、电可擦除可编程只读存储器 (EEPROM)和快闪存储器 (Flash)等不同阶段产生的产品。</p><p>Flash 作为主要应用包括NAND Flash和NOR Flash；NAND Flash (占40%)是一种半导体单元串联排列的闪存，其闪存单元垂直排列,可实现大容量化；同时无需记忆各单元的位置,写入速度很快。</p><p>由于其小型化和大容量化，NAND闪存常常被用作各种移动设备和电子产品的存储设备，是目前全球市场大容量非易失存储的主流技术方案。</p><p>NOR Flash是一种半导体单元并联排列的闪存由于并联排列的它的数据检索快读取速度快且数据安全性高；但NOR由于要记住各个单元的地址电路较为复杂储存数据的空间小，很难实现大容量化，在小容量场景中具备经济效益。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457319893058.png\" title=\"1709456328422190.png\" alt=\"image.png\"/></p><p>二、未来存储发展方向：高密度、大容量<br/></p><p>1、从平面到4D，NAND存储密度不断提升</p><p>提高存储单元的可存储数位 (bit)量和提升3D NAND Flash 的堆叠层数是存储密度提升的主要方式。</p><p>NAND Flash 的进化过程中最重要的趋势就是每个单元拥有更高的密度，从Single Level Cell(SLC)对应1bit、Multi Level Cell(MLC)对应2bit、Triple Level Cell(TLC)对应 3bit到Quadruple Level Cell(QLC)对应4bit 发展，存储密度得到提升。</p><p>但由于在 2D 形式下，单位存储单元密度提升会带来擦写次数减少、可靠性降低和单元间干涉现象严重等问题，3D NAND 技术应运而生。</p><p>3D NAND 大幅减少了单元 (CelI) 之间的干扰影响，提高了单元自身的特性，并持续提高积层单数就能够实现数据容量的扩大及成本节约，其读写速度、功耗、成本、耐久性、数据传输速度及容量等均展现出卓越的优势。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457319557922.png\" title=\"1709456680473861.png\" alt=\"image.png\"/></p><p>为提升性能和容量，3DNAND的存储单元基本结构从浮栅结构FG逐步升级。以美光为例，当3DNAND通过创建垂直单元堆栈实现空间缩小，而单元间产生的电容合导致编程时间增加或性能降低时，美光设计了RG NAND结构。</p><p>通过加入一层非导电的氮化硅(SiN)层充当存储电荷的绝缘体，减轻了电容合;利用金属作为控制栅极以延长使用寿命;最终读取和擦除速度比当前3DNAND快两倍。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457319722400.png\" title=\"1709456727614919.png\" alt=\"image.png\"/></p><p>3DNAND为闪存市场主流产品，22年应用占比超80%，随存储密度要求提升层数增加。自2013年后，3D NAND 的堆叠层数出现了快速增长。</p><p>2015年推出了48层NAND，2017年推出64 层，2019年96层，2020年128层，2021年176层，2022年232层。目前，三星、美光、SK 海力士、长江存储等均超过了200层。根据 Yole数据128层NAND仍为主要工艺232层NAND将随数据中心等需求增加加速渗透。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457319251087.png\" title=\"1709456789368945.png\" alt=\"image.png\"/></p><p>2、从1X到1Z，DRAM制程不断缩小</p><p>DRAM占据全球存储器市场第一大份额，手机和服务器推动DRAM 市场需求扩大。</p><p>DRAM按照产品分类可分为DDR/LPDDR/GDDR和传统型(Legacy/SDR)DRAM，其中DDR(DDR SDRAM 双倍数据速率同步动态随机存取存储器)是DRAM应用最广的产品类型。</p><p>通常SDRAM在一个时钟周期内只传输一次数据，而DDR则是一个时钟周期内可传输两次。随着CPU内核数增加，为保证每个核的带宽不变，整体DDR的带宽(Bandwidth)及功耗要求不断提升，DDR由第一代升级至第五代DDR5。相比DDR4，DDR5数据传输速度与有效带宽翻倍。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457319611666.png\" title=\"1709456886315520.png\" alt=\"image.png\"/></p><p>先进的工艺节点与封装技术的演进是DDR升级的核心要素。每个节点级数都代表芯片中晶体管和电容器的最小组件缩小，14年三星率先实现20nm量产(4Gb DDR3DRAM)，</p><p>此后DRAM制程每两年代，从1Xnm(1nm-19nm)到1Ynm(14nm-16nm)到1Znm (12-14nm，DDR4X/5及LPDDR4X/5)；22年美光推出了1BDRAM技术，该技术初步可使LPDDR5X效率提升15%。</p><p>在此基础上，硅通孔(TSV)技术可实现DRAM芯片的多层堆叠，提升模块容量;例如三星DDR5利用TSV技术堆叠了8层16GBDRAM芯片，DDR5模块容量提升至512GB。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457319579054.png\" title=\"1709456963643747.png\" alt=\"image.png\"/></p><p>3、预计未来5年数据中心对DRAM的需求复合增速将超30%</p><p>DDR5作为高性价比的内存形式将成为DRAM的主要出货类型，根据Yole及IDC 数据，尽管23年PC、服务器需求恢复有限，DDR5 渗透率提升较慢，随着AI、服务器及物联网带来的计算需求增加，未来五年，DRAM 需求将快速增长。</p><p>其中DDR5作为高性能低功耗的新一代产品，将广泛应用于大多数的计算场景中，成为主流产品，25年后渗透率将超60%。</p><p>尽管HBM (High Bandwidth Memory)在高度并行计算、计算机视觉AI等应用中有卓越的性能优势，但考虑其成本、内存灵活性等因素，DDR5将在较长时间内占据主要的内存应用市场。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457319822188.png\" title=\"1709457100397991.png\" alt=\"image.png\"/></p><p>4、HBM为代表的新能内存</p><p>“内存墙”的限制将促进TSV(硅通孔)、混合键合为基础的先进内存封装技术26年占比将在20年基础上增加两倍以上。随着数据密集型应用不断增长，目前处理器算力超过存储芯片存取能力，内存和处理单元之间的数据传输带宽受限即受到“内存墙”的阻碍；</p><p>以HBM(高带宽存储器，High Bandwidth Memory)为代表的新型内存封装形式应运而生。根据 Yole 数据，2026年内存封装市场将增至198亿美元，其中TSV、混合键合等先进封装技术占比将由2020 年不到5%增至约18%。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457320450962.png\" title=\"1709457207891012.png\" alt=\"image.png\"/></p><p>根据Trendforce 数据，高端GPU 需求提升将拉动HBM23 年需求量增加58%，24年将再增加30%HBM是利用TSV和热压键合等技术将DRAM芯片进行堆叠并与GPU一起封装以实现更高的传输带宽的新型内存封装形式。</p><p>从传输位宽来看，通过该种互连方式，每层DRAM 芯片有两个128 bits通道，若堆叠4层DRAM芯片对应1024bits即1024个数据引脚；</p><p>若GPU周围配置4块该类型HBM内存，则总位宽为4096bits;相比GDDR5显存16通道对应512 bits大幅提升，适用于游戏和图形处理等高并行任务对带宽要求高的应用。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457320940123.png\" title=\"1709457267636721.png\" alt=\"image.png\"/></p><p>根据Trendforce 数据，HBM市场被海力士(占53%)、三星(占38%)和美光(占9%)三大内存原厂占据。2014年，AMD与海力士合作开发出了全球第一代HBM，随后海力士相继推出了HBM2、HBM2E、HBM3等产品，内存Die堆叠层数由4层增至12层，单颗HBM容量可达24GB;</p><p>此外，三星HBM3亦开始量产，并推出了HBM-PIM(存算一体)产品;美光HBM2E于21 年开始量产，随着NVIDIA GPU H100、A100采用了HBM2e、HBM3技术，HBM应用将逐步走向成熟，成为AI服务器与高端GPU的主要封装形式。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202403/1709457319255174.png\" title=\"1709457319255174.png\" alt=\"image.png\"/></p>", "CreateTime": "1709454941", "UpdateTime": "0", "Table": [{"Level1": {"ID": "2244", "Name": "RAM存储", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "2246", "Name": "SRAM存储", "ZSCode": "0", "Stocks": [{"StockID": "001287", "IsZz": "2", "IsHot": "0", "Reason": "公司授权分销业务主要产品中包含SRAM", "prod_name": "中电港", "Hot": 3990}, {"StockID": "603986", "IsZz": "2", "IsHot": "0", "Reason": "2021年公司第一款低功耗SRAM产品规模量产", "prod_name": "兆易创新", "Hot": 2433}, {"StockID": "002180", "IsZz": "2", "IsHot": "0", "Reason": "2023年子公司极海微综合市场需求，正式推出采用55nm工艺制程，拥有大容量Flash、SRAM以及丰富片内外设的高性能、高适配型MCU系列新品", "prod_name": "纳思达", "Hot": 1785}, {"StockID": "300053", "IsZz": "2", "IsHot": "0", "Reason": "公司开发产品主要为，高可靠数据存储器 SRAM 系列产品及超大容量 NANDFLASH 模块系列产品。", "prod_name": "航宇微", "Hot": 814}, {"StockID": "688709", "IsZz": "2", "IsHot": "0", "Reason": "针对非易失可编程逻辑器件架构设计，公司采用“内嵌 Flash IP+配置 SRAM”架构，可实现器件上电后自动加载内部配置数据，无需片外加载", "prod_name": "成都华微", "Hot": 612}]}, {"ID": "2247", "Name": "DRAM存储", "ZSCode": "0", "Stocks": [{"StockID": "300650", "IsZz": "2", "IsHot": "1", "Reason": "根据子公司博思达科技(中国香港)有限公司官网：公司有存储芯片解决方案，产品有存储卡 | MicroSD / SD SDA授权、固态硬盘 | SSD、嵌入式存储 | eMMC、内存产品 | DRAM / DRAM Module / LPDDR等。", "prod_name": "太龙股份", "Hot": 10272}, {"StockID": "688525", "IsZz": "2", "IsHot": "0", "Reason": "公司掌握多种先进封装工艺，为NAND Flash芯片、DRAM芯片和SiP封装芯片的大规模量产提供支持。", "prod_name": "佰维存储", "Hot": 3468}, {"StockID": "300223", "IsZz": "2", "IsHot": "0", "Reason": "公司的存储芯片主要产品有高集成密度、高性能品质、高经济价值的 SRAM、DRAM、Flash等", "prod_name": "北京君正", "Hot": 2513}, {"StockID": "002654", "IsZz": "2", "IsHot": "0", "Reason": "2023年报称新增内存（DRAM）业务", "prod_name": "万润科技", "Hot": 2458}, {"StockID": "603986", "IsZz": "2", "IsHot": "0", "Reason": "公司存储器产品包括闪存芯片（NOR Flash、NAND Flash）和动态随机存取存储器（DRAM）", "prod_name": "兆易创新", "Hot": 2433}, {"StockID": "000021", "IsZz": "2", "IsHot": "0", "Reason": "公司控股子公司沛顿科技能够为DRAM和Flash产品提供完整的芯片终测服务，是国内存储芯片行业唯一具有竞争力的公司。", "prod_name": "深科技", "Hot": 2328}, {"StockID": "300042", "IsZz": "2", "IsHot": "0", "Reason": "公司主要经营SSD固态硬盘、DRAM内存条、嵌入式存储和移动存储产品", "prod_name": "朗科科技", "Hot": 2139}, {"StockID": "002049", "IsZz": "2", "IsHot": "0", "Reason": "公司子公司西安紫光国芯主要从事 DRAM 存储器芯片的开发与销售.", "prod_name": "紫光国微", "Hot": 1914}, {"StockID": "688008", "IsZz": "2", "IsHot": "0", "Reason": "公司目前的主营产品均属于产业链的芯片层环节,其中内存接口芯片直接面向DRAM存储器市场", "prod_name": "澜起科技", "Hot": 1718}, {"StockID": "301308", "IsZz": "2", "IsHot": "0", "Reason": "目前公司已形成eSSD（SATA+NVMe）和RDIMM两类产品，贯穿Flash与DRAM", "prod_name": "江波龙", "Hot": 1681}, {"StockID": "603936", "IsZz": "2", "IsHot": "0", "Reason": "公司2022年在EMMC，DRAM等存储类产品配合康佳芯云等客户进入了小批量生产阶段", "prod_name": "博敏电子", "Hot": 1549}, {"StockID": "688627", "IsZz": "2", "IsHot": "0", "Reason": "公司的半导体存储器件测试设备主要用于在 DRAM 等半导体存储器件的晶圆制造环节对晶圆裸片进行电参数性能和功能测试", "prod_name": "精智达  ", "Hot": 627}]}, {"ID": "2248", "Name": "HBM存储", "ZSCode": "0", "Stocks": [{"StockID": "300475", "IsZz": "2", "IsHot": "0", "Reason": "互动易称，公司2022年已向客户销售海力士HBM存储产品。", "prod_name": "香农芯创", "Hot": 1213}]}]}, {"Level1": {"ID": "2245", "Name": "ROM存储", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "2249", "Name": "NAND FLASH", "ZSCode": "0", "Stocks": [{"StockID": "688525", "IsZz": "2", "IsHot": "0", "Reason": "公司主要从事的 NAND Flash 和 DRAM 存储器领域是半导体存储器中规模最大的细分市场。", "prod_name": "佰维存储", "Hot": 3468}, {"StockID": "603933", "IsZz": "2", "IsHot": "0", "Reason": "公司产品包含“芯天下”系列存储芯片。", "prod_name": "睿能科技", "Hot": 3178}, {"StockID": "001309", "IsZz": "2", "IsHot": "0", "Reason": "公司产品主要为 NAND Flash 存储模组，产成品的成本构成中 NAND Flash 存储晶圆的占比较高。", "prod_name": "德明利", "Hot": 2284}, {"StockID": "300042", "IsZz": "2", "IsHot": "0", "Reason": "公司主要经营NAND FLASH固态存储、DRAM动态存储、嵌入式存储和移动存储产品。", "prod_name": "朗科科技", "Hot": 2139}, {"StockID": "301308", "IsZz": "2", "IsHot": "0", "Reason": "公司目前共有 5 款自研 SLC NAND Flash 小容量存储芯片实现量产；", "prod_name": "江波龙", "Hot": 1681}, {"StockID": "688385", "IsZz": "2", "IsHot": "0", "Reason": "公司现有NAND成熟量产产品为40nm/38nm工艺节点。", "prod_name": "复旦微电", "Hot": 1118}, {"StockID": "688110", "IsZz": "2", "IsHot": "0", "Reason": "公司聚焦中小容量的NAND Flash、NOR Flash、DRAM产品的研发、设计与销售。", "prod_name": "东芯股份", "Hot": 721}]}, {"ID": "2250", "Name": "NOR FLASH", "ZSCode": "0", "Stocks": [{"StockID": "603986", "IsZz": "2", "IsHot": "0", "Reason": "公司存储器产品包括闪存芯片（NOR Flash、NAND Flash）和动态随机存取存储器（DRAM）。", "prod_name": "兆易创新", "Hot": 2433}, {"StockID": "688385", "IsZz": "2", "IsHot": "0", "Reason": "目前公司所有NOR FLASH都为ETOX器件结构。", "prod_name": "复旦微电", "Hot": 1118}, {"StockID": "688416", "IsZz": "2", "IsHot": "0", "Reason": "公司现有主营产品包括 NOR Flash 存储芯片。", "prod_name": "恒烁股份", "Hot": 617}, {"StockID": "688709", "IsZz": "2", "IsHot": "0", "Reason": "公司基于 FPGA 产品的研发经验，开发了配套使用的 NOR Flash 存储器产品，并拓展了 EEPROM 等产品", "prod_name": "成都华微", "Hot": 612}, {"StockID": "688766", "IsZz": "2", "IsHot": "0", "Reason": "公司主要产品包括： NOR Flash 和 EEPROM 两大类非易失性存储器芯片、微控制器芯片以及模拟产品。", "prod_name": "普冉股份", "Hot": 553}]}, {"ID": "2251", "Name": "EEPROM", "ZSCode": "0", "Stocks": [{"StockID": "600171", "IsZz": "2", "IsHot": "0", "Reason": "公司EEPROM产品系列已经基本齐全，实现了容量从2kbit到2048kbit，各种封装形式的全覆盖", "prod_name": "上海贝岭", "Hot": 1977}, {"StockID": "688385", "IsZz": "2", "IsHot": "0", "Reason": "公司已形成 EEPROM、 NOR FLASH、 NAND FLASH 三大产品线，建立了完整的利基非挥发存储器产品架构。", "prod_name": "复旦微电", "Hot": 1118}, {"StockID": "688709", "IsZz": "2", "IsHot": "0", "Reason": "公司基于 FPGA 产品的研发经验，开发了配套使用的 NOR Flash 存储器产品，并拓展了 EEPROM 等产品", "prod_name": "成都华微", "Hot": 612}, {"StockID": "688766", "IsZz": "2", "IsHot": "0", "Reason": "公司主要产品包括： NOR Flash 和 EEPROM 两大类非易失性存储器芯片、微控制器芯片以及模拟产品。", "prod_name": "普冉股份", "Hot": 553}, {"StockID": "688123", "IsZz": "2", "IsHot": "0", "Reason": "公司拥有EEPROM、NOR Flash两大类存储芯片产品。", "prod_name": "聚辰股份", "Hot": 495}]}]}], "Stocks": [], "StockList": [{"StockID": "001287", "Tag": [{"ID": "2246", "Name": "SRAM存储", "Reason": "公司授权分销业务主要产品中包含SRAM"}], "prod_name": "中电港", "HotNum": 3990}, {"StockID": "300053", "Tag": [{"ID": "2246", "Name": "SRAM存储", "Reason": "公司开发产品主要为，高可靠数据存储器 SRAM 系列产品及超大容量 NANDFLASH 模块系列产品。"}], "prod_name": "航宇微", "HotNum": 814}, {"StockID": "002180", "Tag": [{"ID": "2246", "Name": "SRAM存储", "Reason": "2023年子公司极海微综合市场需求，正式推出采用55nm工艺制程，拥有大容量Flash、SRAM以及丰富片内外设的高性能、高适配型MCU系列新品"}], "prod_name": "纳思达", "HotNum": 1785}, {"StockID": "603986", "Tag": [{"ID": "2247", "Name": "DRAM存储", "Reason": "公司存储器产品包括闪存芯片（NOR Flash、NAND Flash）和动态随机存取存储器（DRAM）"}, {"ID": "2250", "Name": "NOR FLASH", "Reason": "公司存储器产品包括闪存芯片（NOR Flash、NAND Flash）和动态随机存取存储器（DRAM）。"}, {"ID": "2246", "Name": "SRAM存储", "Reason": "2021年公司第一款低功耗SRAM产品规模量产"}], "prod_name": "兆易创新", "HotNum": 2433}, {"StockID": "300223", "Tag": [{"ID": "2247", "Name": "DRAM存储", "Reason": "公司的存储芯片主要产品有高集成密度、高性能品质、高经济价值的 SRAM、DRAM、Flash等"}], "prod_name": "北京君正", "HotNum": 2513}, {"StockID": "688008", "Tag": [{"ID": "2247", "Name": "DRAM存储", "Reason": "公司目前的主营产品均属于产业链的芯片层环节,其中内存接口芯片直接面向DRAM存储器市场"}], "prod_name": "澜起科技", "HotNum": 1718}, {"StockID": "603936", "Tag": [{"ID": "2247", "Name": "DRAM存储", "Reason": "公司2022年在EMMC，DRAM等存储类产品配合康佳芯云等客户进入了小批量生产阶段"}], "prod_name": "博敏电子", "HotNum": 1549}, {"StockID": "688525", "Tag": [{"ID": "2247", "Name": "DRAM存储", "Reason": "公司掌握多种先进封装工艺，为NAND Flash芯片、DRAM芯片和SiP封装芯片的大规模量产提供支持。"}, {"ID": "2249", "Name": "NAND FLASH", "Reason": "公司主要从事的 NAND Flash 和 DRAM 存储器领域是半导体存储器中规模最大的细分市场。"}], "prod_name": "佰维存储", "HotNum": 3468}, {"StockID": "300042", "Tag": [{"ID": "2247", "Name": "DRAM存储", "Reason": "公司主要经营SSD固态硬盘、DRAM内存条、嵌入式存储和移动存储产品"}, {"ID": "2249", "Name": "NAND FLASH", "Reason": "公司主要经营NAND FLASH固态存储、DRAM动态存储、嵌入式存储和移动存储产品。"}], "prod_name": "朗科科技", "HotNum": 2139}, {"StockID": "002049", "Tag": [{"ID": "2247", "Name": "DRAM存储", "Reason": "公司子公司西安紫光国芯主要从事 DRAM 存储器芯片的开发与销售."}], "prod_name": "紫光国微", "HotNum": 1914}, {"StockID": "000021", "Tag": [{"ID": "2247", "Name": "DRAM存储", "Reason": "公司控股子公司沛顿科技能够为DRAM和Flash产品提供完整的芯片终测服务，是国内存储芯片行业唯一具有竞争力的公司。"}], "prod_name": "深科技", "HotNum": 2328}, {"StockID": "301308", "Tag": [{"ID": "2247", "Name": "DRAM存储", "Reason": "目前公司已形成eSSD（SATA+NVMe）和RDIMM两类产品，贯穿Flash与DRAM"}, {"ID": "2249", "Name": "NAND FLASH", "Reason": "公司目前共有 5 款自研 SLC NAND Flash 小容量存储芯片实现量产；"}], "prod_name": "江波龙", "HotNum": 1681}, {"StockID": "300475", "Tag": [{"ID": "2248", "Name": "HBM存储", "Reason": "互动易称，公司2022年已向客户销售海力士HBM存储产品。"}], "prod_name": "香农芯创", "HotNum": 1213}, {"StockID": "001309", "Tag": [{"ID": "2249", "Name": "NAND FLASH", "Reason": "公司产品主要为 NAND Flash 存储模组，产成品的成本构成中 NAND Flash 存储晶圆的占比较高。"}], "prod_name": "德明利", "HotNum": 2284}, {"StockID": "688110", "Tag": [{"ID": "2249", "Name": "NAND FLASH", "Reason": "公司聚焦中小容量的NAND Flash、NOR Flash、DRAM产品的研发、设计与销售。"}], "prod_name": "东芯股份", "HotNum": 721}, {"StockID": "603933", "Tag": [{"ID": "2249", "Name": "NAND FLASH", "Reason": "公司产品包含“芯天下”系列存储芯片。"}], "prod_name": "睿能科技", "HotNum": 3178}, {"StockID": "688385", "Tag": [{"ID": "2249", "Name": "NAND FLASH", "Reason": "公司现有NAND成熟量产产品为40nm/38nm工艺节点。"}, {"ID": "2251", "Name": "EEPROM", "Reason": "公司已形成 EEPROM、 NOR FLASH、 NAND FLASH 三大产品线，建立了完整的利基非挥发存储器产品架构。"}, {"ID": "2250", "Name": "NOR FLASH", "Reason": "目前公司所有NOR FLASH都为ETOX器件结构。"}], "prod_name": "复旦微电", "HotNum": 1118}, {"StockID": "688416", "Tag": [{"ID": "2250", "Name": "NOR FLASH", "Reason": "公司现有主营产品包括 NOR Flash 存储芯片。"}], "prod_name": "恒烁股份", "HotNum": 617}, {"StockID": "688766", "Tag": [{"ID": "2250", "Name": "NOR FLASH", "Reason": "公司主要产品包括： NOR Flash 和 EEPROM 两大类非易失性存储器芯片、微控制器芯片以及模拟产品。"}, {"ID": "2251", "Name": "EEPROM", "Reason": "公司主要产品包括： NOR Flash 和 EEPROM 两大类非易失性存储器芯片、微控制器芯片以及模拟产品。"}], "prod_name": "普冉股份", "HotNum": 553}, {"StockID": "688123", "Tag": [{"ID": "2251", "Name": "EEPROM", "Reason": "公司拥有EEPROM、NOR Flash两大类存储芯片产品。"}], "prod_name": "聚辰股份", "HotNum": 495}, {"StockID": "688709", "Tag": [{"ID": "2250", "Name": "NOR FLASH", "Reason": "公司基于 FPGA 产品的研发经验，开发了配套使用的 NOR Flash 存储器产品，并拓展了 EEPROM 等产品"}, {"ID": "2251", "Name": "EEPROM", "Reason": "公司基于 FPGA 产品的研发经验，开发了配套使用的 NOR Flash 存储器产品，并拓展了 EEPROM 等产品"}, {"ID": "2246", "Name": "SRAM存储", "Reason": "针对非易失可编程逻辑器件架构设计，公司采用“内嵌 Flash IP+配置 SRAM”架构，可实现器件上电后自动加载内部配置数据，无需片外加载"}], "prod_name": "成都华微", "HotNum": 612}, {"StockID": "002654", "Tag": [{"ID": "2247", "Name": "DRAM存储", "Reason": "2023年报称新增内存（DRAM）业务"}], "prod_name": "万润科技", "HotNum": 2458}, {"StockID": "688627", "Tag": [{"ID": "2247", "Name": "DRAM存储", "Reason": "公司的半导体存储器件测试设备主要用于在 DRAM 等半导体存储器件的晶圆制造环节对晶圆裸片进行电参数性能和功能测试"}], "prod_name": "精智达  ", "HotNum": 627}, {"StockID": "600171", "Tag": [{"ID": "2251", "Name": "EEPROM", "Reason": "公司EEPROM产品系列已经基本齐全，实现了容量从2kbit到2048kbit，各种封装形式的全覆盖"}], "prod_name": "上海贝岭", "HotNum": 1977}, {"StockID": "300650", "Tag": [{"ID": "2247", "Name": "DRAM存储", "Reason": "根据子公司博思达科技(中国香港)有限公司官网：公司有存储芯片解决方案，产品有存储卡 | MicroSD / SD SDA授权、固态硬盘 | SSD、嵌入式存储 | eMMC、内存产品 | DRAM / DRAM Module / LPDDR等。"}], "prod_name": "太龙股份", "HotNum": 10272}], "Power": 0, "Subscribe": 0, "ZT": {"300650": ["1", "20.01", "1751007780"]}, "IsGood": 0, "GoodNum": 278, "ComNum": 694, "errcode": "0", "t": 0.005998000000000003}