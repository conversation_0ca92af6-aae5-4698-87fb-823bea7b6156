{"ID": "197", "Name": "空中成像", "BriefIntro": "空中成像技术是一种先进的显示技术，它能够在没有任何物理介质的情况下，在空气中直接形成三维立体影像。这项技术的核心在于利用特殊的光学原理和高精度的控制技术，将图像信息直接传递到空气中，用户可以看到并与之交互的3D图像，华为是空中成像技术的重要推动者之一。", "ClassLayer": "1", "Desc": "", "PlateSwitch": "2", "StkSwitch": "2", "Introduction": "<p><strong>题材相关新闻：</strong></p><p>名为DUSt3R的新工具，才上线没多久就登上GitHub热榜第二，该工具可2秒钟2张图实现3D重建。</p><p><strong>题材介绍：</strong></p><p>一、什么是空中成像</p><p><span style=\"color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; font-size: 14px; letter-spacing: 0.25px; white-space: pre-wrap; background-color: rgb(255, 255, 255);\">空中成像技术是一种先进的显示技术，能够在没有物理屏幕或其他介质的情况下，在空气中生成三维立体影像。这项技术基于微纳结构光场调控的基本原理，通过无源光波导阵列器件（如负折射平板透镜）精准控制光线的入射、折射和反射，将发散的光线在空中重新汇聚，形成无需介质承载的实像。空中成像技术不仅实现了虚拟三维模型或场景的悬浮展示，还具备无实物接触的直接交互能力，极大地提升了用户体验。</span></p><p>二、空中成像的前景</p><p><span style=\"color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; font-size: 14px; letter-spacing: 0.25px; white-space: pre-wrap; background-color: rgb(255, 255, 255);\">空中成像技术的应用前景非常广阔，可以用于教育、娱乐、艺术、医疗、军事等领域，为各个行业带来巨大的创新和变革。例如，在教育领域，老师可以通过空中成像技术，在空中展示教学内容，让学生更加直观地学习；在医疗领域，医生可以使用空中成像技术，为患者展示病历和影像资料，提高沟通效率。此外，空中成像技术还可以应用于家庭娱乐、商场广告、公共交通等信息展示场景，为人们带来更加便捷、高效的信息服务。</span></p><p>三、空中成像的发展</p><p style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0px; margin-bottom: 0.859em; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\">华为是空中成像技术领域的领先企业之一，已经开发出多种空中成像技术，并取得了一些进展。华为的空中成像技术利用先进的激光和光学处理技术，将图像信息直接传递到空气中，用户可以看到在空中形成的3D图像，为各类应用场景提供无限可能。此外，苹果、三星和谷歌等科技巨头也在积极研发空中成像技术，展示了这项技术的全球关注度和发展潜力。</p><p style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0px; margin-bottom: 0.859em; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\">空中成像技术的核心在于微通道矩阵光波导平板，这种光学微镜结构能够记录来自实物光源的光线信息，并在另一侧复制出镜像的光线，通过再聚焦过程在空中形成实像。这项技术的关键在于它能够在空气中形成可交互的真实影像，结合交互算法及用户体验场景设计，最终形成无介质全息空中成像技术。</p><p class=\"last-node\" style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0px; margin-bottom: 0px; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\">随着技术的不断发展，空中成像技术有望在未来发挥更加重要的作用，为人们带来更加丰富多彩的生活体验。同时，随着万物互联时代的到来，显示技术将变得更加无处不在，空中成像技术具有广泛的应用前景。</p><p><strong><br/></strong><br/></p>", "CreateTime": "1709536486", "UpdateTime": "0", "Table": [], "Stocks": [{"StockID": "002189", "IsZz": "2", "IsHot": "0", "Reason": "互动易称空中成像技术是公司布局未来产业发展新赛道的重点方向，公司将结合计算光学成像与AI人工智能研究开发新一代空中成像设备，为下一代新型显示技术培育发展新优势，目前相关技术与产品处于研发阶段。", "prod_name": "中光学", "Hot": 23013}, {"StockID": "603726", "IsZz": "2", "IsHot": "0", "Reason": "间接持股4.11%及至微公司主要核心技术应用于车用 PGU 高亮度激光投影模块、AnyBeam 激光微型投影仪以及微型激光投影模块在穿戴式 AR 眼镜与视网膜扫描成像技术，目前主要产品仍处于研发、市场应用验证阶段。", "prod_name": "XD朗迪集", "Hot": 2648}, {"StockID": "300947", "IsZz": "2", "IsHot": "0", "Reason": "全资子公司上海智链合创在智慧园区系统的前沿探索中，正不断实践和运用空间计算技术，以提高园区智慧化程度。", "prod_name": "德必集团", "Hot": 2560}, {"StockID": "001314", "IsZz": "2", "IsHot": "0", "Reason": "子公司亿境虚拟致力于将三维显示及空间计算技术和穿戴计算技术等前沿科技。", "prod_name": "亿道信息", "Hot": 2352}, {"StockID": "000810", "IsZz": "2", "IsHot": "0", "Reason": "公司的pancake系列VR/MR产品能实现投射一个巨大的电影屏幕，可播放三维视频，也可以将屏幕固定在需要的虚拟空间追踪，能实现一定的三维空间计算交互能力。", "prod_name": "创维数字", "Hot": 2156}, {"StockID": "605178", "IsZz": "2", "IsHot": "0", "Reason": "公司结合“全量数据+AI计算”领域的研发成果，在城市数字化进程中融入空间计算技术核心成果，实现多种场景下城市智慧化管理，构筑城市的未来数字平台。", "prod_name": "时空科技", "Hot": 1833}, {"StockID": "300081", "IsZz": "2", "IsHot": "0", "Reason": "在算法的积累基础上，公司研发了诸如：人体动作捕捉、SLAM空间计算、人脸识别、车辆识别、镜头姿态计算等 AI 应用的能力模块。", "prod_name": "恒信东方", "Hot": 1623}, {"StockID": "605218", "IsZz": "2", "IsHot": "0", "Reason": "公司参股的东超科技（持股4.11%）一直专注于空中成像技术和材料的研发，自主研发的DCT-Plate镜片能将画面直接呈现在空气中，并可通过交互技术与画面直接交互，过程中不需要任何承载介质。", "prod_name": "伟时电子", "Hot": 1554}, {"StockID": "300578", "IsZz": "2", "IsHot": "0", "Reason": "公司通过投资布局了面向工业4.0和智能制造核心场景的3D引擎，构建了实时建模，实时渲染，实时通信等场景能力。", "prod_name": "会畅通讯", "Hot": 1302}, {"StockID": "300989", "IsZz": "2", "IsHot": "0", "Reason": "公司建设CIM基础平台，实现对城市中多源、异构的二三维空间数据进行统一管理，并提供对空间数据的空间计算能力和可视化能力。", "prod_name": "蕾奥规划", "Hot": 836}, {"StockID": "300134", "IsZz": "2", "IsHot": "0", "Reason": "子公司大富网络自主原创了NPL神经元并行计算机语言、ParaEngine分布式3D引擎等，通过“体素”建模的形式创造三维空间的世界，实现了由AI随机创建生成内容的机能。", "prod_name": "大富科技", "Hot": 734}, {"StockID": "688039", "IsZz": "2", "IsHot": "0", "Reason": "棱镜全息（公司持股11.11%）在全息科技应用、科技创新研发等方面保持业界领先地位，致力于通过脱离介质的依赖，实现全息 3D 立体的空中成像，实现无介质、任意物体全方位立体影像化。", "prod_name": "当虹科技", "Hot": 652}, {"StockID": "688322", "IsZz": "2", "IsHot": "0", "Reason": "公司基于不同应用场景对3D视觉感知技术的不同需求，梯次开展包括结构光、iToF、双目、dToF、Lidar等六种主流3D视觉感知技术路线的研发布局。", "prod_name": "奥比中光", "Hot": 638}, {"StockID": "603466", "IsZz": "2", "IsHot": "0", "Reason": "2023年12月公司互动易称有全息空中成像相关的技术和专利储备", "prod_name": "风语筑  ", "Hot": 575}, {"StockID": "301313", "IsZz": "2", "IsHot": "0", "Reason": "公司以“AI+3D”为技术发展方向、围绕3D可视化技术与数字多媒体集成等核心技加强对数字栾生技术及自研FT-E数字栾生渲染引擎的建设。", "prod_name": "凡拓数创", "Hot": 573}, {"StockID": "300556", "IsZz": "2", "IsHot": "0", "Reason": "unity是数字交互内容的开发引擎，是公司进行创意设计制作的生产工具之一，瑞云旗下的实时云渲染平台3DCAT支持包括unity在内的各种各样的引擎渲染。", "prod_name": "丝路视觉", "Hot": 569}, {"StockID": "300264", "IsZz": "2", "IsHot": "0", "Reason": "空间计算技术是VR、元宇宙等虚拟现实相关应用的基础技术之一，公司已掌握全景采集、空间建模、光学、惯性动捕等相关技术。", "prod_name": "佳创视讯", "Hot": 550}, {"StockID": "300826", "IsZz": "2", "IsHot": "0", "Reason": "公司形成了从时空信息数据采集、多源数据整合、数据处理、数据分析及产品化应用全业务链的核心技术体系。", "prod_name": "测绘股份", "Hot": 510}, {"StockID": "300036", "IsZz": "2", "IsHot": "0", "Reason": "公司应用新一代三维GIS、分布式空间计算和云原生等技术实现对多源数据的集成与管理并能完成CIM+应用的快速搭建的中台产品。", "prod_name": "超图软件", "Hot": 507}, {"StockID": "301390", "IsZz": "2", "IsHot": "0", "Reason": "公司已积累基于3DGIS引擎技术的可视化平台应用、三维建模技术等方面多项核心技术。", "prod_name": "经纬股份", "Hot": 501}, {"StockID": "300365", "IsZz": "2", "IsHot": "0", "Reason": "公司研发了“三维建模引擎（DH3D）”、“三维数字地球平台（DHGlobe）”等技术平台，同时也积累了一系列的“三维设计核心算法”等核心技术。", "prod_name": "恒华科技", "Hot": 461}, {"StockID": "300935", "IsZz": "2", "IsHot": "0", "Reason": "公司已掌握先进的3D图形引擎和实时渲染技术，可以适应各种复杂空间结构以及混合结构体系的建模。", "prod_name": "盈建科", "Hot": 438}], "StockList": [{"StockID": "605178", "Tag": [], "prod_name": "时空科技", "HotNum": 1833}, {"StockID": "001314", "Tag": [], "prod_name": "亿道信息", "HotNum": 2352}, {"StockID": "000810", "Tag": [], "prod_name": "创维数字", "HotNum": 2156}, {"StockID": "300989", "Tag": [], "prod_name": "蕾奥规划", "HotNum": 836}, {"StockID": "300826", "Tag": [], "prod_name": "测绘股份", "HotNum": 510}, {"StockID": "300365", "Tag": [], "prod_name": "恒华科技", "HotNum": 461}, {"StockID": "301313", "Tag": [], "prod_name": "凡拓数创", "HotNum": 573}, {"StockID": "300134", "Tag": [], "prod_name": "大富科技", "HotNum": 734}, {"StockID": "300036", "Tag": [], "prod_name": "超图软件", "HotNum": 507}, {"StockID": "301390", "Tag": [], "prod_name": "经纬股份", "HotNum": 501}, {"StockID": "300947", "Tag": [], "prod_name": "德必集团", "HotNum": 2560}, {"StockID": "300578", "Tag": [], "prod_name": "会畅通讯", "HotNum": 1302}, {"StockID": "300556", "Tag": [], "prod_name": "丝路视觉", "HotNum": 569}, {"StockID": "300081", "Tag": [], "prod_name": "恒信东方", "HotNum": 1623}, {"StockID": "688322", "Tag": [], "prod_name": "奥比中光", "HotNum": 638}, {"StockID": "300264", "Tag": [], "prod_name": "佳创视讯", "HotNum": 550}, {"StockID": "300935", "Tag": [], "prod_name": "盈建科", "HotNum": 438}, {"StockID": "002189", "Tag": [], "prod_name": "中光学", "HotNum": 23013}, {"StockID": "603726", "Tag": [], "prod_name": "XD朗迪集", "HotNum": 2648}, {"StockID": "605218", "Tag": [], "prod_name": "伟时电子", "HotNum": 1554}, {"StockID": "603466", "Tag": [], "prod_name": "风语筑  ", "HotNum": 575}, {"StockID": "688039", "Tag": [], "prod_name": "当虹科技", "HotNum": 652}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 374, "ComNum": 904, "errcode": "0", "t": 0.00566600000000006}