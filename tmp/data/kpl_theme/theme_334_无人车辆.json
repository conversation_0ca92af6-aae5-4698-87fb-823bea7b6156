{"ID": "334", "Name": "无人车辆", "BriefIntro": "无人车，即无人驾驶车辆，是一种通过搭载先进传感器、控制器、执行器等装置，并融合人工智能、计算机视觉、雷达、导航定位、模式识别、机器视觉、智能控制等多种技术，实现自主规划、感知环境、决策行动和执行操作，无需人类驾驶员直接操控的智能车辆。", "ClassLayer": "2", "Desc": "", "PlateSwitch": "1", "StkSwitch": "2", "Introduction": "<p><strong><span style=\"font-family: 宋体, SimSun; font-size: 18px;\">一、题材新闻</span></strong></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"></span></span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">2025年6月23日，据知情人士向界面新闻透露，自动驾驶科技公司文远知行已秘密提交香港上市申请。</span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">2025年6月23日，特斯拉大涨超8%，创4月28日以来最大单日涨幅。特斯拉公司打造的约10辆无人驾驶出租车22日出现在得克萨斯州首府奥斯汀特定区域，向该公司邀请的一些用户提供出行服务，车费统一为每趟4.2美元。这是这家电动汽车企业首次试运行无人驾驶出租车有偿服务。</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">2025年</span><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">6月1日特斯拉Robotaxi即将上线，马斯克表示Robotaxi将开启白天载人晚上送货的商业模式</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">2025年</span><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">5月26日，据《央视财经新闻》报道，8个部门联合发文，表示将推动降低全社会物流成本，推广自动导引车、无人配送车等设施设备。</span></span></span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">2025年2月6日，文远知行发布新一代无人物流车Robovan W5。机构对此表示，对于Robovan，已经能在物流场景商业化落地，在快递二级网点到快递员这个环节降本效果显著。</span></p><p><br/></p><p style=\"text-indent: 2em;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">今年以来，白犀牛、卡尔动力、九识智能等聚焦物流场景的自动驾驶企业陆续完成新一轮融资。其中，白犀牛获得顺丰与鑫源汽车联合投资2亿元，计划在50城部署2000台无人车；九识智能完成超4.3亿美元融资，2024年交付超3000台。此外，近日中邮科技自主研发的无人车投递系统已在浙江、湖北等多地试点投放。</span></p><p style=\"text-indent: 2em;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">招商证券测算，如果以全国快递物流网点数量为基础，无人配送车的市场空间大约为4680亿元；如果以全国小区数量为基础，无人配送车的市场空间大约在5460亿元到7280亿元之间，市场空间巨大。</span></p><p style=\"text-indent: 2em;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">天风证券研报表示，Uber依靠Nuro实现无人配送，成本降低约6-20倍。据其测算，无人配送大规模应用可为美团节约百亿量级的骑手成本。</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><br/></span></p><p><span style=\"font-size: 18px;\"><strong><span style=\"font-family: 宋体, SimSun;\">二、题材介绍</span></strong></span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"></span></p><h3 class=\"header-QFbyWT auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: 28px; font-size: 18px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: 12px !important; padding-bottom: 0px !important; margin-top: 0px !important;\"><span style=\"font-family: 宋体, SimSun;\"><strong><span style=\"font-size: 14px;\">（一）无人物流车是什么？</span></strong></span></h3><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">无人物流车是指基于自动驾驶技术、传感器融合、智能控制等核心技术，无需人工驾驶即可完成货物运输、配送等物流环节的智能装备。其本质是物流自动化与人工智能技术结合的产物，可在仓储、园区、城市道路等场景中实现货物的高效、安全运输，降低人力成本并提升物流效率。</span></p><h3 class=\"header-QFbyWT auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: 28px; margin-top: 28px; font-size: 18px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: 12px !important; padding-bottom: 0px !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">（二）无人物流车的分类</span></h3><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">根据应用场景、技术路线及功能特点，无人物流车主要可分为以下几类：</span></p><h4 class=\"header-QFbyWT auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: 28px; margin-top: 24px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: 8px !important; padding-bottom: 0px !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">一、按应用场景分类</span></h4><table width=\"NaN\"><thead style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); overflow-anchor: auto;\"><tr style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); border-top: 0px; overflow-anchor: auto; background-color: rgb(242, 242, 242);\" class=\"firstRow\"><th style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-top: 0px; border-right-color: rgba(0, 0, 0, 0.08); border-bottom: 0px; border-left: 0px; max-width: 448px; overflow-anchor: auto;\"><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px;\">类型</span></th><th style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-top: 0px; border-right-color: rgba(0, 0, 0, 0.08); border-bottom: 0px; border-left: 0px; max-width: 448px; overflow-anchor: auto;\"><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px;\">特点</span></th><th style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-width: 0px; border-style: initial; border-color: initial; max-width: 448px; overflow-anchor: auto;\"><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px;\">典型应用场景</span></th></tr></thead><tbody style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); overflow-anchor: auto;\"><tr style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); border-top: 1px solid rgba(0, 0, 0, 0.08); overflow-anchor: auto;\"><td style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-top: 0px; border-right-color: rgba(0, 0, 0, 0.08); border-bottom: 0px; border-left: 0px; max-width: 448px; overflow-anchor: auto; color: rgba(0, 0, 0, 0.85) !important;\"><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">仓储物流车</span></td><td style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-top: 0px; border-right-color: rgba(0, 0, 0, 0.08); border-bottom: 0px; border-left: 0px; max-width: 448px; overflow-anchor: auto; color: rgba(0, 0, 0, 0.85) !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">多在封闭或半封闭的仓库、工厂内部运行，路线固定，环境可控，速度较低。</span></td><td style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-width: 0px; border-style: initial; border-color: initial; max-width: 448px; overflow-anchor: auto; color: rgba(0, 0, 0, 0.85) !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">仓库内货物搬运、货架补货、分拣中心货物转运。</span></td></tr><tr style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); border-top: 1px solid rgba(0, 0, 0, 0.08); overflow-anchor: auto;\"><td style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-top: 0px; border-right-color: rgba(0, 0, 0, 0.08); border-bottom: 0px; border-left: 0px; max-width: 448px; overflow-anchor: auto; color: rgba(0, 0, 0, 0.85) !important;\"><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">园区物流车</span></td><td style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-top: 0px; border-right-color: rgba(0, 0, 0, 0.08); border-bottom: 0px; border-left: 0px; max-width: 448px; overflow-anchor: auto; color: rgba(0, 0, 0, 0.85) !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">在工业园区、校园、机场等相对封闭的区域内运行，路线较固定，需应对少量行人或车辆。</span></td><td style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-width: 0px; border-style: initial; border-color: initial; max-width: 448px; overflow-anchor: auto; color: rgba(0, 0, 0, 0.85) !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">园区内快递配送、物料运输、机场行李搬运。</span></td></tr><tr style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); border-top: 1px solid rgba(0, 0, 0, 0.08); overflow-anchor: auto;\"><td style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-top: 0px; border-right-color: rgba(0, 0, 0, 0.08); border-bottom: 0px; border-left: 0px; max-width: 448px; overflow-anchor: auto; color: rgba(0, 0, 0, 0.85) !important;\"><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">城市配送车</span></td><td style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-top: 0px; border-right-color: rgba(0, 0, 0, 0.08); border-bottom: 0px; border-left: 0px; max-width: 448px; overflow-anchor: auto; color: rgba(0, 0, 0, 0.85) !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">在开放道路上运行，需适应复杂交通环境（如红绿灯、行人、其他车辆等），具备更高的自动驾驶等级。</span></td><td style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-width: 0px; border-style: initial; border-color: initial; max-width: 448px; overflow-anchor: auto; color: rgba(0, 0, 0, 0.85) !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">城市末端快递配送、餐饮外卖配送、商超即时配送。</span></td></tr><tr style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); border-top: 1px solid rgba(0, 0, 0, 0.08); overflow-anchor: auto;\"><td style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-top: 0px; border-right-color: rgba(0, 0, 0, 0.08); border-bottom: 0px; border-left: 0px; max-width: 448px; overflow-anchor: auto; color: rgba(0, 0, 0, 0.85) !important;\"><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">港口 / 码头物流车</span></td><td style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-top: 0px; border-right-color: rgba(0, 0, 0, 0.08); border-bottom: 0px; border-left: 0px; max-width: 448px; overflow-anchor: auto; color: rgba(0, 0, 0, 0.85) !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">针对港口、码头等场景设计，承载量大，适应重载、高频次运输需求，多配合集装箱作业。</span></td><td style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 12px 18px; line-height: 28px; border-width: 0px; border-style: initial; border-color: initial; max-width: 448px; overflow-anchor: auto; color: rgba(0, 0, 0, 0.85) !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">港口集装箱转运、码头货物装卸运输。</span></td></tr></tbody></table><h4 class=\"header-QFbyWT auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: 28px; margin-top: 24px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: 8px !important; padding-bottom: 0px !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">二、按技术路线与驱动方式分类</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: 8px !important; margin-top: 8px !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">AGV（自动导引车）</span></p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">特点</span>：通过预设路径（如磁条、二维码、激光导航等）行驶，自动化程度较低，多在封闭场景使用。</span></p></li><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">应用</span>：仓储内货物搬运、生产线物料配送。</span></p></li></ul><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">AMR（自主移动机器人）</span></p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">特点</span>：无需预设路径，通过传感器（如激光雷达、视觉相机）自主感知环境并规划路线，灵活性更高。</span></p></li><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">应用</span>：仓储分拣、园区内动态配送。</span></p></li></ul><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">L4 级自动驾驶物流车</span></p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">特点</span>：具备高度自动驾驶能力，可在开放道路上独立应对复杂路况，无需人工干预。</span></p></li><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">应用</span>：城市末端配送、跨区域干线运输（部分场景）。</span></p></li></ul></ul><h4 class=\"header-QFbyWT auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: 28px; margin-top: 24px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: 8px !important; padding-bottom: 0px !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">三、按装载能力与体型分类</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: 8px !important; margin-top: 8px !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">小型配送车</span></p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">特点</span>：体型小巧（如箱式机器人），载重通常在 50kg 以下，适合短距离、小批量货物配送。</span></p></li><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">应用</span>：校园快递、写字楼外卖配送。</span></p></li></ul><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">中型物流车</span></p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">特点</span>：载重约 50-500kg，车身尺寸适中，可在园区或城市道路中运输中等批量货物。</span></p></li><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">应用</span>：商超货物配送、工业园区物料运输。</span></p></li></ul><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">大型物流车</span></p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">特点</span>：载重可达吨级，多为卡车或集装箱车形态，适合长距离、大批量货物运输。</span></p></li><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">应用</span>：港口集装箱运输、干线物流配送。</span></p></li></ul></ul><h4 class=\"header-QFbyWT auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: 28px; margin-top: 24px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: 8px !important; padding-bottom: 0px !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">四、按能源类型分类</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: 8px !important; margin-top: 8px !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">电动无人物流车</span></p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">特点</span>：以锂电池为动力源，环保、噪音低，是目前市场主流类型。</span></p></li><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">应用</span>：各类场景的短途配送。</span></p></li></ul><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">氢能无人物流车</span></p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">特点</span>：以氢燃料电池为动力，续航长、加注时间短，适合中长途或重载场景。</span></p></li><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">应用</span>：干线物流、港口重载运输（试点阶段）。</span></p></li></ul></ul><h4 class=\"header-QFbyWT auto-hide-last-sibling-br\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); line-height: 28px; margin-top: 24px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: 8px !important; padding-bottom: 0px !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">五、按功能属性分类</span></h4><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding-left: 20px; overflow-anchor: auto; font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;SF Pro SC&quot;, &quot;SF Pro Display&quot;, &quot;SF Pro Icons&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; background-color: rgb(255, 255, 255); margin-bottom: 8px !important; margin-top: 8px !important;\"><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">分拣型物流车</span></p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">特点</span>：集成分拣系统，可在运输过程中完成货物分类，提升仓储效率。</span></p></li><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">应用</span>：快递分拣中心。</span></p></li></ul><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">运输型物流车</span></p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">特点</span>：专注于货物从 A 点到 B 点的运输，不涉及分拣等额外功能。</span></p></li><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">应用</span>：末端配送、干线运输。</span></p></li></ul><li><p><span style=\"-webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; font-family: 宋体, SimSun; font-size: 14px; color: rgb(0, 0, 0) !important;\">综合服务型物流车</span></p></li><ul class=\"auto-hide-last-sibling-br list-paddingleft-2\" style=\"list-style-type: square;\"><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">特点</span>：具备多种功能（如运输、充电、数据采集等），可与其他设备联动。</span></p></li><li><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun; -webkit-font-smoothing: antialiased; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-weight: 600; line-height: 28px; overflow-anchor: auto; color: rgb(0, 0, 0) !important;\">应用</span>：智慧园区综合物流服务。</span></p></li></ul></ul><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><br/><br/></span></p>", "CreateTime": "1748402341", "UpdateTime": "1750752749", "Table": [{"Level1": {"ID": "3863", "Name": "无人物流车", "ZSCode": "801979", "FirstShelveTime": "1748398320", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "603390", "IsZz": "2", "IsHot": "0", "Reason": "公司与厦门金龙在合作开发无人驾驶物流车项目还待开展试运行等测试", "FirstShelveTime": "1748401340", "UpdateCacheTime": "1748401340", "prod_name": "通达电气", "Hot": 7393}, {"StockID": "603056", "IsZz": "2", "IsHot": "0", "Reason": "无人物流车在最后一公里无人化快递派送的场景下应用，已实现的应用场景包括校园、住宅区、大型厂区、工业园区和总部园区试运营", "FirstShelveTime": "1748402050", "UpdateCacheTime": "1748402050", "prod_name": "德邦股份", "Hot": 6082}, {"StockID": "000903", "IsZz": "2", "IsHot": "0", "Reason": "公司正在与中国邮政集团有限公司云南省分公司等积极开展无人配送车道路的测试业务", "FirstShelveTime": "1748401222", "UpdateCacheTime": "1748401222", "prod_name": "云内动力", "Hot": 4127}, {"StockID": "600476", "IsZz": "2", "IsHot": "0", "Reason": "公司为中国邮政提供技术支撑服务，目前无人快递方面公司仅参与设备试用", "FirstShelveTime": "1748401842", "UpdateCacheTime": "1748401858", "prod_name": "湘邮科技", "Hot": 2892}, {"StockID": "688648", "IsZz": "2", "IsHot": "0", "Reason": "公司研发的无人配送车运营调度管理平台主要用于邮政快递行业的末端投递、接驳、甩点直投等场景", "FirstShelveTime": "1748401076", "UpdateCacheTime": "1748401076", "prod_name": "中邮科技", "Hot": 1770}, {"StockID": "300771", "IsZz": "2", "IsHot": "0", "Reason": "公司拥有自己研发的无人送货车和无人机接收柜", "FirstShelveTime": "1748401571", "UpdateCacheTime": "1748485009", "prod_name": "智莱科技", "Hot": 1169}, {"StockID": "600817", "IsZz": "2", "IsHot": "0", "Reason": "公司与供应商联合开发无人驾驶环卫车辆，共同推进无人驾驶商业化落地运营", "FirstShelveTime": "1748401769", "UpdateCacheTime": "1748401769", "prod_name": "宇通重工", "Hot": 1149}, {"StockID": "301070", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月12日，公司与河南省汽车产业投资集团签署《合作协议》，双方拟围绕无人驾驶技术在无人出行车、无人物流车、无人配送车、无人巡检车等相关领域进行技术研发和成果转化", "FirstShelveTime": "1748427816", "UpdateCacheTime": "1748427816", "prod_name": "开勒股份", "Hot": 1075}, {"StockID": "688455", "IsZz": "2", "IsHot": "0", "Reason": "智能物流领域，公司积极获取包括顺丰、京东等在内的下游主要客户相关订单", "FirstShelveTime": "1748401927", "UpdateCacheTime": "1748401927", "prod_name": "科捷智能", "Hot": 717}]}, "Level2": []}, {"Level1": {"ID": "3864", "Name": "无人出租车", "ZSCode": "0", "FirstShelveTime": "1748398320", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "600650", "IsZz": "2", "IsHot": "0", "Reason": "锦江出租与相关合作方在上海市嘉定区、 临港新城等指定区域内继续开展智能网联无人出租车示范运营活动", "FirstShelveTime": "1748400069", "UpdateCacheTime": "1748400069", "prod_name": "锦江在线", "Hot": 5530}, {"StockID": "600686", "IsZz": "2", "IsHot": "0", "Reason": "2022年年报：实现了福建省首条 L4 级别自动驾驶开放道路公交线的落地运营", "FirstShelveTime": "1748400851", "UpdateCacheTime": "1748400851", "prod_name": "金龙汽车", "Hot": 2320}, {"StockID": "601238", "IsZz": "2", "IsHot": "0", "Reason": "未来，公司团将搭建 Robo AirTaxi 端到端低空立体出行体系，为用户带来更便捷高效的智慧出行方式", "FirstShelveTime": "1748400413", "UpdateCacheTime": "1748400511", "prod_name": "XD广汽集", "Hot": 651}]}, "Level2": []}, {"Level1": {"ID": "3865", "Name": "无人环卫车", "ZSCode": "0", "FirstShelveTime": "1748398320", "UpdateCacheTime": "1750752749", "IsNew": 0, "Stocks": [{"StockID": "603686", "IsZz": "2", "IsHot": "0", "Reason": "公司打造智慧环卫系统平台，包括：环卫大数据可视化系统、环卫作业监管系统、环卫车辆监管系统、环卫事件调度监管考核系统、垃圾智慧收运系统等系统软件", "FirstShelveTime": "1748401125", "UpdateCacheTime": "1748401125", "prod_name": "福龙马  ", "Hot": 3848}, {"StockID": "000826", "IsZz": "2", "IsHot": "0", "Reason": "公司所属启迪数字环卫集团无人环卫清扫车示范和应用项目的车辆采用5种23个以上的传感器，360度覆盖车身周围", "FirstShelveTime": "1748592516", "UpdateCacheTime": "1748592516", "prod_name": "启迪环境", "Hot": 3248}, {"StockID": "300815", "IsZz": "2", "IsHot": "0", "Reason": "公司有无人驾驶环卫车，无人驾驶智能清扫车“田田”是舜泰汽车有限公司结合我司需求所进行自主研发生产的一款智能道路清扫车", "FirstShelveTime": "1748508489", "UpdateCacheTime": "1748508489", "prod_name": "玉禾田", "Hot": 3087}, {"StockID": "001230", "IsZz": "2", "IsHot": "0", "Reason": "全资子公司劲威科技拥有劲威智慧环卫管理云平台V1.0、人工智能环卫作业管理软件V1.0等在内的多项软件著作权和专利", "FirstShelveTime": "1748401193", "UpdateCacheTime": "1748401193", "prod_name": "劲旅环境", "Hot": 2552}, {"StockID": "002973", "IsZz": "2", "IsHot": "0", "Reason": "2024年，公司开发的无人驾驶城市服务智能装备陆续下线包括开放路面及园区无人驾驶清扫装备，市政巡逻及配送机器人等", "FirstShelveTime": "1748571348", "UpdateCacheTime": "1748571348", "prod_name": "侨银股份", "Hot": 2091}, {"StockID": "600006", "IsZz": "2", "IsHot": "0", "Reason": "公司无人驾驶产品主要聚焦于物流和环卫车型，公司无人驾驶车辆已在襄阳市投入示范应用", "FirstShelveTime": "1748503814", "UpdateCacheTime": "1748503814", "prod_name": "东风股份", "Hot": 1632}, {"StockID": "000967", "IsZz": "2", "IsHot": "0", "Reason": "公司在智慧城市环卫服务中，有智云平台、智能装备（含作业舱）、清洁机器人、无人驾驶等环卫装备，部分时段新能源环卫车辆市占率近 30%。", "FirstShelveTime": "1748401308", "UpdateCacheTime": "1748401308", "prod_name": "盈峰环境", "Hot": 1377}, {"StockID": "300746", "IsZz": "2", "IsHot": "0", "Reason": "伏泰科技和库萨科技生产的无人驾驶环卫机器人在国内处于行业领先水平", "FirstShelveTime": "1750752749", "UpdateCacheTime": "1748946646", "prod_name": "汉嘉设计", "Hot": 1165}, {"StockID": "600817", "IsZz": "2", "IsHot": "0", "Reason": "公司建立宇通智慧环卫云平台，发布了道路保洁精细化作业监控技术方案和作业自动规划最优收运路线技术方案，配套车辆自动规划收运路线", "FirstShelveTime": "1748401264", "UpdateCacheTime": "1748401883", "prod_name": "宇通重工", "Hot": 1149}, {"StockID": "300422", "IsZz": "2", "IsHot": "0", "Reason": "公司陆续研发了数十款产品，涵盖不同吨位的道路清扫、清洗、垃圾收转运及小型智能环卫装备。", "FirstShelveTime": "1748508514", "UpdateCacheTime": "1748508514", "prod_name": "博世科", "Hot": 995}]}, "Level2": []}, {"Level1": {"ID": "3871", "Name": "其他", "ZSCode": "0", "FirstShelveTime": "1748514273", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "002383", "IsZz": "2", "IsHot": "0", "Reason": "公司与国内多个头部农业厂商联合实施无人驾驶拖拉机、无人驾驶插秧机等示范项目", "FirstShelveTime": "1748514461", "UpdateCacheTime": "1748514461", "prod_name": "合众思壮", "Hot": 4101}, {"StockID": "600678", "IsZz": "2", "IsHot": "0", "Reason": "2024年8月6日互动易：控股子公司四川开物信息的无人驾驶矿卡在公司自有矿山开展测试", "FirstShelveTime": "1748572859", "UpdateCacheTime": "1748572859", "prod_name": "四川金顶", "Hot": 3901}]}, "Level2": []}], "Stocks": [], "StockList": [{"StockID": "600650", "Tag": [{"ID": "3864", "Name": "无人出租车", "Reason": "锦江出租与相关合作方在上海市嘉定区、 临港新城等指定区域内继续开展智能网联无人出租车示范运营活动"}], "prod_name": "锦江在线", "HotNum": 5530}, {"StockID": "601238", "Tag": [{"ID": "3864", "Name": "无人出租车", "Reason": "未来，公司团将搭建 Robo AirTaxi 端到端低空立体出行体系，为用户带来更便捷高效的智慧出行方式"}], "prod_name": "XD广汽集", "HotNum": 651}, {"StockID": "600686", "Tag": [{"ID": "3864", "Name": "无人出租车", "Reason": "2022年年报：实现了福建省首条 L4 级别自动驾驶开放道路公交线的落地运营"}], "prod_name": "金龙汽车", "HotNum": 2320}, {"StockID": "688648", "Tag": [{"ID": "3863", "Name": "无人物流车", "Reason": "公司研发的无人配送车运营调度管理平台主要用于邮政快递行业的末端投递、接驳、甩点直投等场景"}], "prod_name": "中邮科技", "HotNum": 1770}, {"StockID": "603686", "Tag": [{"ID": "3865", "Name": "无人环卫车", "Reason": "公司打造智慧环卫系统平台，包括：环卫大数据可视化系统、环卫作业监管系统、环卫车辆监管系统、环卫事件调度监管考核系统、垃圾智慧收运系统等系统软件"}], "prod_name": "福龙马  ", "HotNum": 3848}, {"StockID": "001230", "Tag": [{"ID": "3865", "Name": "无人环卫车", "Reason": "全资子公司劲威科技拥有劲威智慧环卫管理云平台V1.0、人工智能环卫作业管理软件V1.0等在内的多项软件著作权和专利"}], "prod_name": "劲旅环境", "HotNum": 2552}, {"StockID": "000903", "Tag": [{"ID": "3863", "Name": "无人物流车", "Reason": "公司正在与中国邮政集团有限公司云南省分公司等积极开展无人配送车道路的测试业务"}], "prod_name": "云内动力", "HotNum": 4127}, {"StockID": "600817", "Tag": [{"ID": "3865", "Name": "无人环卫车", "Reason": "公司建立宇通智慧环卫云平台，发布了道路保洁精细化作业监控技术方案和作业自动规划最优收运路线技术方案，配套车辆自动规划收运路线"}, {"ID": "3863", "Name": "无人物流车", "Reason": "公司与供应商联合开发无人驾驶环卫车辆，共同推进无人驾驶商业化落地运营"}], "prod_name": "宇通重工", "HotNum": 1149}, {"StockID": "000967", "Tag": [{"ID": "3865", "Name": "无人环卫车", "Reason": "公司在智慧城市环卫服务中，有智云平台、智能装备（含作业舱）、清洁机器人、无人驾驶等环卫装备，部分时段新能源环卫车辆市占率近 30%。"}], "prod_name": "盈峰环境", "HotNum": 1377}, {"StockID": "603390", "Tag": [{"ID": "3863", "Name": "无人物流车", "Reason": "公司与厦门金龙在合作开发无人驾驶物流车项目还待开展试运行等测试"}], "prod_name": "通达电气", "HotNum": 7393}, {"StockID": "300771", "Tag": [{"ID": "3863", "Name": "无人物流车", "Reason": "公司拥有自己研发的无人送货车和无人机接收柜"}], "prod_name": "智莱科技", "HotNum": 1169}, {"StockID": "600476", "Tag": [{"ID": "3863", "Name": "无人物流车", "Reason": "公司为中国邮政提供技术支撑服务，目前无人快递方面公司仅参与设备试用"}], "prod_name": "湘邮科技", "HotNum": 2892}, {"StockID": "688455", "Tag": [{"ID": "3863", "Name": "无人物流车", "Reason": "智能物流领域，公司积极获取包括顺丰、京东等在内的下游主要客户相关订单"}], "prod_name": "科捷智能", "HotNum": 717}, {"StockID": "603056", "Tag": [{"ID": "3863", "Name": "无人物流车", "Reason": "无人物流车在最后一公里无人化快递派送的场景下应用，已实现的应用场景包括校园、住宅区、大型厂区、工业园区和总部园区试运营"}], "prod_name": "德邦股份", "HotNum": 6082}, {"StockID": "301070", "Tag": [{"ID": "3863", "Name": "无人物流车", "Reason": "2025年2月12日，公司与河南省汽车产业投资集团签署《合作协议》，双方拟围绕无人驾驶技术在无人出行车、无人物流车、无人配送车、无人巡检车等相关领域进行技术研发和成果转化"}], "prod_name": "开勒股份", "HotNum": 1075}, {"StockID": "600006", "Tag": [{"ID": "3865", "Name": "无人环卫车", "Reason": "公司无人驾驶产品主要聚焦于物流和环卫车型，公司无人驾驶车辆已在襄阳市投入示范应用"}], "prod_name": "东风股份", "HotNum": 1632}, {"StockID": "300815", "Tag": [{"ID": "3865", "Name": "无人环卫车", "Reason": "公司有无人驾驶环卫车，无人驾驶智能清扫车“田田”是舜泰汽车有限公司结合我司需求所进行自主研发生产的一款智能道路清扫车"}], "prod_name": "玉禾田", "HotNum": 3087}, {"StockID": "300422", "Tag": [{"ID": "3865", "Name": "无人环卫车", "Reason": "公司陆续研发了数十款产品，涵盖不同吨位的道路清扫、清洗、垃圾收转运及小型智能环卫装备。"}], "prod_name": "博世科", "HotNum": 995}, {"StockID": "002383", "Tag": [{"ID": "3871", "Name": "其他", "Reason": "公司与国内多个头部农业厂商联合实施无人驾驶拖拉机、无人驾驶插秧机等示范项目"}], "prod_name": "合众思壮", "HotNum": 4101}, {"StockID": "002973", "Tag": [{"ID": "3865", "Name": "无人环卫车", "Reason": "2024年，公司开发的无人驾驶城市服务智能装备陆续下线包括开放路面及园区无人驾驶清扫装备，市政巡逻及配送机器人等"}], "prod_name": "侨银股份", "HotNum": 2091}, {"StockID": "600678", "Tag": [{"ID": "3871", "Name": "其他", "Reason": "2024年8月6日互动易：控股子公司四川开物信息的无人驾驶矿卡在公司自有矿山开展测试"}], "prod_name": "四川金顶", "HotNum": 3901}, {"StockID": "000826", "Tag": [{"ID": "3865", "Name": "无人环卫车", "Reason": "公司所属启迪数字环卫集团无人环卫清扫车示范和应用项目的车辆采用5种23个以上的传感器，360度覆盖车身周围"}], "prod_name": "启迪环境", "HotNum": 3248}, {"StockID": "300746", "Tag": [{"ID": "3865", "Name": "无人环卫车", "Reason": "伏泰科技和库萨科技生产的无人驾驶环卫机器人在国内处于行业领先水平"}], "prod_name": "汉嘉设计", "HotNum": 1165}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 781, "ComNum": 950, "errcode": "0", "t": 0.013120999999999994}