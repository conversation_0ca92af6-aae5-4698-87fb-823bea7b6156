{"ID": "320", "Name": "MCP概念", "BriefIntro": "MCP协议问世以后，包括openAI，阿里、腾讯等国内外大厂都积极拥抱，阿里云旗下modelscope也有了mcp广场，内部有超过1400中工具方便使用，降低了AI Agent的开发门槛。", "ClassLayer": "2", "Desc": "", "PlateSwitch": "1", "StkSwitch": "2", "Introduction": "<p><span style=\";font-family:<PERSON>ibri;font-size:14px\">加入MCP标准的科技巨头：</span></p><p><span style=\";font-family:Calibri;font-size:14px\">2024<span style=\"font-family:宋体\">年</span><span style=\"font-family:Calibri\">11</span><span style=\"font-family:宋体\">月</span><span style=\"font-family:Calibri\">25</span><span style=\"font-family:宋体\">日，</span></span><span style=\"font-family: Calibri; font-size: 14px;\">Anthropic</span><span style=\"font-size: 14px; font-family: 宋体;\">率先开源</span><span style=\"font-family: Calibri; font-size: 14px;\">MCP</span><span style=\"font-size: 14px; font-family: 宋体;\">协议，旨在使</span><span style=\"font-family: Calibri; font-size: 14px;\">AI</span><span style=\"font-size: 14px; font-family: 宋体;\">模型（如</span><span style=\"font-family: Calibri; font-size: 14px;\">Claude</span><span style=\"font-size: 14px; font-family: 宋体;\">）更容易与工具和数据源交互。</span></p><p><span style=\"font-family: Calibri; font-size: 14px;\">2025</span><span style=\"font-size: 14px; font-family: 宋体;\">年</span><span style=\"font-family: Calibri; font-size: 14px;\">3</span><span style=\"font-size: 14px; font-family: 宋体;\">月</span><span style=\"font-family: Calibri; font-size: 14px;\">27</span><span style=\"font-size: 14px; font-family: 宋体;\">日，</span><span style=\"font-family: Calibri; font-size: 14px;\">OpenAI&nbsp;</span><span style=\"font-family: Calibri; font-size: 14px;\">CEO Altman</span><span style=\"font-size: 14px; font-family: 宋体;\">宣布对</span><span style=\"font-family: Calibri; font-size: 14px;\">Agents SDK</span><span style=\"font-size: 14px; font-family: 宋体;\">进行重大更新，支持</span><span style=\"font-family: Calibri; font-size: 14px;\">Anthropic</span><span style=\"font-size: 14px; font-family: 宋体;\">推出的</span><span style=\"font-family: Calibri; font-size: 14px;\">MCP</span><span style=\"font-size: 14px; font-family: 宋体;\">服务协议。</span></p><p><span style=\"font-size: 14px; font-family: 宋体;\"></span></p><p><span style=\";font-family:Calibri;font-size:14px\">2025<span style=\"font-family:宋体\">年</span><span style=\"font-family:Calibri\">4</span><span style=\"font-family:宋体\">月</span><span style=\"font-family:Calibri\">10</span><span style=\"font-family:宋体\">日，</span></span><span style=\"font-family: 宋体; font-size: 14px;\">谷歌</span><span style=\"font-family: Calibri; font-size: 14px;\">CEO Demis Hassabis</span><span style=\"font-size: 14px; font-family: 宋体;\">称</span><span style=\"font-family: Calibri; font-size: 14px;\">MCP</span><span style=\"font-size: 14px; font-family: 宋体;\">是优秀的协议，正在成为</span><span style=\"font-family: Calibri; font-size: 14px;\">AI</span><span style=\"font-size: 14px; font-family: 宋体;\">智能体时代的开放标准，并宣布为</span><span style=\"font-family: Calibri; font-size: 14px;\">Gemini</span><span style=\"font-size: 14px; font-family: 宋体;\">模型和</span><span style=\"font-family: Calibri; font-size: 14px;\">SDK</span><span style=\"font-size: 14px; font-family: 宋体;\">提供支持。</span></p><p><span style=\"font-family: Calibri; font-size: 14px;\">2025</span><span style=\"font-size: 14px; font-family: 宋体;\">年</span><span style=\"font-family: Calibri; font-size: 14px;\">4</span><span style=\"font-size: 14px; font-family: 宋体;\">月</span><span style=\"font-family: Calibri; font-size: 14px;\">9</span><span style=\"font-size: 14px; font-family: 宋体;\">日，</span><span style=\"font-family: 宋体; font-size: 14px;\">阿里（阿里云）</span><span style=\"font-size: 14px; font-family: 宋体;\">百炼平台上线业界首个全生命周期</span><span style=\"font-family: Calibri; font-size: 14px;\">MCP</span><span style=\"font-size: 14px; font-family: 宋体;\">服务，用户无需管理资源、开发部署或运维，</span><span style=\"font-family: Calibri; font-size: 14px;\">5</span><span style=\"font-size: 14px; font-family: 宋体;\">分钟即可搭建连接</span><span style=\"font-family: Calibri; font-size: 14px;\">MCP</span><span style=\"font-size: 14px; font-family: 宋体;\">服务的</span><span style=\"font-family: Calibri; font-size: 14px;\">Agent</span><span style=\"font-size: 14px; font-family: 宋体;\">（智能体）。</span><span style=\"font-size: 14px; font-family: 宋体;\">首批上线</span><span style=\"font-family: Calibri; font-size: 14px;\">50+</span><span style=\"font-size: 14px; font-family: 宋体;\">款</span><span style=\"font-family: Calibri; font-size: 14px;\">MCP</span><span style=\"font-size: 14px; font-family: 宋体;\">服务，覆盖高德、无影、</span><span style=\"font-family: Calibri; font-size: 14px;\">Fetch</span><span style=\"font-size: 14px; font-family: 宋体;\">、</span><span style=\"font-family: Calibri; font-size: 14px;\">Notion</span><span style=\"font-size: 14px; font-family: 宋体;\">等阿里及第三方服务，涉及生活信息、浏览器、内容生成等领域。</span></p><p><span style=\"font-family: 宋体; font-size: 14px;\"></span></p><p><span style=\";font-family:Calibri;font-size:14px\">2025<span style=\"font-family:宋体\">年</span><span style=\"font-family:Calibri\">4</span><span style=\"font-family:宋体\">月</span><span style=\"font-family:Calibri\">14</span><span style=\"font-family:宋体\">日，</span></span><span style=\"font-family: 宋体; font-size: 14px;\">腾讯（腾讯云）</span><span style=\"font-family: Calibri; font-size: 14px;\"><span style=\"font-family:宋体\">大模型知识引擎升级支持</span>MCP<span style=\"font-family:宋体\">协议，用户可调用平台精选的</span>MCP<span style=\"font-family:宋体\">插件或插入自定义插件。</span></span><span style=\"font-size: 14px; font-family: 宋体;\">已接入的</span><span style=\"font-family: Calibri; font-size: 14px;\">MCP</span><span style=\"font-size: 14px; font-family: 宋体;\">服务包括腾讯位置服务、</span><span style=\"font-family: Calibri; font-size: 14px;\">EdgeOne Pages</span><span style=\"font-size: 14px; font-family: 宋体;\">、</span><span style=\"font-family: Calibri; font-size: 14px;\">Airbnb</span><span style=\"font-size: 14px; font-family: 宋体;\">、</span><span style=\"font-family: Calibri; font-size: 14px;\">Figma</span><span style=\"font-size: 14px; font-family: 宋体;\">、</span><span style=\"font-family: Calibri; font-size: 14px;\">Fetch</span><span style=\"font-size: 14px; font-family: 宋体;\">、微信读书等，覆盖专业信息获取、网页部署</span><span style=\"font-family: Calibri; font-size: 14px;\">/</span><span style=\"font-size: 14px; font-family: 宋体;\">预览、网页解析等场景。</span></p><p><br/></p><p>1.什么是MCP?</p><p>Anthropic发布的Model Context Protocol是一种开源标准，24年11月推出。标准化地为LLM提供应用场景和数据，简单说就是让LLM和API工具用统一的方式连接起来，省的每次LLM接不同的API/或者API接不同的LLM，都得重写代码。统一标准还方便LLM和API双向通信，把连接做的更丝滑。</p><p>用通俗的话说，MCP协议就像给AI和软件世界造了一种<strong><span data-type=\"text\" style=\"font-weight:bold;\">通用语言</span></strong>或<span data-type=\"text\" style=\"font-weight:bold;\">万能翻译官</span>。AI模型（比如GPT或Claude）相当于一个聪明的大脑，但再聪明的大脑，如果听不懂工具的数据“语言”，也没法直接使用工具完成任务。MCP提供的就是这样一种<span data-type=\"text\" style=\"font-weight:bold;\">标准语言</span>，让AI能听懂并“会说”各种软件工具的指令。通过MCP，AI可以像人一样获取上下文信息（Context），然后按照既定的规则（Protocol）去调用外部系统的功能。</p><p>总之，MCP协议赋予AI与外界沟通的“标准接口”，让AI更容易地利用软件世界的资源为我们服务。</p><p><br/></p><p>2.MCP Server的作用：AI与软件的适配层。</p><p>MPC实现了持续的系统级连接，AI能够自主发现并利用环境信息，而且无需重复开发适配层。</p><p><span data-type=\"text\" style=\"font-weight:bold;\">过去，AI如何使用软件？</span> &nbsp;在没有MCP协议之前，AI想使用某个软件或数据源通常很麻烦。即使是强大的语言模型，比如<span class=\"\">ChatGPT</span>，过去也<span data-type=\"text\" style=\"font-weight:bold;\">无法直接操作电脑上的应用或联网获取私有数据</span>。如果用户希望AI帮忙查找一份本地文件、在日历应用中创建事件，往往需要开发者为每种软件写一个<span data-type=\"text\" style=\"font-weight:bold;\">专门的插件或API集成</span>。也就是说，每对AI-工具组合都像是说不同的语言，开发者充当翻译，每次都得教AI如何使用那个特定工具。这导致开发工作量大、维护繁琐，而且AI对新工具的支持跟不上变化。很多情况下，AI只能退而求其次——要么让用户自己根据AI的输出去操作软件，要么利用有限的内置插件。总体来说，以往AI使用软件是<span data-type=\"text\" style=\"font-weight:bold;\">受限的、零散的</span>，缺少一个通用的方法。<span data-type=\"text\" style=\"font-weight:bold;\">MPC Server如何改变这一现状？</span> &nbsp;MPC Server可以看作是在AI和具体软件之间搭建的<span data-type=\"text\" style=\"font-weight:bold;\">桥梁</span>或“驱动”。根据MCP协议的架构，每一种数据源或工具都有对应的MCP服务器——一个轻量级的服务，负责跟那个工具打交道。AI想访问某项功能时，不再需要直接调用每个软件独有的API，而是通过MCP协议向相应的MCP服务器发出请求。MCP服务器懂得该如何跟后台的数据源交流，并把结果返回给AI。这就好比给各种软件都安装了一个<span data-type=\"text\" style=\"font-weight:bold;\">标准化的适配器</span>：AI只需会用一种通信方式，就能间接操作很多种不同的软件。</p><p>每次清求都需要独立封装工具调用，上下文是静态的，并且每个工具都要编写适配代码。</p><p><br/></p><p>3.MCP协议和AI Agent的关系。</p><p>MCP 具备了 AI Agent 所需要的记忆能力和工具执行能力，这使得 AIAgent 变得更加智能化。</p><p><span data-type=\"text\" style=\"font-weight:bold;\">MPC如何赋能AI Agent？</span> MCP协议的出现，正好为AI Agent插上了“全面开挂”的翅膀。以前AI Agent要用某个新工具，可能需要开发者特别为它<span data-type=\"text\" style=\"font-weight:bold;\">定制技能</span>；但在MCP的支持下，Agent获取新能力就像下载插件一样简单。因为MCP定义了一套统一标准，Agent只要实现了这套“说话方式”，就能跟任何遵循MCP的服务对话。<span data-type=\"text\" style=\"font-weight:bold;\">这极大地扩展了Agent可以触达的领域</span>：从本地文件到云端数据库，从办公软件到专业系统，只要有MCP接口，Agent都能学会使用。可以说，MCP把过去一个个孤立的工具串成了一个丰富的“动作库”，让AI Agent真正具备了<span data-type=\"text\" style=\"font-weight:bold;\">像人一样调用万物的能力。</span></p><p><span data-type=\"text\" style=\"font-weight:bold;\"><br/></span></p>", "CreateTime": "1744856886", "UpdateTime": "0", "Table": [{"Level1": {"ID": "3757", "Name": "MCP生态", "ZSCode": "0", "Stocks": [{"StockID": "002131", "IsZz": "2", "IsHot": "0", "Reason": "公司官微4月18日消息，利欧数字发布广告行业首个MCP（Model Context Protocol）服务，正式宣布旗下开放API服务工具支持MCP协议。", "prod_name": "利欧股份", "Hot": 3983}, {"StockID": "000811", "IsZz": "2", "IsHot": "0", "Reason": "公司持续深化 MCP，从业务主线深入到专业领域的创新领域，围绕各专业体系“提质、降本、增效” 实现数智赋能的升级优化。", "prod_name": "冰轮环境", "Hot": 2139}, {"StockID": "603110", "IsZz": "2", "IsHot": "0", "Reason": "子公司东方超算官微：宣布正式上线“东方六合”MCP服务平台。", "prod_name": "东方材料", "Hot": 1713}, {"StockID": "300687", "IsZz": "2", "IsHot": "0", "Reason": "2025年官微：赛意AI平台（善谋 GPT）作为赛意信息精心打造的为企业效能而生的企业级AI应用开发平台，已全面支持MCP（Model Context Protocol，模型上下文协议）与A2A协议", "prod_name": "赛意信息", "Hot": 1027}, {"StockID": "300605", "IsZz": "2", "IsHot": "0", "Reason": "公司官微表示，公司针对公共安全领域的三大场景使用MCP技术实现了智能化升级。", "prod_name": "恒锋信息", "Hot": 901}, {"StockID": "300017", "IsZz": "2", "IsHot": "0", "Reason": "网宿对象存储全面支持MCP协议打造AI大模型数据底座", "prod_name": "网宿科技", "Hot": 746}, {"StockID": "603713", "IsZz": "2", "IsHot": "0", "Reason": "公司即将推出“密问 2.0”， 融入 Deepseek 深度思考功能； MCP 系统推出“AI 智能入库”“智能波次捡货”“AI 智能调度系统”“AI智能预测系统”；", "prod_name": "密尔克卫", "Hot": 368}]}, "Level2": []}, {"Level1": {"ID": "3758", "Name": "TO B应用", "ZSCode": "0", "Stocks": [{"StockID": "300170", "IsZz": "2", "IsHot": "0", "Reason": "2025年官微：汉得计划在5月底发布灵猿AI中台1.6版，全面支持MCP", "prod_name": "汉得信息", "Hot": 1916}, {"StockID": "688158", "IsZz": "2", "IsHot": "0", "Reason": "公司主要通过子公司上海优铭云为客户提供企业级私有云产品与解决方案，核心产品包括 MCP、专有云服务及容器等", "prod_name": "优刻得  ", "Hot": 1486}, {"StockID": "603171", "IsZz": "2", "IsHot": "0", "Reason": "公司自主研发的财税垂直领域大模型“犀友”大模型获国家网信办算法备案， “犀友”人工智能平台通过提炼和标注可靠的财税知识数据规则，深度整合 MCP 构建“多模型协同+场景化精调”的财税智能化引擎。", "prod_name": "税友股份", "Hot": 1324}, {"StockID": "688258", "IsZz": "2", "IsHot": "0", "Reason": "公司称EazyDevelop产品完全支持MCP调用，其中重点支持代码类的MCP工具，也将在未来上线云服务市场，提供mcp等服务", "prod_name": "卓易信息", "Hot": 528}, {"StockID": "300369", "IsZz": "2", "IsHot": "0", "Reason": "绿盟科技发布MCP专项审计产品，覆盖代码扫描，日志分析，漏洞响应等全流程", "prod_name": "绿盟科技", "Hot": 470}, {"StockID": "688615", "IsZz": "2", "IsHot": "0", "Reason": "合合信息在B端致力于为大家提供标准化的产品；公司作为一家数据和AI企业，在MCP之前公司也有通过API接口为大模型提供文本识别、文档解析等服务", "prod_name": "合合信息", "Hot": 422}, {"StockID": "688058", "IsZz": "2", "IsHot": "0", "Reason": "公司消息中间件的 PaaS 服务MQPaaS 及中间件统一管理平台 MCP 已经在金融领域实现落地应用，通过向下对接统一云平台、向上支撑云管理平台，可提供广泛且高效的运维管理支撑能力", "prod_name": "宝兰德  ", "Hot": 412}]}, "Level2": []}], "Stocks": [], "StockList": [{"StockID": "002131", "Tag": [{"ID": "3757", "Name": "MCP生态", "Reason": "公司官微4月18日消息，利欧数字发布广告行业首个MCP（Model Context Protocol）服务，正式宣布旗下开放API服务工具支持MCP协议。"}], "prod_name": "利欧股份", "HotNum": 3983}, {"StockID": "300369", "Tag": [{"ID": "3758", "Name": "TO B应用", "Reason": "绿盟科技发布MCP专项审计产品，覆盖代码扫描，日志分析，漏洞响应等全流程"}], "prod_name": "绿盟科技", "HotNum": 470}, {"StockID": "300605", "Tag": [{"ID": "3757", "Name": "MCP生态", "Reason": "公司官微表示，公司针对公共安全领域的三大场景使用MCP技术实现了智能化升级。"}], "prod_name": "恒锋信息", "HotNum": 901}, {"StockID": "603110", "Tag": [{"ID": "3757", "Name": "MCP生态", "Reason": "子公司东方超算官微：宣布正式上线“东方六合”MCP服务平台。"}], "prod_name": "东方材料", "HotNum": 1713}, {"StockID": "688158", "Tag": [{"ID": "3758", "Name": "TO B应用", "Reason": "公司主要通过子公司上海优铭云为客户提供企业级私有云产品与解决方案，核心产品包括 MCP、专有云服务及容器等"}], "prod_name": "优刻得  ", "HotNum": 1486}, {"StockID": "688258", "Tag": [{"ID": "3758", "Name": "TO B应用", "Reason": "公司称EazyDevelop产品完全支持MCP调用，其中重点支持代码类的MCP工具，也将在未来上线云服务市场，提供mcp等服务"}], "prod_name": "卓易信息", "HotNum": 528}, {"StockID": "688615", "Tag": [{"ID": "3758", "Name": "TO B应用", "Reason": "合合信息在B端致力于为大家提供标准化的产品；公司作为一家数据和AI企业，在MCP之前公司也有通过API接口为大模型提供文本识别、文档解析等服务"}], "prod_name": "合合信息", "HotNum": 422}, {"StockID": "603171", "Tag": [{"ID": "3758", "Name": "TO B应用", "Reason": "公司自主研发的财税垂直领域大模型“犀友”大模型获国家网信办算法备案， “犀友”人工智能平台通过提炼和标注可靠的财税知识数据规则，深度整合 MCP 构建“多模型协同+场景化精调”的财税智能化引擎。"}], "prod_name": "税友股份", "HotNum": 1324}, {"StockID": "000811", "Tag": [{"ID": "3757", "Name": "MCP生态", "Reason": "公司持续深化 MCP，从业务主线深入到专业领域的创新领域，围绕各专业体系“提质、降本、增效” 实现数智赋能的升级优化。"}], "prod_name": "冰轮环境", "HotNum": 2139}, {"StockID": "603713", "Tag": [{"ID": "3757", "Name": "MCP生态", "Reason": "公司即将推出“密问 2.0”， 融入 Deepseek 深度思考功能； MCP 系统推出“AI 智能入库”“智能波次捡货”“AI 智能调度系统”“AI智能预测系统”；"}], "prod_name": "密尔克卫", "HotNum": 368}, {"StockID": "688058", "Tag": [{"ID": "3758", "Name": "TO B应用", "Reason": "公司消息中间件的 PaaS 服务MQPaaS 及中间件统一管理平台 MCP 已经在金融领域实现落地应用，通过向下对接统一云平台、向上支撑云管理平台，可提供广泛且高效的运维管理支撑能力"}], "prod_name": "宝兰德  ", "HotNum": 412}, {"StockID": "300170", "Tag": [{"ID": "3758", "Name": "TO B应用", "Reason": "2025年官微：汉得计划在5月底发布灵猿AI中台1.6版，全面支持MCP"}], "prod_name": "汉得信息", "HotNum": 1916}, {"StockID": "300687", "Tag": [{"ID": "3757", "Name": "MCP生态", "Reason": "2025年官微：赛意AI平台（善谋 GPT）作为赛意信息精心打造的为企业效能而生的企业级AI应用开发平台，已全面支持MCP（Model Context Protocol，模型上下文协议）与A2A协议"}], "prod_name": "赛意信息", "HotNum": 1027}, {"StockID": "300017", "Tag": [{"ID": "3757", "Name": "MCP生态", "Reason": "网宿对象存储全面支持MCP协议打造AI大模型数据底座"}], "prod_name": "网宿科技", "HotNum": 746}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 231, "ComNum": 470, "errcode": "0", "t": 0.005999000000000004}