{"ID": "247", "Name": "无人驾驶应用", "BriefIntro": "北京市经信局就《北京市自动驾驶汽车条例（征求意见稿）》对外征求意见。北京市拟支持自动驾驶汽车用于城市公共电汽车客运、网约车、汽车租赁等城市出行服务。", "ClassLayer": "2", "Desc": "", "PlateSwitch": "2", "StkSwitch": "2", "Introduction": "<p><span style=\"font-family: 宋体, SimSun; font-size: 18px;\"><strong>题材相关消息</strong></span></p><p><span style=\"text-align: justify; font-family: 宋体, SimSun; font-size: 14px;\"></span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">2025年6月23日，据知情人士向界面新闻透露，自动驾驶科技公司文远知行已秘密提交香港上市申请。</span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">2025年6月23日，特斯拉大涨超8%，创4月28日以来最大单日涨幅。特斯拉公司打造的约10辆无人驾驶出租车22日出现在得克萨斯州首府奥斯汀特定区域，向该公司邀请的一些用户提供出行服务，车费统一为每趟4.2美元。这是这家电动汽车企业首次试运行无人驾驶出租车有偿服务。</span></p><p><br/></p><p><span style=\"font-family: 宋体, SimSun; font-size: 18px;\"><strong>题材相关介绍</strong></span><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><br/></span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">一、应用初始<br/></span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\"><span style=\"font-size: 14px; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;\">武汉市针对</span><span class=\"wx_search_keyword_wrap\" style=\"font-size: 14px; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: var(--weui-LINK); cursor: default; font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;\">Robotaxi<em class=\"wx_search_keyword\" style=\"margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; display: inline-block; vertical-align: super; font-size: 10px; width: 1.2em; height: 1.2em; -webkit-mask-position: 50% 50%; -webkit-mask-repeat: no-repeat; -webkit-mask-size: 100%; background-color: var(--weui-LINK); -webkit-mask-image: url(&quot;data:image/svg+xml,%3csvg width=&#39;12&#39; height=&#39;12&#39; viewBox=&#39;0 0 12 12&#39; fill=&#39;none&#39; xmlns=&#39;http://www.w3.org/2000/svg&#39;%3e%3cpath fill-rule=&#39;evenodd&#39; clip-rule=&#39;evenodd&#39; d=&#39;M7.60772 8.29444C7.02144 8.73734 6.29139 9 5.5 9C3.567 9 2 7.433 2 5.5C2 3.567 3.567 2 5.5 2C7.433 2 9 3.567 9 5.5C9 6.28241 8.74327 7.00486 8.30946 7.5877C8.3183 7.59444 8.3268 7.60186 8.33488 7.60994L10.4331 9.70816L9.726 10.4153L7.62777 8.31704C7.62055 8.30983 7.61387 8.30228 7.60772 8.29444ZM8 5.5C8 6.88071 6.88071 8 5.5 8C4.11929 8 3 6.88071 3 5.5C3 4.11929 4.11929 3 5.5 3C6.88071 3 8 4.11929 8 5.5Z&#39; fill=&#39;%23576B95&#39;/%3e%3c/svg%3e&quot;);\"></em></span><span style=\"font-size: 14px; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;\">最重要的政策文件为2022年6月正式印发的《武汉市智能网联汽车道路测试和示范应用管理实施细则（试行）》，明确Robotaxi企业示范应用、商业化试点以及远程测试（及无安全员）的申请准入条件，是全国首个发布全无人驾驶商业化运营试点政策。</span><br/></span></p><p><span style=\"background-color: rgb(255, 255, 255); color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; letter-spacing: 0.544px; text-align: justify; font-size: 14px;\">二、应用发展<br/></span></p><p><span style=\"background-color: rgb(255, 255, 255); color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; letter-spacing: 0.544px; text-align: justify; font-size: 14px;\">截至2023年底，武汉市已发布九批智能网联测试道路，累计开放测试道路里程达到1845.91公里，分布于全市8个行政和功能区。<br/></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); font-family: 宋体, SimSun; font-size: 14px;\">截至2024年1月，已累计发放自动驾驶道路测试和运营牌照1800张。</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">截至22年底、23年底以及24年6月，武汉市常态化自动驾驶测试及商业化运营车辆分别为130、500、580台。(注:统计口径包括Robotaxi和无人巴士)<br/></span></p><p><span style=\"margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgba(0, 0, 0, 0.9); letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); font-family: 宋体, SimSun; font-size: 14px; box-sizing: border-box !important; overflow-wrap: break-word !important;\"></span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">全年自动驾驶出行服务订单73.2万单，其中2023年12月单月订单12.1万单。以武汉市10km路程为例，萝卜快跑车费4-16元，传统网约车18-30元<br/></span></p><p style=\"margin-top: 0px; margin-bottom: 8px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; line-height: 1.75em; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">三、百度萝卜快跑</span></p><p style=\"margin-top: 0px; margin-bottom: 8px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; line-height: 1.75em; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">1、在武汉市的运营情况:</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">(1)2022年5月，百度萝卜快跑落地武汉光谷，</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">(2)2022年8月，首批投放10辆，其中5辆没有安全员，设立63个推荐上车点，运营时间9:00-17:00</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">(3)2022年12月，累计投放103台，总计完成订单5.2万单。运营时间延长至9:00-23:00，武汉已成为百度继北京外部署车辆最多的城市。</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">(4)2023年9月:累计投放300辆，运营时间6:00-凌晨2:00</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">(5)2024年:预计在武汉投放累积达到千量。</span></p><p style=\"margin-top: 0px; margin-bottom: 8px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; line-height: 1.75em; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">2、发展史<br/></span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">2017年7月，百度宣布Apolo计划，发布开放、完整、安全的自动驾驶开源平台2021年8月，百度发布萝卜快跑，面向大众提供商业运营和多元化增值服务，预计三年内实现30座城市覆盖。</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">2022年，百度先后在深圳、北京、重庆、合肥、上海等地区投入商业化运营;年末，萝卜快跑首批获准在京开展全无人自动驾驶测试。<br/></span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">2023年，根据百度年报披露，萝卜快跑累计订单量超500万单，Q4单季度订单量84万单，同比+49%，武汉地区全无人驾驶订单比例达45%，已在全国11个城市落地自动驾驶出行服务，北上深武开展车内无人示范运营。<br/></span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">2024年5月，百度Apollo Day 2024发布全球首个支持L4级自动驾驶大模型Apoll0 ADFM,以及搭载第六代智能化系统解决方案的萝卜快跑无人车，价格为20万元，整车成本较上一代下降60%。<br/></span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">2024年7月，萝卜快跑武汉市订单量迎来爆发，单日单车峰值超20单，与出租车司机单日订单情况达到同一水平;总订单量已超过600万单，测试里程超1亿公里。</span></p><p style=\"margin-top: 0px; margin-bottom: 8px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; line-height: 1.75em; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><br/></p><p><br/></p>", "CreateTime": "1720597718", "UpdateTime": "0", "Table": [{"Level1": {"ID": "2785", "Name": "网约车", "ZSCode": "0", "FirstShelveTime": "1720574655", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "600650", "IsZz": "2", "IsHot": "0", "Reason": "锦江出租组织日常运营管理，正式在嘉定区1076公里开放测试道路范围内推出准商业化Robotaxi服务，由自动驾驶车辆组成的“智能网联出租车队”为上海市民提供出行服务。", "FirstShelveTime": "1720574736", "UpdateCacheTime": "1720574736", "prod_name": "锦江在线", "Hot": 5530}, {"StockID": "603776", "IsZz": "2", "IsHot": "0", "Reason": "公司于2019年5月8日收到常州市交通运输局颁发的《网络预约出租汽车经营许可证》，可合法开展相关网约车的经营活动", "FirstShelveTime": "1720576825", "UpdateCacheTime": "1720576825", "prod_name": "永安行  ", "Hot": 5435}, {"StockID": "600611", "IsZz": "2", "IsHot": "0", "Reason": "大众出行平台成功获得上海首家网约车平台资质", "FirstShelveTime": "1720574736", "UpdateCacheTime": "1720574736", "prod_name": "大众交通", "Hot": 3908}, {"StockID": "000421", "IsZz": "2", "IsHot": "0", "Reason": "公司已开展网约车业务。", "FirstShelveTime": "1720574950", "UpdateCacheTime": "1720574950", "prod_name": "南京公用", "Hot": 1283}, {"StockID": "002357", "IsZz": "2", "IsHot": "0", "Reason": "富临运业公告称将提供1500台车辆，在成都地区接入T3出行平台，提供运力。", "FirstShelveTime": "1722217522", "UpdateCacheTime": "1722217522", "prod_name": "富临运业", "Hot": 1267}, {"StockID": "600635", "IsZz": "2", "IsHot": "0", "Reason": "大众交通为其子公司", "FirstShelveTime": "1722217420", "UpdateCacheTime": "1722217420", "prod_name": "大众公用", "Hot": 965}, {"StockID": "601188", "IsZz": "2", "IsHot": "0", "Reason": "对于既有产业如出租车业务，公司拟以龙运现代为基础，寻找科技型战略伙伴，打造网约车+巡游车的全业态格局", "FirstShelveTime": "1720574950", "UpdateCacheTime": "1720574950", "prod_name": "龙江交通", "Hot": 930}, {"StockID": "603528", "IsZz": "2", "IsHot": "0", "Reason": "公司旗下多伦车检与“T3出行正式签订车辆年检服务合作协议，本次合作落地城市为湖北省武汉市。", "FirstShelveTime": "1720576285", "UpdateCacheTime": "1720576285", "prod_name": "多伦科技", "Hot": 866}, {"StockID": "600662", "IsZz": "2", "IsHot": "0", "Reason": "子公司强生积极开展网约车服务", "FirstShelveTime": "1720576437", "UpdateCacheTime": "1720576437", "prod_name": "外服控股", "Hot": 387}]}, "Level2": []}, {"Level1": {"ID": "2786", "Name": "百度萝卜快跑", "ZSCode": "0", "FirstShelveTime": "1720574655", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "002806", "IsZz": "2", "IsHot": "0", "Reason": "公司与阿波罗智能技术（北京）有限公司持续加强战略合作，相关智能底盘产品搭载百度Apollo9.0版自动驾驶系统", "FirstShelveTime": "1720576201", "UpdateCacheTime": "1720576201", "prod_name": "华锋股份", "Hot": 5913}, {"StockID": "002829", "IsZz": "2", "IsHot": "0", "Reason": "公司无人系统相关产品已配套萝卜快跑无人出租车实现量产。", "FirstShelveTime": "1720574775", "UpdateCacheTime": "1720574775", "prod_name": "星网宇达", "Hot": 3323}, {"StockID": "600686", "IsZz": "2", "IsHot": "0", "Reason": "与百度Apollo合作的L4级自动驾驶小巴阿波龙在18年7月量产,是国内首款量产无人驾驶巴士", "FirstShelveTime": "1720772594", "UpdateCacheTime": "1720772594", "prod_name": "金龙汽车", "Hot": 2320}, {"StockID": "002813", "IsZz": "2", "IsHot": "0", "Reason": "百度Apollo生态首批成员，2016年与百度车联网达成战略合作，双方就智能车机、车联网产品深入合作", "FirstShelveTime": "1720574775", "UpdateCacheTime": "1720574775", "prod_name": "路畅科技", "Hot": 1859}, {"StockID": "301488", "IsZz": "2", "IsHot": "0", "Reason": "公司已与萝卜快跑在自动泊车、超声波雷达等产品上达成配套合作", "FirstShelveTime": "1721210087", "UpdateCacheTime": "1721210087", "prod_name": "豪恩汽电", "Hot": 1622}, {"StockID": "300807", "IsZz": "2", "IsHot": "0", "Reason": "2020年与百度签署合作协议，深度参与Apollo计划，目前已在郑州自动驾驶公交等项目展开合作。", "FirstShelveTime": "1720574775", "UpdateCacheTime": "1720574775", "prod_name": "天迈科技", "Hot": 1458}, {"StockID": "300177", "IsZz": "2", "IsHot": "0", "Reason": "公司与百度Apollo在高精度差分定位方案产品及技术领域保持合作。", "FirstShelveTime": "1720661027", "UpdateCacheTime": "1720661027", "prod_name": "中海达", "Hot": 1297}, {"StockID": "600733", "IsZz": "2", "IsHot": "0", "Reason": "2021年与百度Apollo智能驾驶事业部签订联合技术开发，双方共同完成了百度第五代Robotaxi产品Apollo Moon的研发", "FirstShelveTime": "1721021311", "UpdateCacheTime": "1721021311", "prod_name": "北汽蓝谷", "Hot": 1246}, {"StockID": "300627", "IsZz": "2", "IsHot": "0", "Reason": "5月24日互动易：公司目前与百度达成合作，向其robotaxi提供P-box等产品。", "FirstShelveTime": "1720620168", "UpdateCacheTime": "1720620168", "prod_name": "华测导航", "Hot": 1188}, {"StockID": "300456", "IsZz": "2", "IsHot": "0", "Reason": "公司针对百度 Apollo 3.0 推出的 MEMS 组合导航系统内置高精度 GNSS 板卡和高精度 MEMS 陀螺。", "FirstShelveTime": "1720574864", "UpdateCacheTime": "1720574864", "prod_name": "赛微电子", "Hot": 918}, {"StockID": "300978", "IsZz": "2", "IsHot": "0", "Reason": "百度北汽新一代Apollo Moon共享无人车的自动侧门开启技术为东箭协助提供", "FirstShelveTime": "1720620346", "UpdateCacheTime": "1720620346", "prod_name": "东箭科技", "Hot": 562}]}, "Level2": []}, {"Level1": {"ID": "2849", "Name": "客车", "ZSCode": "0", "FirstShelveTime": "1721096892", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "000868", "IsZz": "2", "IsHot": "1", "Reason": "2017年公司无人驾驶智能公交车在深圳全球首发并成功示范运行，成为中国首款在开放道路运行的无人驾驶智能公交车", "FirstShelveTime": "1721097153", "UpdateCacheTime": "1721097153", "prod_name": "安凯客车", "Hot": 5056}, {"StockID": "600686", "IsZz": "2", "IsHot": "0", "Reason": "公司是首批加入Apollo生态圈的唯一套车企业，与百度合作推出中国首辆商用级无人驾驶微循环车阿波龙", "FirstShelveTime": "1721096976", "UpdateCacheTime": "1721096976", "prod_name": "金龙汽车", "Hot": 2320}, {"StockID": "600066", "IsZz": "2", "IsHot": "0", "Reason": "宇通客车在2015年完成世界首例自动驾驶客车开放道路试验", "FirstShelveTime": "1721097287", "UpdateCacheTime": "1721097287", "prod_name": "宇通客车", "Hot": 501}]}, "Level2": []}, {"Level1": {"ID": "2787", "Name": "文远知行", "ZSCode": "0", "FirstShelveTime": "1720574655", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "000935", "IsZz": "2", "IsHot": "0", "Reason": "互动易：公司参与投资的智慧出行基金持有文远知行的股份。", "FirstShelveTime": "1740720832", "UpdateCacheTime": "1740720832", "prod_name": "四川双马", "Hot": 1693}, {"StockID": "300655", "IsZz": "2", "IsHot": "0", "Reason": "全资子公司善丰投资参与设立辅沅合伙，该基金的最终投资标的为广州文远知行科技有限公司。", "FirstShelveTime": "1720575005", "UpdateCacheTime": "1720575005", "prod_name": "晶瑞电材", "Hot": 1526}, {"StockID": "000550", "IsZz": "2", "IsHot": "0", "Reason": "互动易称公司联合文远知行研发的无人驾驶货车，目前已经应用于公开道路的同城货运场景", "FirstShelveTime": "1721120758", "UpdateCacheTime": "1721120758", "prod_name": "江铃汽车", "Hot": 811}]}, "Level2": []}], "Stocks": [], "StockList": [{"StockID": "600611", "Tag": [{"ID": "2785", "Name": "网约车", "Reason": "大众出行平台成功获得上海首家网约车平台资质"}], "prod_name": "大众交通", "HotNum": 3908}, {"StockID": "600650", "Tag": [{"ID": "2785", "Name": "网约车", "Reason": "锦江出租组织日常运营管理，正式在嘉定区1076公里开放测试道路范围内推出准商业化Robotaxi服务，由自动驾驶车辆组成的“智能网联出租车队”为上海市民提供出行服务。"}], "prod_name": "锦江在线", "HotNum": 5530}, {"StockID": "002813", "Tag": [{"ID": "2786", "Name": "百度萝卜快跑", "Reason": "百度Apollo生态首批成员，2016年与百度车联网达成战略合作，双方就智能车机、车联网产品深入合作"}], "prod_name": "路畅科技", "HotNum": 1859}, {"StockID": "300807", "Tag": [{"ID": "2786", "Name": "百度萝卜快跑", "Reason": "2020年与百度签署合作协议，深度参与Apollo计划，目前已在郑州自动驾驶公交等项目展开合作。"}], "prod_name": "天迈科技", "HotNum": 1458}, {"StockID": "002829", "Tag": [{"ID": "2786", "Name": "百度萝卜快跑", "Reason": "公司无人系统相关产品已配套萝卜快跑无人出租车实现量产。"}], "prod_name": "星网宇达", "HotNum": 3323}, {"StockID": "300456", "Tag": [{"ID": "2786", "Name": "百度萝卜快跑", "Reason": "公司针对百度 Apollo 3.0 推出的 MEMS 组合导航系统内置高精度 GNSS 板卡和高精度 MEMS 陀螺。"}], "prod_name": "赛微电子", "HotNum": 918}, {"StockID": "000421", "Tag": [{"ID": "2785", "Name": "网约车", "Reason": "公司已开展网约车业务。"}], "prod_name": "南京公用", "HotNum": 1283}, {"StockID": "601188", "Tag": [{"ID": "2785", "Name": "网约车", "Reason": "对于既有产业如出租车业务，公司拟以龙运现代为基础，寻找科技型战略伙伴，打造网约车+巡游车的全业态格局"}], "prod_name": "龙江交通", "HotNum": 930}, {"StockID": "300655", "Tag": [{"ID": "2787", "Name": "文远知行", "Reason": "全资子公司善丰投资参与设立辅沅合伙，该基金的最终投资标的为广州文远知行科技有限公司。"}], "prod_name": "晶瑞电材", "HotNum": 1526}, {"StockID": "002806", "Tag": [{"ID": "2786", "Name": "百度萝卜快跑", "Reason": "公司与阿波罗智能技术（北京）有限公司持续加强战略合作，相关智能底盘产品搭载百度Apollo9.0版自动驾驶系统"}], "prod_name": "华锋股份", "HotNum": 5913}, {"StockID": "603528", "Tag": [{"ID": "2785", "Name": "网约车", "Reason": "公司旗下多伦车检与“T3出行正式签订车辆年检服务合作协议，本次合作落地城市为湖北省武汉市。"}], "prod_name": "多伦科技", "HotNum": 866}, {"StockID": "600662", "Tag": [{"ID": "2785", "Name": "网约车", "Reason": "子公司强生积极开展网约车服务"}], "prod_name": "外服控股", "HotNum": 387}, {"StockID": "603776", "Tag": [{"ID": "2785", "Name": "网约车", "Reason": "公司于2019年5月8日收到常州市交通运输局颁发的《网络预约出租汽车经营许可证》，可合法开展相关网约车的经营活动"}], "prod_name": "永安行  ", "HotNum": 5435}, {"StockID": "300627", "Tag": [{"ID": "2786", "Name": "百度萝卜快跑", "Reason": "5月24日互动易：公司目前与百度达成合作，向其robotaxi提供P-box等产品。"}], "prod_name": "华测导航", "HotNum": 1188}, {"StockID": "300978", "Tag": [{"ID": "2786", "Name": "百度萝卜快跑", "Reason": "百度北汽新一代Apollo Moon共享无人车的自动侧门开启技术为东箭协助提供"}], "prod_name": "东箭科技", "HotNum": 562}, {"StockID": "300177", "Tag": [{"ID": "2786", "Name": "百度萝卜快跑", "Reason": "公司与百度Apollo在高精度差分定位方案产品及技术领域保持合作。"}], "prod_name": "中海达", "HotNum": 1297}, {"StockID": "600686", "Tag": [{"ID": "2786", "Name": "百度萝卜快跑", "Reason": "与百度Apollo合作的L4级自动驾驶小巴阿波龙在18年7月量产,是国内首款量产无人驾驶巴士"}, {"ID": "2849", "Name": "客车", "Reason": "公司是首批加入Apollo生态圈的唯一套车企业，与百度合作推出中国首辆商用级无人驾驶微循环车阿波龙"}], "prod_name": "金龙汽车", "HotNum": 2320}, {"StockID": "600733", "Tag": [{"ID": "2786", "Name": "百度萝卜快跑", "Reason": "2021年与百度Apollo智能驾驶事业部签订联合技术开发，双方共同完成了百度第五代Robotaxi产品Apollo Moon的研发"}], "prod_name": "北汽蓝谷", "HotNum": 1246}, {"StockID": "000868", "Tag": [{"ID": "2849", "Name": "客车", "Reason": "2017年公司无人驾驶智能公交车在深圳全球首发并成功示范运行，成为中国首款在开放道路运行的无人驾驶智能公交车"}], "prod_name": "安凯客车", "HotNum": 5056}, {"StockID": "600066", "Tag": [{"ID": "2849", "Name": "客车", "Reason": "宇通客车在2015年完成世界首例自动驾驶客车开放道路试验"}], "prod_name": "宇通客车", "HotNum": 501}, {"StockID": "000550", "Tag": [{"ID": "2787", "Name": "文远知行", "Reason": "互动易称公司联合文远知行研发的无人驾驶货车，目前已经应用于公开道路的同城货运场景"}], "prod_name": "江铃汽车", "HotNum": 811}, {"StockID": "301488", "Tag": [{"ID": "2786", "Name": "百度萝卜快跑", "Reason": "公司已与萝卜快跑在自动泊车、超声波雷达等产品上达成配套合作"}], "prod_name": "豪恩汽电", "HotNum": 1622}, {"StockID": "600635", "Tag": [{"ID": "2785", "Name": "网约车", "Reason": "大众交通为其子公司"}], "prod_name": "大众公用", "HotNum": 965}, {"StockID": "002357", "Tag": [{"ID": "2785", "Name": "网约车", "Reason": "富临运业公告称将提供1500台车辆，在成都地区接入T3出行平台，提供运力。"}], "prod_name": "富临运业", "HotNum": 1267}, {"StockID": "000935", "Tag": [{"ID": "2787", "Name": "文远知行", "Reason": "互动易：公司参与投资的智慧出行基金持有文远知行的股份。"}], "prod_name": "四川双马", "HotNum": 1693}], "Power": 0, "Subscribe": 0, "ZT": {"000868": ["1", "9.93", "1751007739"]}, "IsGood": 0, "GoodNum": 687, "ComNum": 1162, "errcode": "0", "t": 0.011168999999999998}