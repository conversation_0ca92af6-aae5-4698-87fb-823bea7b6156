{"ID": "286", "Name": "AI智能体", "BriefIntro": "2025年3月6日，Monica.im发布全球首个通用AlAgent产品ManusAl，可全自动执行深度研究、复杂数据分析、合同审查、简历筛选等多类型人物，涵盖研究、生活、数据分析、教育、生产效率等多个场景。", "ClassLayer": "1", "Desc": "", "PlateSwitch": "1", "StkSwitch": "2", "Introduction": "<p>题材相关新闻</p><p><br/></p><p>3月6日，Monica.im发布全球首个通用AlAgent产品ManusAl，可全自动执行深度研究、复杂数据分析、合同审查、简历筛选等多类型人物，涵盖研究、生活、数据分析、教育、生产效率等多个场景。</p><p><span style=\"color: rgb(52, 48, 75); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, sans-serif; background-color: rgb(255, 255, 255);\">OpenAI准备发布一款能够独立执行任务的AI智能体（AI agent），项目代号为 “操作员”（Operator）。OpenAI计划在明年1月份将其作为研究预览版和开发者工具，首次向公众亮相。OpenAI的AI智能体开发预示着AI系统将向主动接触操作界面，而不仅仅是处理文本和图像的方向转变。</span></p><p><br/></p><p>题材相关介绍</p><p><br/></p><p>与大模型的区别</p><p><br/></p><p style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0px; margin-bottom: 0.859em; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\">AI agent（人工智能代理）和大模型（Large Language Models，LLMs）是人工智能领域的两个重要概念，它们之间有一些区别：</p><ol style=\"box-sizing: inherit; padding-inline-start: 2em; margin-top: 0.859em; font-size: 14px; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; letter-spacing: 0.5px; background-color: rgb(255, 255, 255);\" class=\" list-paddingleft-2\"><li><p style=\"box-sizing: inherit; line-height: 24px; margin-top: 0px; margin-bottom: 0px; max-width: 100%; overflow: visible; white-space: pre-wrap !important; word-break: break-word !important;\"><span style=\"box-sizing: inherit; font-weight: 700;\">目的和应用场景</span>：</p></li><ul style=\"box-sizing: inherit; padding-inline-start: 2em; margin-top: 0.859em; margin-bottom: 0.859em; padding-left: 1.25em;\" class=\" list-paddingleft-2\"><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">AI agent</span>：通常指的是一个集成了人工智能技术的软件程序，它能够执行特定的任务或一系列任务。AI agent可以是基于大模型的，也可以是基于其他类型的人工智能技术，如规则引擎、机器学习模型等。AI agent的目的是为了完成特定的目标，比如自动化客户服务、个人助理、智能家居控制等。</p></li><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">大模型</span>：通常指的是参数数量庞大、能够处理复杂任务的机器学习模型，尤其是指自然语言处理（NLP）领域的大型语言模型。这些模型通过大量的数据进行训练，能够理解和生成自然语言，执行如文本摘要、问答、文本生成等任务。</p></li></ul><li><p style=\"box-sizing: inherit; line-height: 24px; margin-top: 0px; margin-bottom: 0px; max-width: 100%; overflow: visible; white-space: pre-wrap !important; word-break: break-word !important;\"><span style=\"box-sizing: inherit; font-weight: 700;\">技术实现</span>：</p></li><ul style=\"box-sizing: inherit; padding-inline-start: 2em; margin-top: 0.859em; margin-bottom: 0.859em; padding-left: 1.25em;\" class=\" list-paddingleft-2\"><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">AI agent</span>：可能使用大模型作为其智能的一部分，也可能使用其他技术。AI agent的设计通常是为了解决特定的问题或任务，它可能包含多个不同类型的模型和算法。</p></li><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">大模型</span>：通常是通过深度学习技术实现的，特别是变换器（Transformer）架构，这种架构在处理序列数据时非常有效，尤其是在自然语言处理任务中。</p></li></ul><li><p style=\"box-sizing: inherit; line-height: 24px; margin-top: 0px; margin-bottom: 0px; max-width: 100%; overflow: visible; white-space: pre-wrap !important; word-break: break-word !important;\"><span style=\"box-sizing: inherit; font-weight: 700;\">交互方式</span>：</p></li><ul style=\"box-sizing: inherit; padding-inline-start: 2em; margin-top: 0.859em; margin-bottom: 0.859em; padding-left: 1.25em;\" class=\" list-paddingleft-2\"><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">AI agent</span>：可以与用户或其他系统进行交互，以执行任务或提供服务。它可以根据用户的输入和上下文来做出决策。</p></li><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">大模型</span>：通常是通过文本输入和输出来与用户交互，但也可以集成到其他类型的系统中，以提供更复杂的交互方式。</p></li></ul><li><p style=\"box-sizing: inherit; line-height: 24px; margin-top: 0px; margin-bottom: 0px; max-width: 100%; overflow: visible; white-space: pre-wrap !important; word-break: break-word !important;\"><span style=\"box-sizing: inherit; font-weight: 700;\">自主性和学习能力</span>：</p></li><ul style=\"box-sizing: inherit; padding-inline-start: 2em; margin-top: 0.859em; margin-bottom: 0.859em; padding-left: 1.25em;\" class=\" list-paddingleft-2\"><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">AI agent</span>：可能具有不同程度的自主性，一些AI agent能够根据经验学习和适应，而另一些则可能是基于预设的规则。</p></li><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">大模型</span>：通常不具备自主性，它们通过大量数据的训练来理解和生成语言，但不会从每次交互中“学习”或“适应”。</p></li></ul></ol><p class=\"last-node\" style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0.859em; margin-bottom: 0px; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\">总的来说，大模型是实现AI agent智能功能的一种技术，而AI agent是一个更广泛的概念，可以包含大模型在内的多种技术来实现其功能。</p><p><br/></p>", "CreateTime": "1731569602", "UpdateTime": "0", "Table": [], "Stocks": [{"StockID": "002987", "IsZz": "2", "IsHot": "0", "Reason": "2025年03月07日官微：京北方Al Agent 助力银行降本增效", "prod_name": "京北方", "Hot": 31649}, {"StockID": "002402", "IsZz": "2", "IsHot": "1", "Reason": "2025年2月17日互动易：公司已于2024年积极利用开源的千问大语言模型技术，开发了智能体示范应用", "prod_name": "和而泰", "Hot": 24883}, {"StockID": "003040", "IsZz": "2", "IsHot": "0", "Reason": "公司充分发挥在数智政务领域的合作优势，推动“AI+智能硬件”融合，助力各地政府机构打造窗口AI智能体", "prod_name": "楚天龙", "Hot": 21503}, {"StockID": "002657", "IsZz": "2", "IsHot": "0", "Reason": "公司深化技术研发，以“智能化”为技术核心理念，在多场景多基座大模型引擎、AI Agent 等相关方向投入研发", "prod_name": "中科金财", "Hot": 9955}, {"StockID": "300130", "IsZz": "2", "IsHot": "0", "Reason": "公司控股子公司上海拾贰区信息技术有限公司重点布局开展通用智能体技术研发，成功发布首款 AI“数字员工”产品", "prod_name": "新国都", "Hot": 8295}, {"StockID": "605398", "IsZz": "2", "IsHot": "0", "Reason": "公司及下属子公司积极推进大模型与运维场景的深度融合，集成大语言模型 LLM、人工智能代理 AI Agent、检索增强生成 RAG 以及多模态等技术", "prod_name": "新炬网络", "Hot": 6897}, {"StockID": "301315", "IsZz": "2", "IsHot": "2", "Reason": "2025年3月3日互动易回复，公司已投入研发工业领域的AI智能体应用", "prod_name": "威士顿", "Hot": 6133}, {"StockID": "600410", "IsZz": "2", "IsHot": "0", "Reason": "发布基于DeepSeek的“企业运营智能体”", "prod_name": "华胜天成", "Hot": 4908}, {"StockID": "300253", "IsZz": "2", "IsHot": "0", "Reason": "2024年11月21日互动易：公司自2017年就成立了人工智能实验室，目前已有医疗智能体相关的技术积累", "prod_name": "卫宁健康", "Hot": 4632}, {"StockID": "600734", "IsZz": "2", "IsHot": "0", "Reason": "公司加速推进算力调度平台、智能体应用平台、大模型能力引擎等核心产品的研发工作", "prod_name": "实达集团", "Hot": 4026}, {"StockID": "002131", "IsZz": "2", "IsHot": "0", "Reason": "2024年半年报:利欧数字推出的营销领域大模型「利欧归一」通过 AI+投放产品的战略布局，致力于以A1技术来构建新型的投放模式", "prod_name": "利欧股份", "Hot": 3983}, {"StockID": "000409", "IsZz": "2", "IsHot": "0", "Reason": "山东能源2025年2月26日微信公众号：云鼎科技打造了NLP应用智能体", "prod_name": "云鼎科技", "Hot": 3930}, {"StockID": "000555", "IsZz": "2", "IsHot": "0", "Reason": "2025年02月18日官微：旗下“神州灵境”与DeepSeek大模型R1&amp;V3的接入，率先推出“运维智能体、办公智能体和业务管理智能体”", "prod_name": "神州信息", "Hot": 3560}, {"StockID": "002530", "IsZz": "2", "IsHot": "0", "Reason": "公司与H公司合作的“欣智悦财税大模型”采用开放式架构设计，结合公司财税产品和开放平台，构建财税AI Agent智能体", "prod_name": "金财互联", "Hot": 3481}, {"StockID": "600839", "IsZz": "2", "IsHot": "0", "Reason": "2024年，公司在德国柏林消费电子展览会上携全屋AI家电震撼登场，正式发布了全球首款沧海智能体AI TV和行业首个白电垂域大模型——长虹美菱智汇家AI大模型。", "prod_name": "四川长虹", "Hot": 3315}, {"StockID": "000665", "IsZz": "2", "IsHot": "0", "Reason": "2025年02月21日人民邮电报官微：湖北广电通过打造“楚韵智能体”，深度融合荆楚文化数字资产，训练出具备方言理解能力的专属模型", "prod_name": "湖北广电", "Hot": 3194}, {"StockID": "002354", "IsZz": "2", "IsHot": "0", "Reason": "2025年3月6日官微：构建具身智能通用AI Agent", "prod_name": "天娱数科", "Hot": 3190}, {"StockID": "300674", "IsZz": "2", "IsHot": "0", "Reason": "公司已在多家金融机构落地了大模型业务场景智能体", "prod_name": "宇信科技", "Hot": 3120}, {"StockID": "300798", "IsZz": "2", "IsHot": "0", "Reason": "子公司英智官网：我们的企业AI智能体产品，与企业私有化大模型结合，提供丰富的智能体问答权限管控功能", "prod_name": "锦鸡股份", "Hot": 3113}, {"StockID": "002881", "IsZz": "2", "IsHot": "0", "Reason": "2025年1月26日官微：公司结合美格智能自研的AIMO智能体及DeepSeek-R1模型的基础能力", "prod_name": "美格智能", "Hot": 2981}, {"StockID": "300248", "IsZz": "2", "IsHot": "0", "Reason": "公司基于盘古大模型、通义千问大模型打造了“小美同学”校园生活AI产品。“", "prod_name": "新开普", "Hot": 2945}, {"StockID": "600797", "IsZz": "2", "IsHot": "0", "Reason": "2025年1月10日互动易：华通云数据是火山引擎的HiAgent的合作伙伴", "prod_name": "浙大网新", "Hot": 2752}, {"StockID": "002929", "IsZz": "2", "IsHot": "0", "Reason": "公司的曲尺平台在通用开源大模型上基于AI agent技术", "prod_name": "润建股份", "Hot": 2669}, {"StockID": "300377", "IsZz": "2", "IsHot": "0", "Reason": "公司“携手”昇腾A1、麒麟操作系统推出的“AI-AGENT专业服务器”", "prod_name": "赢时胜", "Hot": 2640}, {"StockID": "300047", "IsZz": "2", "IsHot": "0", "Reason": "公司在AI 智能体等方面都有相应的产品", "prod_name": "天源迪科", "Hot": 2558}, {"StockID": "000948", "IsZz": "2", "IsHot": "0", "Reason": "2025年3月3日互动易：公司已将全AI Agent助手平台等解决方案接入DeepSeek", "prod_name": "南天信息", "Hot": 2533}, {"StockID": "300846", "IsZz": "2", "IsHot": "0", "Reason": "公司的一体机产品体系不仅搭载了基于先进模型的应用以及智能 Agent 产品，更致力于为开发者构建完善的生态环境", "prod_name": "首都在线", "Hot": 2470}, {"StockID": "002065", "IsZz": "2", "IsHot": "0", "Reason": "公司打造安全可信的基于私有化大模型的“智多型（A.I.Cogniflex）”AI Agent平台", "prod_name": "东华软件", "Hot": 2454}, {"StockID": "605069", "IsZz": "2", "IsHot": "0", "Reason": "公司“数智生态”业务将智慧监测、智能设备、智慧水务大模型应用在在生态、水务垂直领域场景，提供生态水务智能体及运维一体化解决方案。", "prod_name": "正和生态", "Hot": 2410}, {"StockID": "000681", "IsZz": "2", "IsHot": "0", "Reason": "公司基于自研与合作伙伴共同开发ai agent智能体，例如智能搜索 智能配图 一键成片等", "prod_name": "视觉中国", "Hot": 2358}, {"StockID": "002757", "IsZz": "2", "IsHot": "0", "Reason": "已上线与微软展开联合创新的小鹭AIGC智能助手，致力于为行业客户提供AIGC应用产品和解决方案", "prod_name": "南兴股份", "Hot": 2348}, {"StockID": "301287", "IsZz": "2", "IsHot": "0", "Reason": "互动易称，公司AI智能体产品应用场景广泛，除了体育教室和户外运动产品外，其智能健身器材通过 AI 智能体可对运动数据实时监测与分析，为用户提供精准的运动效果评估和个性化的训练计划调整。", "prod_name": "康力源", "Hot": 2257}, {"StockID": "300339", "IsZz": "2", "IsHot": "0", "Reason": "公司推出的新一代AI Agent智能中台可以基于各类基础大模型的智能体能力及其强大的生态优势，提供全流程的智能服务支持", "prod_name": "润和软件", "Hot": 2202}, {"StockID": "002093", "IsZz": "2", "IsHot": "0", "Reason": "公司与主流大模型服务商合作，构建深耕于社区养老以及居家养老场景的 AI Agent", "prod_name": "国脉科技", "Hot": 2164}, {"StockID": "002195", "IsZz": "2", "IsHot": "0", "Reason": "2025年1月14日官微：与黑芝麻智能合作推出基于武当 C1200 芯片的 AI Agent 解决方案", "prod_name": "岩山科技", "Hot": 2138}, {"StockID": "600602", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月7日全资子公司万邦软件官微：南洋星宫自主研发的智能平台功能丰富，涵盖知识检索、数据查询、Agent 开发等核心功能", "prod_name": "云赛智联", "Hot": 2133}, {"StockID": "600186", "IsZz": "2", "IsHot": "0", "Reason": "公司部署的DeepSeek R1为深度求索开源大模型，智能体平台上支持多用户使用问答。", "prod_name": "莲花控股", "Hot": 2113}, {"StockID": "600588", "IsZz": "2", "IsHot": "0", "Reason": "公司推出了分别基于智能体、人机交互、知识增强应用框架的“智友”、“数智员工”、“智能大搜”3项重要产品。", "prod_name": "用友网络", "Hot": 2008}, {"StockID": "601360", "IsZz": "2", "IsHot": "0", "Reason": "在ToC端，公司借鉴New Bing的模式，推出新一代生成式AI搜索引擎、AI浏览器、AI个人助理等产品。", "prod_name": "三六零  ", "Hot": 1980}, {"StockID": "301396", "IsZz": "2", "IsHot": "0", "Reason": "公司通过探索大模型与AI Agent相融合，开展产业多智能体研究，开展智慧园区多智能体、农业多智能体相关研究", "prod_name": "宏景科技", "Hot": 1954}, {"StockID": "301316", "IsZz": "2", "IsHot": "0", "Reason": "2024年12月11日官微：“悦见”增强型企业智能体，是生成式人工智能场景模型应用的优秀解决方案", "prod_name": "慧博云通", "Hot": 1952}, {"StockID": "300170", "IsZz": "2", "IsHot": "0", "Reason": "公司H-Copilot融合AIGC平台以及丰富的产品API，大量应用于具体场景的智能助手", "prod_name": "汉得信息", "Hot": 1916}, {"StockID": "300418", "IsZz": "2", "IsHot": "0", "Reason": "2023年12月1日，昆仑万维正式发布“天工SkyAgents”平台", "prod_name": "昆仑万维", "Hot": 1857}, {"StockID": "000785", "IsZz": "2", "IsHot": "0", "Reason": "互动易：居然设计家（Homestyler）利用大模型、AI等数智化能力，率先于海外上线了AI Agent设计智能体——AI设计助手", "prod_name": "居然智家", "Hot": 1832}, {"StockID": "300840", "IsZz": "2", "IsHot": "0", "Reason": "公司发力C端市场，结合最新的AI科技，打造“生活方式智能体”集群", "prod_name": "酷特智能", "Hot": 1685}, {"StockID": "301236", "IsZz": "2", "IsHot": "0", "Reason": "公司依托天璇MaaS企业大模型技术底座重磅推出天璇AutoAgent企业智能体编排平台，提供零代码、一站式智能体开发解决方案", "prod_name": "软通动力", "Hot": 1642}, {"StockID": "600986", "IsZz": "2", "IsHot": "0", "Reason": "2025年3月7日公众号发文，“好奇飞梭”通过融合DeepSeek“技术，形成了自身核心决策模型，实现了“多Agent协同”的效果。", "prod_name": "浙文互联", "Hot": 1607}, {"StockID": "688590", "IsZz": "2", "IsHot": "0", "Reason": "公司面向保险客户提供新致新知2.0平台，提供智能客户助手、数据查询助手、报告生成助手等Agent", "prod_name": "新致软件", "Hot": 1582}, {"StockID": "300353", "IsZz": "2", "IsHot": "0", "Reason": "投资者互动平台表示，公司基于国内头部大模型公司产品，结合开源模型开发工业智能体应用，适应工业现场业务。", "prod_name": "东土科技", "Hot": 1553}, {"StockID": "603636", "IsZz": "2", "IsHot": "0", "Reason": "发布 AI 智能体服务平台“白泽启元”、政务公文写作与协同平台“白泽文墨”、智能体客服平台“白泽对话管理”以及智能体 BI 产品“白泽云图”等白泽系列四款大模型智能体产品", "prod_name": "南威软件", "Hot": 1539}, {"StockID": "603108", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月25日微信公众号发布，润达医疗携手华为基于DeepSeek大模型推出“华擎智医”训推一体机 开启智能诊疗新范式", "prod_name": "润达医疗", "Hot": 1511}, {"StockID": "603039", "IsZz": "2", "IsHot": "0", "Reason": "公司合作腾讯，打造“市场、销售、合同、采购”等10多种场景Agent", "prod_name": "泛微网络", "Hot": 1508}, {"StockID": "688158", "IsZz": "2", "IsHot": "0", "Reason": "全球首款AI多智能体开发团队MGX（MetaGPT X）正式上线，优刻得云平台为其提供核心算力支持", "prod_name": "优刻得  ", "Hot": 1486}, {"StockID": "002279", "IsZz": "2", "IsHot": "0", "Reason": "久其女娲GPT通过自身大模型能力，推动政企客户实现数字化转型。", "prod_name": "久其软件", "Hot": 1439}, {"StockID": "002236", "IsZz": "2", "IsHot": "0", "Reason": "根据2025年2月27日官微，大华鲁班 +DeepSeek大模型技术，让园区管理拥有&quot;会思考、能预判、善执行&quot;的智能体。", "prod_name": "大华股份", "Hot": 1381}, {"StockID": "300229", "IsZz": "2", "IsHot": "0", "Reason": "2025年1月14日互动易：拓尔思的拓天大模型一体化平台已经提供了完整的AI Agent工具链", "prod_name": "拓尔思", "Hot": 1376}, {"StockID": "002315", "IsZz": "2", "IsHot": "0", "Reason": "于2023年4月25日推出中国制造网人工智能代理——AI 麦可", "prod_name": "焦点科技", "Hot": 1349}, {"StockID": "603171", "IsZz": "2", "IsHot": "0", "Reason": "公司“犀友”AI智能体应用平台通过API调用，将通义千问大模型应用于数智化运营、财税咨询、爱搜税等多个AI SaaS场景中", "prod_name": "税友股份", "Hot": 1324}, {"StockID": "002230", "IsZz": "2", "IsHot": "0", "Reason": "2024年4月26日，科大讯飞推出星火智能体平台", "prod_name": "科大讯飞", "Hot": 1239}, {"StockID": "300058", "IsZz": "2", "IsHot": "0", "Reason": "2024年11月28日微信公众号发布，蓝色光标宣布与火山引擎达成深度合作，双方将基于火山方舟、豆包·视频生成模型、扣子专业版智能体开发平台，在视频生成、视频服务解决方案以及营销行业智能体应用等领域展开深入合作。", "prod_name": "蓝色光标", "Hot": 1231}, {"StockID": "603206", "IsZz": "2", "IsHot": "0", "Reason": "公司积极推动“大模型”在服务领域、企业数字化中的应用，从而催生了“小嘉AI”智能体", "prod_name": "嘉环科技", "Hot": 1201}, {"StockID": "300746", "IsZz": "2", "IsHot": "0", "Reason": "2025年3月，子公司伏泰科技推出AI智能体新产品：智问推算一体机", "prod_name": "汉嘉设计", "Hot": 1165}, {"StockID": "301208", "IsZz": "2", "IsHot": "0", "Reason": "互动易：公司自主研发的中亦图灵智能可观测平台是新一代应用性能监控平台，拥有One Agent等核心能力", "prod_name": "中亦科技", "Hot": 1158}, {"StockID": "002439", "IsZz": "2", "IsHot": "0", "Reason": "天阗 AI 智能体是以“智检测，慧守护”为理念，基于公司安全大模型构建的一个立体感知、全域协同、精准判断、持续进化、开放的智能检测防御能力实体", "prod_name": "启明星辰", "Hot": 1135}, {"StockID": "002362", "IsZz": "2", "IsHot": "0", "Reason": "公司依托天地大模型，形成了多个AI Agent并落地应用", "prod_name": "汉王科技", "Hot": 1069}, {"StockID": "300378", "IsZz": "2", "IsHot": "0", "Reason": "公司与微软在中国台湾地区发布结合微软 OpenAI 的个人智能助理“娜娜帮我”", "prod_name": "鼎捷数智", "Hot": 1062}, {"StockID": "300687", "IsZz": "2", "IsHot": "0", "Reason": "公司深度绑定华为，AI Agent赋能业务流程智能化升级", "prod_name": "赛意信息", "Hot": 1027}, {"StockID": "301078", "IsZz": "2", "IsHot": "0", "Reason": "孩子王与涂鸦智能签署合作协议，共同开发智能体并打造AI伴身智能硬件", "prod_name": "孩子王", "Hot": 1014}, {"StockID": "300366", "IsZz": "2", "IsHot": "0", "Reason": "公司通过搭建生成式智能认知平台，Agent等技术，构建复杂业务智能流转、行业图谱快速构建、多维数据可视分析等能力，同时持续打造云边一体化智能平台", "prod_name": "创意信息", "Hot": 1010}, {"StockID": "300608", "IsZz": "2", "IsHot": "0", "Reason": "公司推出九思大模型，以智能体的模式，作为企业级大脑，改变企业 IT 系统的人机交互模式。", "prod_name": "思特奇", "Hot": 962}, {"StockID": "603000", "IsZz": "2", "IsHot": "0", "Reason": "2024年04月02日官微：“社交智能助理”是一个AI Agent，连接了用户推荐模型、智能创作模型等11种大模型", "prod_name": "人民网  ", "Hot": 948}, {"StockID": "300250", "IsZz": "2", "IsHot": "0", "Reason": "互动易：公司在AI Agent方向已有明确布局，主要聚焦于基于运维大模型的AI Agent和客服AI Agent的研发", "prod_name": "初灵信息", "Hot": 940}, {"StockID": "300496", "IsZz": "2", "IsHot": "0", "Reason": "公司的Rubik Av at ar就是一个包括AI Agent等多种技术的融合创新产品", "prod_name": "中科创达", "Hot": 888}, {"StockID": "300996", "IsZz": "2", "IsHot": "0", "Reason": "公司 AI Agent 技术主要面向企业应用中的复杂任务，聚焦财务共享、全面预算、金融保险、设备运维、司库等优势业务领域", "prod_name": "普联软件", "Hot": 880}, {"StockID": "002649", "IsZz": "2", "IsHot": "0", "Reason": "博彦科技作为世界级的IT咨询、服务及行业解决方案提供商受邀参会，并携超写实数字人--小言，以全新的数字人主持形象亮相本届论坛", "prod_name": "博彦科技", "Hot": 850}, {"StockID": "603859", "IsZz": "2", "IsHot": "0", "Reason": "公司乐仓智能体上线，具备AI百科、AI创造及AI搜索能力，能够快速进行应用创造", "prod_name": "能科科技", "Hot": 841}, {"StockID": "600718", "IsZz": "2", "IsHot": "0", "Reason": "公司积极推进与国产算力和 AI 生态适配，与合作伙伴共同推动“大模型智能体一体机” 的适配和测试", "prod_name": "东软集团", "Hot": 837}, {"StockID": "600756", "IsZz": "2", "IsHot": "0", "Reason": "2024年11月29日官微：浪潮“灵犀知识服务智能体”正式发布", "prod_name": "浪潮软件", "Hot": 834}, {"StockID": "688343", "IsZz": "2", "IsHot": "0", "Reason": "云天励飞凭借在边缘AI和大模型领域的深厚技术积累，通过在智能眼镜等硬件产品中集成云天天书大模型，为其赋予AI智能体能力", "prod_name": "云天励飞", "Hot": 826}, {"StockID": "300634", "IsZz": "2", "IsHot": "0", "Reason": "公司已拓展接入鸿蒙“小艺”智能体应用", "prod_name": "彩讯股份", "Hot": 799}, {"StockID": "300113", "IsZz": "2", "IsHot": "0", "Reason": "顺网科技发布了自己的智能体引擎——灵悉引擎", "prod_name": "顺网科技", "Hot": 781}, {"StockID": "300002", "IsZz": "2", "IsHot": "0", "Reason": "2024年11月21日官微：神州泰岳已成功打造了多个运维智能体（Agent），涵盖核心网运维、IP网络运维、故障监控等多个领域，并致力于推动其在高价值场景中的应用。", "prod_name": "神州泰岳", "Hot": 706}, {"StockID": "002707", "IsZz": "2", "IsHot": "0", "Reason": "根据2025年2月13日官微：YOYO是众信旅游专门为各位老板们打造的智能助手", "prod_name": "众信旅游", "Hot": 656}, {"StockID": "688228", "IsZz": "2", "IsHot": "0", "Reason": "开悟大模型智能应用中台通过融合AI Agent技术已经升级为开悟大模型智能体中台，能够为用户提供以AI-Native模式构建应用的新一代AI智能体平台", "prod_name": "开普云  ", "Hot": 645}, {"StockID": "688111", "IsZz": "2", "IsHot": "0", "Reason": "WPSAI2.0新增AI写作助手、AI阅读助手、AI数据助手、AI设计助手等功能", "prod_name": "金山办公", "Hot": 587}, {"StockID": "601928", "IsZz": "2", "IsHot": "0", "Reason": "公司拥有基于大模型的智能校对系统、凤凰AI智能体测系统等。", "prod_name": "凤凰传媒", "Hot": 581}, {"StockID": "300785", "IsZz": "2", "IsHot": "0", "Reason": "2024年上半年， “什么值得买” 推出了AI 智能体 购物助手“小值”", "prod_name": "值得买", "Hot": 536}, {"StockID": "688365", "IsZz": "2", "IsHot": "0", "Reason": "参股公司实在智能2024年8月26日公众号：实在Agent智能体开放公测", "prod_name": "光云科技", "Hot": 530}, {"StockID": "833030", "IsZz": "2", "IsHot": "0", "Reason": "公司的自建智能体Agent在理解语音地理位置描述的基础上，通过集成GIS系统能力，实现了语音内容与地图坐标的转化，为语音场景下的智能化匹配方案提供了底层支撑。", "prod_name": "立方控股", "Hot": 514}, {"StockID": "688369", "IsZz": "2", "IsHot": "0", "Reason": "公司AIAgent关联合同(含AAgent功能的合同)金额约2500万元，AI相关产品已逐步实现订单转化", "prod_name": "致远互联", "Hot": 486}, {"StockID": "300384", "IsZz": "2", "IsHot": "0", "Reason": "公司研发的《化纤工业智能体解决方案V1.0》，2024半年报告期内已实现收入2271万元", "prod_name": "三联虹普", "Hot": 390}, {"StockID": "688132", "IsZz": "2", "IsHot": "0", "Reason": "公司规划的是自主智能体，力图实现复杂流程自动化", "prod_name": "邦彦技术", "Hot": 382}, {"StockID": "688118", "IsZz": "2", "IsHot": "0", "Reason": "2024年升级低代码开发平台与数据中台产品，助力金融、军事、能源等行业客户加速Agent开发", "prod_name": "普元信息", "Hot": 370}], "StockList": [{"StockID": "002657", "Tag": [], "prod_name": "中科金财", "HotNum": 9955}, {"StockID": "002757", "Tag": [], "prod_name": "南兴股份", "HotNum": 2348}, {"StockID": "300840", "Tag": [], "prod_name": "酷特智能", "HotNum": 1685}, {"StockID": "301396", "Tag": [], "prod_name": "宏景科技", "HotNum": 1954}, {"StockID": "300608", "Tag": [], "prod_name": "思特奇", "HotNum": 962}, {"StockID": "300378", "Tag": [], "prod_name": "鼎捷数智", "HotNum": 1062}, {"StockID": "300248", "Tag": [], "prod_name": "新开普", "HotNum": 2945}, {"StockID": "300634", "Tag": [], "prod_name": "彩讯股份", "HotNum": 799}, {"StockID": "688132", "Tag": [], "prod_name": "邦彦技术", "HotNum": 382}, {"StockID": "300130", "Tag": [], "prod_name": "新国都", "HotNum": 8295}, {"StockID": "300384", "Tag": [], "prod_name": "三联虹普", "HotNum": 390}, {"StockID": "300113", "Tag": [], "prod_name": "顺网科技", "HotNum": 781}, {"StockID": "601360", "Tag": [], "prod_name": "三六零  ", "HotNum": 1980}, {"StockID": "002362", "Tag": [], "prod_name": "汉王科技", "HotNum": 1069}, {"StockID": "002065", "Tag": [], "prod_name": "东华软件", "HotNum": 2454}, {"StockID": "300418", "Tag": [], "prod_name": "昆仑万维", "HotNum": 1857}, {"StockID": "002230", "Tag": [], "prod_name": "科大讯飞", "HotNum": 1239}, {"StockID": "300785", "Tag": [], "prod_name": "值得买", "HotNum": 536}, {"StockID": "300366", "Tag": [], "prod_name": "创意信息", "HotNum": 1010}, {"StockID": "002530", "Tag": [], "prod_name": "金财互联", "HotNum": 3481}, {"StockID": "603859", "Tag": [], "prod_name": "能科科技", "HotNum": 841}, {"StockID": "300170", "Tag": [], "prod_name": "汉得信息", "HotNum": 1916}, {"StockID": "605398", "Tag": [], "prod_name": "新炬网络", "HotNum": 6897}, {"StockID": "300250", "Tag": [], "prod_name": "初灵信息", "HotNum": 940}, {"StockID": "600734", "Tag": [], "prod_name": "实达集团", "HotNum": 4026}, {"StockID": "300496", "Tag": [], "prod_name": "中科创达", "HotNum": 888}, {"StockID": "300996", "Tag": [], "prod_name": "普联软件", "HotNum": 880}, {"StockID": "002439", "Tag": [], "prod_name": "启明星辰", "HotNum": 1135}, {"StockID": "003040", "Tag": [], "prod_name": "楚天龙", "HotNum": 21503}, {"StockID": "603636", "Tag": [], "prod_name": "南威软件", "HotNum": 1539}, {"StockID": "688369", "Tag": [], "prod_name": "致远互联", "HotNum": 486}, {"StockID": "002315", "Tag": [], "prod_name": "焦点科技", "HotNum": 1349}, {"StockID": "300339", "Tag": [], "prod_name": "润和软件", "HotNum": 2202}, {"StockID": "301236", "Tag": [], "prod_name": "软通动力", "HotNum": 1642}, {"StockID": "000785", "Tag": [], "prod_name": "居然智家", "HotNum": 1832}, {"StockID": "300674", "Tag": [], "prod_name": "宇信科技", "HotNum": 3120}, {"StockID": "688118", "Tag": [], "prod_name": "普元信息", "HotNum": 370}, {"StockID": "605069", "Tag": [], "prod_name": "正和生态", "HotNum": 2410}, {"StockID": "603206", "Tag": [], "prod_name": "嘉环科技", "HotNum": 1201}, {"StockID": "603039", "Tag": [], "prod_name": "泛微网络", "HotNum": 1508}, {"StockID": "688590", "Tag": [], "prod_name": "新致软件", "HotNum": 1582}, {"StockID": "688111", "Tag": [], "prod_name": "金山办公", "HotNum": 587}, {"StockID": "300687", "Tag": [], "prod_name": "赛意信息", "HotNum": 1027}, {"StockID": "600588", "Tag": [], "prod_name": "用友网络", "HotNum": 2008}, {"StockID": "833030", "Tag": [], "prod_name": "立方控股", "HotNum": 514}, {"StockID": "000681", "Tag": [], "prod_name": "视觉中国", "HotNum": 2358}, {"StockID": "002131", "Tag": [], "prod_name": "利欧股份", "HotNum": 3983}, {"StockID": "002649", "Tag": [], "prod_name": "博彦科技", "HotNum": 850}, {"StockID": "688343", "Tag": [], "prod_name": "云天励飞", "HotNum": 826}, {"StockID": "301208", "Tag": [], "prod_name": "中亦科技", "HotNum": 1158}, {"StockID": "301287", "Tag": [], "prod_name": "康力源", "HotNum": 2257}, {"StockID": "603171", "Tag": [], "prod_name": "税友股份", "HotNum": 1324}, {"StockID": "688158", "Tag": [], "prod_name": "优刻得  ", "HotNum": 1486}, {"StockID": "600602", "Tag": [], "prod_name": "云赛智联", "HotNum": 2133}, {"StockID": "002929", "Tag": [], "prod_name": "润建股份", "HotNum": 2669}, {"StockID": "300229", "Tag": [], "prod_name": "拓尔思", "HotNum": 1376}, {"StockID": "000555", "Tag": [], "prod_name": "神州信息", "HotNum": 3560}, {"StockID": "603000", "Tag": [], "prod_name": "人民网  ", "HotNum": 948}, {"StockID": "002195", "Tag": [], "prod_name": "岩山科技", "HotNum": 2138}, {"StockID": "688228", "Tag": [], "prod_name": "开普云  ", "HotNum": 645}, {"StockID": "688365", "Tag": [], "prod_name": "光云科技", "HotNum": 530}, {"StockID": "600186", "Tag": [], "prod_name": "莲花控股", "HotNum": 2113}, {"StockID": "600986", "Tag": [], "prod_name": "浙文互联", "HotNum": 1607}, {"StockID": "300058", "Tag": [], "prod_name": "蓝色光标", "HotNum": 1231}, {"StockID": "002236", "Tag": [], "prod_name": "大华股份", "HotNum": 1381}, {"StockID": "002279", "Tag": [], "prod_name": "久其软件", "HotNum": 1439}, {"StockID": "301315", "Tag": [], "prod_name": "威士顿", "HotNum": 6133}, {"StockID": "300377", "Tag": [], "prod_name": "赢时胜", "HotNum": 2640}, {"StockID": "600797", "Tag": [], "prod_name": "浙大网新", "HotNum": 2752}, {"StockID": "300253", "Tag": [], "prod_name": "卫宁健康", "HotNum": 4632}, {"StockID": "002881", "Tag": [], "prod_name": "美格智能", "HotNum": 2981}, {"StockID": "600756", "Tag": [], "prod_name": "浪潮软件", "HotNum": 834}, {"StockID": "002093", "Tag": [], "prod_name": "国脉科技", "HotNum": 2164}, {"StockID": "600718", "Tag": [], "prod_name": "东软集团", "HotNum": 837}, {"StockID": "600410", "Tag": [], "prod_name": "华胜天成", "HotNum": 4908}, {"StockID": "002987", "Tag": [], "prod_name": "京北方", "HotNum": 31649}, {"StockID": "300798", "Tag": [], "prod_name": "锦鸡股份", "HotNum": 3113}, {"StockID": "000948", "Tag": [], "prod_name": "南天信息", "HotNum": 2533}, {"StockID": "002354", "Tag": [], "prod_name": "天娱数科", "HotNum": 3190}, {"StockID": "300002", "Tag": [], "prod_name": "神州泰岳", "HotNum": 706}, {"StockID": "000409", "Tag": [], "prod_name": "云鼎科技", "HotNum": 3930}, {"StockID": "002402", "Tag": [], "prod_name": "和而泰", "HotNum": 24883}, {"StockID": "300353", "Tag": [], "prod_name": "东土科技", "HotNum": 1553}, {"StockID": "300746", "Tag": [], "prod_name": "汉嘉设计", "HotNum": 1165}, {"StockID": "301316", "Tag": [], "prod_name": "慧博云通", "HotNum": 1952}, {"StockID": "300047", "Tag": [], "prod_name": "天源迪科", "HotNum": 2558}, {"StockID": "601928", "Tag": [], "prod_name": "凤凰传媒", "HotNum": 581}, {"StockID": "000665", "Tag": [], "prod_name": "湖北广电", "HotNum": 3194}, {"StockID": "603108", "Tag": [], "prod_name": "润达医疗", "HotNum": 1511}, {"StockID": "600839", "Tag": [], "prod_name": "四川长虹", "HotNum": 3315}, {"StockID": "300846", "Tag": [], "prod_name": "首都在线", "HotNum": 2470}, {"StockID": "301078", "Tag": [], "prod_name": "孩子王", "HotNum": 1014}, {"StockID": "002707", "Tag": [], "prod_name": "众信旅游", "HotNum": 656}], "Power": 0, "Subscribe": 0, "ZT": {"301315": ["0", "12.03", "1751007750"], "002402": ["1", "10.00", "1751007768"]}, "IsGood": 0, "GoodNum": 3071, "ComNum": 1981, "errcode": "0", "t": 0.011306999999999956}