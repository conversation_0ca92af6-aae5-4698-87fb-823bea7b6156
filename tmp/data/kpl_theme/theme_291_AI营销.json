{"ID": "291", "Name": "AI营销", "BriefIntro": "AppLovin今年累计涨幅 837.8%，远超英伟达同期涨幅，市值突破千亿美元大关，成为华尔街“AI新宠”。这家靠着广告起家的公司在AI热潮中发家致富，公司三季度业绩大超预期，AI广告引擎 AXON2.0提升了其广告盈利能力，AI营销的变现能力得到市场关注", "ClassLayer": "1", "Desc": "", "PlateSwitch": "1", "StkSwitch": "2", "Introduction": "<p>题材相关新闻</p><p><br/></p><p><span style=\"color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 14px; text-align: center;\">截止北京时间2024年12月5日美股收盘，AppLovin今年累计涨幅 837.8%，远超英伟达同期涨幅，市值突破千亿美元大关，成为华尔街“AI新宠”。这家靠着广告起家的公司在AI热潮中发家致富，公司三季度业绩大超预期，AI广告引擎 AXON2.0提升了其广告盈利能力，AI营销的变现能力得到市场关注。</span></p><p><span style=\"color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 14px; text-align: center;\"><br/></span></p><p><span style=\"color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 14px; text-align: center;\">题材相关介绍</span></p><p><span style=\"color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 14px; text-align: center;\"><br/></span></p><p><span style=\"color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 14px; text-align: center;\"><span style=\"color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; font-size: 14px; letter-spacing: 0.25px; white-space: pre-wrap; background-color: rgb(255, 255, 255);\">AI营销，即人工智能营销（Artificial Intelligence Marketing），是指运用人工智能技术来优化和提升营销活动的效率和效果</span></span></p><p><span style=\"color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 14px; text-align: center;\"><span style=\"color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; font-size: 14px; letter-spacing: 0.25px; white-space: pre-wrap; background-color: rgb(255, 255, 255);\"></span></span></p><h3 style=\"box-sizing: inherit; color: rgb(6, 6, 7); margin-bottom: 0px; font-size: 14px; line-height: 24px; margin-top: 1.14em; font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; letter-spacing: 0.5px; background-color: rgb(255, 255, 255);\">&nbsp;<span style=\"box-sizing: inherit;\">应用场景</span></h3><ul style=\"box-sizing: inherit; padding-inline-start: 2em; margin-top: 0.859em; font-size: 14px; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; letter-spacing: 0.5px; background-color: rgb(255, 255, 255);\" class=\" list-paddingleft-2\"><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">客户服务</span>：使用聊天机器人和虚拟助手提供24/7的客户支持。</p></li><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">内容创作</span>：自动生成营销文案、社交媒体帖子和广告内容。</p></li><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">个性化推荐</span>：根据用户行为和偏好推荐产品或服务。</p></li><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">广告投放</span>：优化广告投放策略，提高广告的点击率和转化率。</p></li></ul><p><span style=\"color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 14px; text-align: center;\"><span style=\"color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; font-size: 14px; letter-spacing: 0.25px; white-space: pre-wrap; background-color: rgb(255, 255, 255);\"><br/></span></span><br/></p><p><span style=\"color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 14px; text-align: center;\"></span></p><ol style=\"box-sizing: inherit; padding-inline-start: 2em; margin-top: 0.859em; font-size: 14px; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; letter-spacing: 0.5px; background-color: rgb(255, 255, 255);\" class=\" list-paddingleft-2\"><li><p style=\"box-sizing: inherit; line-height: 24px; margin-top: 0px; margin-bottom: 0px; max-width: 100%; overflow: visible; white-space: pre-wrap !important; word-break: break-word !important;\"><span style=\"box-sizing: inherit; font-weight: 700;\">AI营销新纪元</span>：国内新兴AI创业公司艾思智创（AICE Inc.）推出了革命性的创意灵感生成式AI解决方案——ADGo，宣称能够将繁琐的营销任务高效分解为多个专业智能体的协同作业，提升工作效率高达72倍。</p></li><li><p style=\"box-sizing: inherit; line-height: 24px; margin-top: 0px; margin-bottom: 0px; max-width: 100%; overflow: visible; white-space: pre-wrap !important; word-break: break-word !important;\"><span style=\"box-sizing: inherit; font-weight: 700;\">AI×营销应用案例</span>：Jasper作为基于OpenAI API的AIGC独角兽，通过对话式AI生成营销文案，服务于Airbnb、Volvo等企业，帮助他们批量生成投放文案、图片，并规避法律风险。麦当劳与Karen X Cheng合作，推出了使用NeRF技术的广告，为观众呈现了一个既有趣又新颖的视觉体验。</p></li><li><p style=\"box-sizing: inherit; line-height: 24px; margin-top: 0px; margin-bottom: 0px; max-width: 100%; overflow: visible; white-space: pre-wrap !important; word-break: break-word !important;\"><span style=\"box-sizing: inherit; font-weight: 700;\">AI营销市场趋势</span>：《2022AI营销白皮书》显示，44%的广告主认为AI营销将在未来两年成为主流营销概念和手段，预示着AI智能营销新时代的到来。《AI时代全链路营销进化白皮书》提出了AI营销的十大趋势，包括垂直领域模型深化、多模态能力增强、AI模型构建方式创新等。</p></li><li><p style=\"box-sizing: inherit; line-height: 24px; margin-top: 0px; margin-bottom: 0px; max-width: 100%; overflow: visible; white-space: pre-wrap !important; word-break: break-word !important;\"><span style=\"box-sizing: inherit; font-weight: 700;\">AI营销技术发展</span>：AI技术正在推动营销的发展，不仅提高触达精准用户效率，还在新营销场景拓展、新交互体验上具备了落地条件。AI技术将数字营销推进到AI+全域营销的阶段，未来AI营销将呈现出更多的想象力，集合在洞察、投放、创意内容生成、效果检测、客户关系管理等方面。</p></li><li><p style=\"box-sizing: inherit; line-height: 24px; margin-top: 0px; margin-bottom: 0px; max-width: 100%; overflow: visible; white-space: pre-wrap !important; word-break: break-word !important;\"><span style=\"box-sizing: inherit; font-weight: 700;\">AI营销的商业化验证</span>：AppLovin的市值飙升，以及AI广告商业化的可能性被验证。</p></li><li><p style=\"box-sizing: inherit; line-height: 24px; margin-top: 0px; margin-bottom: 0px; max-width: 100%; overflow: visible; white-space: pre-wrap !important; word-break: break-word !important;\"><span style=\"box-sizing: inherit; font-weight: 700;\">AI营销的规模预测</span>：群邑全球的报告预测，2022年全球AI广告将超过3700亿美元；2032年将覆盖绝大多数媒体，规模达到1.3万亿美元，占总体广告总收入的90%以上。</p></li><li><p style=\"box-sizing: inherit; line-height: 24px; margin-top: 0px; margin-bottom: 0px; max-width: 100%; overflow: visible; white-space: pre-wrap !important; word-break: break-word !important;\"><span style=\"box-sizing: inherit; font-weight: 700;\">AI技术在广告中的应用</span>：AI技术在提高触达精准用户效率外，在新营销场景拓展、新交互体验上也都具备了落地条件。</p></li></ol><p class=\"last-node\" style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0.859em; margin-bottom: 0px; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\">这些进展显示了AI营销技术的快速发展和在广告行业中的广泛应用，预示着AI营销将成为数字营销的下一个发展阶段。</p><p><span style=\"color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 14px; text-align: center;\"><br/></span><br/></p>", "CreateTime": "1733363933", "UpdateTime": "0", "Table": [], "Stocks": [{"StockID": "002878", "IsZz": "2", "IsHot": "0", "Reason": "AIGC文生视频技术在公司广告营销业务中有较多应用场景", "prod_name": "元隆雅图", "Hot": 9806}, {"StockID": "002131", "IsZz": "2", "IsHot": "0", "Reason": "利欧数字全面拥抱 AI 技术，成为 AI 营销时代的领军者", "prod_name": "利欧股份", "Hot": 3983}, {"StockID": "002354", "IsZz": "2", "IsHot": "0", "Reason": "公司推出的AIGC短视频智能营销助手“魔方Mix”，助力广告主、内容方、MCN机构等短视频营销需求方。", "prod_name": "天娱数科", "Hot": 3190}, {"StockID": "000681", "IsZz": "2", "IsHot": "0", "Reason": "公司不断强化“小前台、大中台”的模式，完善数据管理、智能AI、营销等中台", "prod_name": "视觉中国", "Hot": 2358}, {"StockID": "601360", "IsZz": "2", "IsHot": "0", "Reason": "360 智慧商业在传统搜索广告基础上，融合大模型技术与品牌直达展现形式，打造出 AIGC 驱动的搜索品牌广告投放新模式", "prod_name": "三六零  ", "Hot": 1980}, {"StockID": "603825", "IsZz": "2", "IsHot": "0", "Reason": "公司已经开始使用人工智能来完成一些重复性的任务，例如自动化生成广告文案、设计图片、宣传策略建议等。", "prod_name": "华扬联众", "Hot": 1897}, {"StockID": "000676", "IsZz": "2", "IsHot": "0", "Reason": "公司已在海外互联网媒体业务的部分业务中应用ChatGPT，PC端业务又上线了浏览器W ave Pro，可为用户提供AI助理、广告拦截等付费订阅功能", "prod_name": "智度股份", "Hot": 1880}, {"StockID": "002400", "IsZz": "2", "IsHot": "0", "Reason": "灵犀 AI 营销平台成功入选 2024 年广州市 “人工智能 +” 优秀解决方案，也为广告行业的数字化、智能化转型树立了优秀范例", "prod_name": "省广集团", "Hot": 1619}, {"StockID": "600986", "IsZz": "2", "IsHot": "0", "Reason": "程序化广告应用工具 “派智”，可帮助广告客户进行多模态创意内容生产，实现低成本的大规模广告内容生产，快速生成符合目标受众兴趣和需求的内容", "prod_name": "浙文互联", "Hot": 1607}, {"StockID": "300058", "IsZz": "2", "IsHot": "0", "Reason": "BlueAI 下半年将重点发力视频智能理解及创作，智能体工作流等方向，为 AI 的营销赋能", "prod_name": "蓝色光标", "Hot": 1231}, {"StockID": "002712", "IsZz": "2", "IsHot": "0", "Reason": "公司开发出如数据追踪、超大规模级广告投放、人工智能广告文案自动生成等一系列互联网广告的营销及数据优化技术", "prod_name": "思美传媒", "Hot": 962}, {"StockID": "301171", "IsZz": "2", "IsHot": "0", "Reason": "公司“数字人视频创作平台”项目，以达成AI广告营销文案生成为目标", "prod_name": "易点天下", "Hot": 843}, {"StockID": "605168", "IsZz": "2", "IsHot": "0", "Reason": "公司开创性推出营销领域多模态 AI 产品：“一个” AI", "prod_name": "三人行  ", "Hot": 694}, {"StockID": "300785", "IsZz": "2", "IsHot": "0", "Reason": "公司荣获“第24届IAI传鉴国际广告奖”。", "prod_name": "值得买", "Hot": 536}, {"StockID": "300612", "IsZz": "2", "IsHot": "0", "Reason": "公司在稳步提升数智营销及数字广告业务发展的同时，积极探索通用人工智能技术对内容生产侧的结构性升级改造。", "prod_name": "宣亚国际", "Hot": 532}], "StockList": [{"StockID": "002354", "Tag": [], "prod_name": "天娱数科", "HotNum": 3190}, {"StockID": "300058", "Tag": [], "prod_name": "蓝色光标", "HotNum": 1231}, {"StockID": "600986", "Tag": [], "prod_name": "浙文互联", "HotNum": 1607}, {"StockID": "301171", "Tag": [], "prod_name": "易点天下", "HotNum": 843}, {"StockID": "002400", "Tag": [], "prod_name": "省广集团", "HotNum": 1619}, {"StockID": "002131", "Tag": [], "prod_name": "利欧股份", "HotNum": 3983}, {"StockID": "002878", "Tag": [], "prod_name": "元隆雅图", "HotNum": 9806}, {"StockID": "000676", "Tag": [], "prod_name": "智度股份", "HotNum": 1880}, {"StockID": "300785", "Tag": [], "prod_name": "值得买", "HotNum": 536}, {"StockID": "603825", "Tag": [], "prod_name": "华扬联众", "HotNum": 1897}, {"StockID": "002712", "Tag": [], "prod_name": "思美传媒", "HotNum": 962}, {"StockID": "300612", "Tag": [], "prod_name": "宣亚国际", "HotNum": 532}, {"StockID": "601360", "Tag": [], "prod_name": "三六零  ", "HotNum": 1980}, {"StockID": "605168", "Tag": [], "prod_name": "三人行  ", "HotNum": 694}, {"StockID": "000681", "Tag": [], "prod_name": "视觉中国", "HotNum": 2358}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 350, "ComNum": 328, "errcode": "0", "t": 0.006265999999999994}