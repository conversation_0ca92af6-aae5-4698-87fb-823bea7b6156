{"ID": "213", "Name": "<PERSON>i智能助手", "BriefIntro": "近日，月之暗面宣布取得重大突破: 公司研发的AI大语言模型Kimi智能助手可支持200万字无损上下文", "ClassLayer": "1", "Desc": "", "PlateSwitch": "2", "StkSwitch": "2", "Introduction": "<p><strong>题材介绍：</strong></p><p>Kimi是由月之暗面科技有限公司（Moonshot AI）开发的人工智能助手。Kimi旨在通过高级的自然语言处理技术，为用户提供信息查询、数据分析、文本生成、语言翻译等多种智能服务。Kimi的设计目标是帮助用户更高效地处理信息，解决问题，并在各种场景下提供支持。</p><p>以下是Kimi的一些主要特点和功能：</p><p>1. 长文本处理能力：Kimi能够处理长达20万字的文本输入，这使得它在处理复杂文件资料、长文本总结等方面具有显著优势。</p><p>2. 多语言互译：Kimi支持多种语言的互译功能，能够帮助用户跨越语言障碍，进行无障碍沟通。</p><p>3. 文本生成和总结：用户可以通过提问或上传文件的方式，让Kimi迅速对大量文献和报告进行摘要提炼，或者生成新的文本内容。</p><p>4. 联网搜索：Kimi能够联网搜索实时信息，快速整合并提供详尽的回答，同时确保对话的丰富性和准确性。</p><p>5. 数据处理：Kimi可以将繁杂的数据整理成表格，辅助用户进行数据分析，提高工作效率。</p><p>6. 编写代码：Kimi能够辅助用户理解和编写代码，适合编程学习和实践。</p><p>7. 用户交互：Kimi提供了丰富的人格化聊天互动体验，可以模拟与名人对话，提供虚拟陪伴的效果。</p><p>8. 持续迭代：Kimi在技术上不断迭代，例如无损上下文长度已经提升到200万字，这使得它在处理长文档和长对话时更具优势。</p><p>Kimi的这些功能使其在个人助理、教育、研究、编程、数据分析等多个领域都有广泛的应用潜力。随着人工智能技术的不断进步，Kimi的能力也在不断扩展和完善，以满足用户日益增长的需求。</p><p><br/></p>", "CreateTime": "1710898596", "UpdateTime": "0", "Table": [], "Stocks": [{"StockID": "603533", "IsZz": "2", "IsHot": "0", "Reason": "公司已接入月之暗面旗下的AI对话助手产品Kimi", "prod_name": "掌阅科技", "Hot": 2767}, {"StockID": "603322", "IsZz": "2", "IsHot": "0", "Reason": "据超讯智能公众号，灵犀妙笔AI和火山方舟建立合作关系打通Kimi Chat链路，新增长文本处理功能。", "prod_name": "超讯通信", "Hot": 1451}, {"StockID": "300624", "IsZz": "2", "IsHot": "0", "Reason": "万兴科技旗下视频创意软件万兴喵影已率先接入Kimi，其AI写文案功能当前已采用Kimi大模型服务", "prod_name": "万兴科技", "Hot": 1061}, {"StockID": "300133", "IsZz": "2", "IsHot": "0", "Reason": "公司与月之暗面进行了模型接入层面的深度合作", "prod_name": "华策影视", "Hot": 930}, {"StockID": "301171", "IsZz": "2", "IsHot": "0", "Reason": "易点天下在互动平台表示，公司已接入<PERSON><PERSON>", "prod_name": "易点天下", "Hot": 843}, {"StockID": "300454", "IsZz": "2", "IsHot": "0", "Reason": "公司通过旗下的琥珀资本参与了kimi的研发公司月之暗面的最近一轮融资", "prod_name": "深信服", "Hot": 804}, {"StockID": "002432", "IsZz": "2", "IsHot": "0", "Reason": "2023 年 8 月，公司全资子公司九安香港与月之暗面相关主体签署投资相关协议，投资金额等值于约 1,000 万美元。2024年 3 月，公司参与投资的天津九尚一号管理咨询合伙企业与月之暗面相关主体签署投资相关协议，投资金额等值于约 2,000 万美元。", "prod_name": "九安医疗", "Hot": 597}, {"StockID": "300188", "IsZz": "2", "IsHot": "0", "Reason": "公司大模型内容检测平台支持对kimi生成式文本的检测识别。", "prod_name": "国投智能", "Hot": 553}, {"StockID": "300785", "IsZz": "2", "IsHot": "0", "Reason": "公司已经在消费内容社区“什么值得买”中部分商品详情页面接入了Kimi，为用户提供商品提炼总结服务。", "prod_name": "值得买", "Hot": 536}], "StockList": [{"StockID": "603533", "Tag": [], "prod_name": "掌阅科技", "HotNum": 2767}, {"StockID": "300454", "Tag": [], "prod_name": "深信服", "HotNum": 804}, {"StockID": "002432", "Tag": [], "prod_name": "九安医疗", "HotNum": 597}, {"StockID": "300133", "Tag": [], "prod_name": "华策影视", "HotNum": 930}, {"StockID": "603322", "Tag": [], "prod_name": "超讯通信", "HotNum": 1451}, {"StockID": "301171", "Tag": [], "prod_name": "易点天下", "HotNum": 843}, {"StockID": "300188", "Tag": [], "prod_name": "国投智能", "HotNum": 553}, {"StockID": "300624", "Tag": [], "prod_name": "万兴科技", "HotNum": 1061}, {"StockID": "300785", "Tag": [], "prod_name": "值得买", "HotNum": 536}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 407, "ComNum": 1055, "errcode": "0", "t": 0.008731999999999962}