{"ID": "302", "Name": "DeepSeek概念", "BriefIntro": "春节期间DeepSeek引发热议，在全球140个市场的应用商店下载榜上排名第一，仅上线18天日活1500万，增速是ChatGPT的13倍。因其开源低成本特性引发行业巨震，英伟达、AMD等美股科技公司纷纷下挫。\n近日，华为云和腾讯云分别宣布已上线基于其云服务的DeepSeek-R1相关服务、微软、英伟达、亚马逊、英特尔、AMD等亦上线DeepSeek模型服务。", "ClassLayer": "3", "Desc": "", "PlateSwitch": "1", "StkSwitch": "2", "Introduction": "<p><span style=\"font-family: 宋体, SimSun; font-size: 18px;\"><strong>一、题材相关新闻</strong></span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">2025年2月1日-2日，华为云和腾讯云分别宣布已上线基于其云服务的DeepSeek-R1 相关服务。此外，微软、英伟达、亚马逊、英特尔、AMD等已于近日上线 DeepSeek 模型服务</span></span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">2025年1月20日，深度求索公司正式发布 DeepSeek-R1，并同步开源模型权重，DeepSeek-R1 在数学、代码、自然语言推理等任务上性能对齐 OpenAl-01 正式版。</span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">2025年1月27日，Deepseek应用登顶苹果中国地区和美国地区应用商店免费 App 下载排行榜，并在美区下载榜上超越了 ChatGPT。</span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">上线18天日活达1500万。</span></p><p><br/></p><p><span style=\"font-family: 宋体, SimSun; font-size: 18px;\"><strong>二、题材相关介绍</strong></span></p><p><br/></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">一、什么是DeepSeek</span></p><p><br/></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">DeepSeek(深度求索)成立于2023年7月，由量化资管公司幻方量化创立，DeepSeek 专注于开发先进的大语言模型(LLM)和相关技术。</span></p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202506/1750752245714618.jpg\" title=\"1738721232873672.jpg\" alt=\"192865a910930683f6cd511ec3db068.jpg\"/></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">2024年1月5日，发布第一个大模型DeepSeekLLM;5月7日，发布MoE 架构的 DeepSeekV2;</span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">12月26日，上线 DeepSeek-V3 并同步开源,DeepSeek-V3 采用 FP8 训练,性能对其世界顶尖的闭源模型 GPT-40以及 Claude-3.5-Sonnet。</span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">2025年1月20日，发布 DeepSeek-R1,DeepSeek-R1 在数学、代码、自然语言推理等任务上，性能比肩 OpenAl01 正式版。同时，DeepSeek并通过DeepSeek-R1的输出，蒸馏了6个小模型开源给社区，其中 32B 和70B 模型在多项能力上实现了对标OpenAl o1-mini.</span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">DeepSeek-R1的发布广受关注，1月27日，Deepseek应用登顶苹果中国地区和美国地区应用商店免费 App 下载排行榜，并在美区下载榜上超越了 ChatGPT。</span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">Meta 首席 Al 科学家 Yann Lecun 表示“DeepSeek-R1 面世与其说意味着中国公司在 A| 领域正在超越美国公司，不如说意味着开源大模型正在超越闭源。”，OpenAl首席执行官Sam Altman 首次承认 OpenAl 的闭源策略“站在了历史错误的一边”。</span></p><p><br/></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">二、DeepSeek的技术创新</span></p><p><br/></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">DeepSeek 引领了全球Al基础技术创新，主要技术创新包括模型架构、算力优化、强化学习、知识蒸馏、开源策略等。</span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">(1)架构:DeepSeek-V3创新了自研的多头潜在注意力机制 MLA和 DeepSeek MoE 架构。</span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">(2)开源:DeepSeek开源代码和模型，加速技术迭代和生态系统建设。</span></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">(3)高效硬件利用:DeepSeek 并通过高效的负载均衡策略、FP8混合精度训练框架以及通信优化等技术手段，大幅提升了算力利用效率，降低了训练成本。<br/></span></p><p><br/></p><p><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">三、蒸馏技术介绍（来源&nbsp;智驻未来）</span></p><p><br/></p><p><span style=\"letter-spacing: 0em; font-size: 14px; font-family: 宋体, SimSun;\">1. DeepSeek蒸馏技术概述<br/></span></p><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding: 2px 13px; color: rgb(81, 81, 81); line-height: 1.5em; letter-spacing: 0em; background-image: linear-gradient(0deg, rgb(255, 177, 27) 40%, transparent 40%); background-position: 0% 0%; background-size: auto; background-repeat: no-repeat; background-attachment: scroll; background-origin: padding-box; background-clip: border-box; width: auto; height: auto; align-items: unset; border-style: none; border-width: 1px; border-color: rgb(0, 0, 0); border-radius: 0px; box-shadow: none; display: block; flex-direction: unset; float: unset; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">1.1 蒸馏技术定义与原理</span></h2><p><br/></p><section nodeleaf=\"\" style=\"text-align: center;\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202506/1750752245540501.png\" title=\"1738651418377412.png\" alt=\"image.png\"/></section><p><br/></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">模型蒸馏（Knowledge Distillation）是一种将大型复杂模型（教师模型）的知识迁移到小型高效模型（学生模型）的技术。其核心目标是在保持模型性能的同时，显著降低模型的计算复杂度和存储需求，使其更适合在资源受限的环境中部署。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">蒸馏技术的定义</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">在机器学习中，模型蒸馏是一种优化技术，通过模仿教师模型的输出，训练一个较小的学生模型，从而实现知识的传递。教师模型通常具有较高的性能，但计算成本高昂，而学生模型则更加轻量级，推理速度更快，且内存占用更少。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">蒸馏技术的原理</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">蒸馏技术的核心在于知识的传递和压缩。具体来说，教师模型通过其复杂的结构和大量的参数，学习到了数据中的复杂模式和特征。学生模型则通过模仿教师模型的输出，学习这些模式和特征，从而获得类似的性能。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">蒸馏过程通常包括以下几个步骤：</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">教师模型的训练</strong>：首先训练一个性能强大的教师模型，该模型通常具有大量的参数和复杂的结构。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">数据准备</strong>：从教师模型中提取推理数据样本，这些数据将用于训练学生模型。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">学生模型的训练</strong>：使用教师模型的输出作为监督信号，对较小的学生模型进行训练。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">优化与调整</strong>：通过调整学生模型的结构和参数，使其在保持高效的同时，尽可能接近教师模型的性能。</span></p><h1 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;\"><span style=\"line-height: 1.5em; letter-spacing: 0em; display: block; font-size: 14px; font-family: 宋体, SimSun;\">2. DeepSeek蒸馏技术的关键创新</span></h1><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding: 2px 13px; color: rgb(81, 81, 81); line-height: 1.5em; letter-spacing: 0em; background-image: linear-gradient(0deg, rgb(255, 177, 27) 40%, transparent 40%); background-position: 0% 0%; background-size: auto; background-repeat: no-repeat; background-attachment: scroll; background-origin: padding-box; background-clip: border-box; width: auto; height: auto; align-items: unset; border-style: none; border-width: 1px; border-color: rgb(0, 0, 0); border-radius: 0px; box-shadow: none; display: block; flex-direction: unset; float: unset; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">2.1 数据蒸馏与模型蒸馏结合</span></h2><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">DeepSeek的蒸馏技术将数据蒸馏与模型蒸馏相结合，实现了从大型复杂模型到小型高效模型的知识迁移。这种结合方式不仅提升了模型的性能，还显著降低了计算成本。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">数据蒸馏的作用</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">数据蒸馏通过优化训练数据，帮助小模型更高效地学习。DeepSeek利用强大的教师模型生成或优化数据，这些数据包括数据增强、伪标签生成和优化数据分布。例如，教师模型可以对原始数据进行扩展或修改，生成丰富的训练数据样本，从而提高数据的多样性和代表性。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">模型蒸馏的优化</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">在模型蒸馏方面，DeepSeek通过监督微调（SFT）的方式，将教师模型的知识迁移到学生模型中。具体来说，DeepSeek使用教师模型生成的800,000个推理数据样本对较小的基础模型（如Qwen和Llama系列）进行微调。这一过程不包括额外的强化学习（RL）阶段，使得蒸馏过程更加高效。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">结合的优势</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">数据蒸馏与模型蒸馏的结合，使得DeepSeek的蒸馏模型在推理基准测试中取得了显著的性能提升。例如，DeepSeek-R1-Distill-Qwen-7B在AIME 2024上实现了55.5%的Pass@1，超越了QwQ-32B-Preview（最先进的开源模型）。这种结合方式不仅提高了模型的性能，还降低了计算资源的需求，使得模型更适合在资源受限的环境中部署。</span></p><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding: 2px 13px; color: rgb(81, 81, 81); line-height: 1.5em; letter-spacing: 0em; background-image: linear-gradient(0deg, rgb(255, 177, 27) 40%, transparent 40%); background-position: 0% 0%; background-size: auto; background-repeat: no-repeat; background-attachment: scroll; background-origin: padding-box; background-clip: border-box; width: auto; height: auto; align-items: unset; border-style: none; border-width: 1px; border-color: rgb(0, 0, 0); border-radius: 0px; box-shadow: none; display: block; flex-direction: unset; float: unset; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">2.2 高效知识迁移策略</span></h2><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">DeepSeek在知识迁移策略上进行了多项创新，以实现高效的知识传递和模型优化。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">知识迁移策略的优化</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">DeepSeek采用了多种高效的知识迁移策略，包括基于特征的蒸馏和特定任务蒸馏。基于特征的蒸馏通过将教师模型中间层的特征信息传递给学生模型，帮助学生模型更好地捕捉数据的本质特征。特定任务蒸馏则针对不同的具体任务，如自然语言处理中的机器翻译和文本生成，对蒸馏过程进行针对性优化。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">蒸馏模型的性能提升</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">这些策略的优化使得DeepSeek的蒸馏模型在多个基准测试中表现优异。例如，DeepSeek-R1-Distill-Qwen-32B在AIME 2024上实现了72.6%的Pass@1，在MATH-500上实现了94.3%的Pass@1。这些结果表明，DeepSeek的蒸馏模型不仅在性能上接近甚至超越了原始的大型模型，还在计算效率上具有显著优势。</span></p><h1 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;\"><span style=\"line-height: 1.5em; letter-spacing: 0em; display: block; font-size: 14px; font-family: 宋体, SimSun;\">3. DeepSeek蒸馏模型的架构与训练</span></h1><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding: 2px 13px; color: rgb(81, 81, 81); line-height: 1.5em; letter-spacing: 0em; background-image: linear-gradient(0deg, rgb(255, 177, 27) 40%, transparent 40%); background-position: 0% 0%; background-size: auto; background-repeat: no-repeat; background-attachment: scroll; background-origin: padding-box; background-clip: border-box; width: auto; height: auto; align-items: unset; border-style: none; border-width: 1px; border-color: rgb(0, 0, 0); border-radius: 0px; box-shadow: none; display: block; flex-direction: unset; float: unset; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">3.1 蒸馏模型架构设计</span></h2><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">DeepSeek的蒸馏模型架构设计充分考虑了效率与性能的平衡，通过精心设计的模型结构，实现了从大型复杂模型到小型高效模型的知识迁移。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">教师模型与学生模型的选择</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">教师模型</strong>：DeepSeek选择的教师模型是其自主研发的大型语言模型DeepSeek-R1，该模型具有671B参数，具备强大的推理能力和广泛的知识覆盖。教师模型的强大性能为蒸馏过程提供了丰富的知识基础。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">学生模型</strong>：学生模型则基于Qwen和Llama系列架构，这些架构在计算效率和内存占用方面表现出色。通过选择这些架构，DeepSeek确保了学生模型在资源受限的环境中能够高效运行。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">架构设计的关键点</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">层次化特征提取</strong>：DeepSeek的蒸馏模型采用了层次化特征提取机制。教师模型在处理输入数据时，会生成多层特征表示，这些特征表示包含了数据的丰富语义信息。学生模型通过学习这些特征表示，能够更好地理解数据的结构和模式。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">多任务适应性</strong>：为了提高模型的泛化能力，DeepSeek的蒸馏模型设计了多任务适应性机制。学生模型不仅学习教师模型的输出，还针对不同的任务需求进行优化。例如，在自然语言处理任务中，学生模型能够根据具体的任务（如文本分类、机器翻译等）调整自身的结构和参数，从而更好地适应任务需求。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">架构优化策略</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">参数共享与压缩</strong>：DeepSeek采用了参数共享和压缩技术，以进一步优化模型的存储和计算效率。通过共享部分参数，学生模型在保持性能的同时，显著减少了参数数量和存储需求。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">轻量化模块设计</strong>：在学生模型中，DeepSeek引入了轻量化模块设计。这些模块在保持模型性能的同时，大幅降低了计算复杂度。例如，使用轻量级的注意力机制模块，使得学生模型能够高效地处理长文本输入。</span></p><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding: 2px 13px; color: rgb(81, 81, 81); line-height: 1.5em; letter-spacing: 0em; background-image: linear-gradient(0deg, rgb(255, 177, 27) 40%, transparent 40%); background-position: 0% 0%; background-size: auto; background-repeat: no-repeat; background-attachment: scroll; background-origin: padding-box; background-clip: border-box; width: auto; height: auto; align-items: unset; border-style: none; border-width: 1px; border-color: rgb(0, 0, 0); border-radius: 0px; box-shadow: none; display: block; flex-direction: unset; float: unset; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">3.2 训练过程与优化方法</span></h2><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">DeepSeek的蒸馏模型训练过程包括多个关键步骤，通过精心设计的训练策略和优化方法，确保了模型的高效训练和性能提升。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">训练数据的准备</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">数据来源：训练数据主要来自教师模型生成的推理数据样本。DeepSeek使用教师模型对大量输入数据进行处理，生成高质量的输出数据，这些数据作为学生模型的训练样本。数据增强：为了提高数据的多样性和代表性，DeepSeek采用了数据增强技术。通过对原始数据进行扩展、修改和优化，生成了丰富的训练数据样本，从而提高了学生模型的学习效率。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">训练过程</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">监督微调（SFT）</strong>：DeepSeek采用监督微调的方式，将教师模型的知识迁移到学生模型中。具体来说，学生模型通过学习教师模型的输出概率分布，调整自身的参数，以尽可能接近教师模型的性能。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">损失函数设计</strong>：在训练过程中，DeepSeek设计了混合损失函数，结合了软标签损失和硬标签损失。软标签损失鼓励学生模型模仿教师模型的输出概率分布，而硬标签损失则确保学生模型正确预测真实标签。通过这种混合损失函数，学生模型能够在保持高效的同时，学习到教师模型的关键知识。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">优化方法</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">温度参数调整</strong>：在蒸馏过程中，DeepSeek引入了温度参数来调整软标签的分布。较高的温度参数可以使分布更加平滑，从而帮助学生模型更好地学习教师模型的输出。随着训练的进行，温度参数逐渐降低，以提高蒸馏效果。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">动态学习率调整</strong>：为了提高训练效率，DeepSeek采用了动态学习率调整策略。通过根据训练进度和模型性能动态调整学习率，确保了模型在训练过程中的稳定性和收敛速度。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">正则化技术</strong>：为了避免过拟合，DeepSeek在训练过程中引入了正则化技术。例如，使用L2正则化项来约束模型的参数，防止模型过于复杂，从而提高模型的泛化能力。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">通过这些训练过程和优化方法，DeepSeek的蒸馏模型不仅在性能上接近甚至超越了原始的大型模型，还在计算效率和资源占用方面表现出色，为资源受限场景下的应用提供了强大的支持。</span></p><h1 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;\"><span style=\"line-height: 1.5em; letter-spacing: 0em; display: block; font-size: 14px; font-family: 宋体, SimSun;\">4. 蒸馏模型的性能表现</span></h1><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding: 2px 13px; color: rgb(81, 81, 81); line-height: 1.5em; letter-spacing: 0em; background-image: linear-gradient(0deg, rgb(255, 177, 27) 40%, transparent 40%); background-position: 0% 0%; background-size: auto; background-repeat: no-repeat; background-attachment: scroll; background-origin: padding-box; background-clip: border-box; width: auto; height: auto; align-items: unset; border-style: none; border-width: 1px; border-color: rgb(0, 0, 0); border-radius: 0px; box-shadow: none; display: block; flex-direction: unset; float: unset; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">4.1 推理效率提升</span></h2><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202506/1750752245481156.png\" title=\"1738651515918088.png\" alt=\"image.png\"/></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">DeepSeek的蒸馏模型在推理效率方面表现出显著的提升，这主要得益于模型结构的优化和蒸馏技术的应用。通过将知识从大型复杂模型（教师模型）迁移到小型高效模型（学生模型），DeepSeek的蒸馏模型在计算资源、内存使用和推理速度方面都实现了显著的优化。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">计算资源优化</strong>：蒸馏模型的参数量大幅减少，例如DeepSeek-R1-Distill-Qwen-7B的参数量仅为7B，相比原始的DeepSeek-R1（671B参数），计算复杂度显著降低。这使得模型在推理时所需的计算资源大幅减少，更适合在资源受限的环境中部署。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">内存占用减少</strong>：由于参数量的减少，蒸馏模型在内存占用方面也表现出色。以DeepSeek-R1-Distill-Llama-8B为例，其内存占用仅为原始模型的1/80左右。这意味着模型可以在更小的内存空间中运行，降低了硬件要求。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">推理速度提升</strong>：推理速度是衡量模型效率的重要指标。DeepSeek的蒸馏模型在推理速度上实现了显著提升。例如，DeepSeek-R1-Distill-Qwen-32B在处理复杂的推理任务时，推理速度比原始模型提高了约50倍。这种速度的提升使得模型能够更快地响应用户请求，提供实时的推理结果。</span></p><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding: 2px 13px; color: rgb(81, 81, 81); line-height: 1.5em; letter-spacing: 0em; background-image: linear-gradient(0deg, rgb(255, 177, 27) 40%, transparent 40%); background-position: 0% 0%; background-size: auto; background-repeat: no-repeat; background-attachment: scroll; background-origin: padding-box; background-clip: border-box; width: auto; height: auto; align-items: unset; border-style: none; border-width: 1px; border-color: rgb(0, 0, 0); border-radius: 0px; box-shadow: none; display: block; flex-direction: unset; float: unset; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">4.2 性能与原始模型对比</span></h2><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">尽管蒸馏模型的参数量大幅减少，但通过高效的知识迁移策略，DeepSeek的蒸馏模型在性能上仍然能够接近甚至超越原始的大型模型。这种性能的保持主要得益于以下几个方面：</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">性能保持策略</strong>：DeepSeek采用了多种策略来确保蒸馏模型的性能。例如，通过监督微调（SFT）的方式，将教师模型的推理数据样本用于学生模型的训练。这种策略使得学生模型能够学习到教师模型的关键知识和推理模式，从而在性能上接近教师模型。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">基准测试结果</strong>：在多个基准测试中，DeepSeek的蒸馏模型表现优异。例如，DeepSeek-R1-Distill-Qwen-7B在AIME 2024基准测试中实现了55.5%的Pass@1，超越了QwQ-32B-Preview（最先进的开源模型）。DeepSeek-R1-Distill-Qwen-32B在AIME 2024上实现了72.6%的Pass@1，在MATH-500上实现了94.3%的Pass@1。这些结果表明，蒸馏模型在推理任务上不仅能够保持高性能，还能在某些情况下超越原始模型。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\"><strong style=\"color: rgb(0, 0, 0);background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;width: auto;height: auto;border-style: none;border-width: 3px;border-color: rgba(0, 0, 0, 0.4);border-radius: 0px;\">与原始模型的对比</strong>：通过对比蒸馏模型和原始模型的性能，可以更直观地了解蒸馏技术的效果。例如，DeepSeek-R1-Distill-Llama-70B在AIME 2024上实现了70.0%的Pass@1，在MATH-500上实现了94.5%的Pass@1。这些结果与原始的DeepSeek-R1模型相比，虽然在绝对性能上略有差距，但在计算效率和资源占用方面的优势使其在实际应用中更具价值。</span></p><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">通过这些策略和实验结果，DeepSeek的蒸馏模型在保持高性能的同时，显著降低了计算成本和资源需求，为资源受限场景下的应用提供了强大的支持。</span></p><h1 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;\"><span style=\"line-height: 1.5em; letter-spacing: 0em; display: block; font-size: 14px; font-family: 宋体, SimSun;\">5. 蒸馏技术的挑战</span></h1><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding: 2px 13px; color: rgb(81, 81, 81); line-height: 1.5em; letter-spacing: 0em; background-image: linear-gradient(0deg, rgb(255, 177, 27) 40%, transparent 40%); background-position: 0% 0%; background-size: auto; background-repeat: no-repeat; background-attachment: scroll; background-origin: padding-box; background-clip: border-box; width: auto; height: auto; align-items: unset; border-style: none; border-width: 1px; border-color: rgb(0, 0, 0); border-radius: 0px; box-shadow: none; display: block; flex-direction: unset; float: unset; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">5.1 突破蒸馏的“隐性天花板”</span></h2><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">尽管DeepSeek的蒸馏技术在提升模型性能和降低计算成本方面取得了显著成效，但蒸馏技术仍面临“隐性天花板”的挑战。这一挑战主要体现在学生模型的性能难以超越教师模型的固有能力，限制了模型在新领域或复杂任务中的扩展性。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding-left: 20px; color: rgb(34, 34, 34); line-height: 1.8em; letter-spacing: 0em; border-style: none none none solid; border-width: 1px 1px 1px 3px; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) rgb(255, 177, 27); border-radius: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position: 0% 0%; background-repeat: no-repeat; background-size: auto; box-shadow: none; display: block; flex-direction: unset; float: unset; height: auto; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">学生模型的性能瓶颈</span></h3><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">研究表明，通过蒸馏训练的学生模型总是受到教师模型能力的限制。无论蒸馏过程多么复杂，学生模型都无法真正超越教师模型的性能。例如，在多模态数据处理任务中，学生模型在面对复杂的图像与文本融合任务时，其推理能力往往受限于教师模型的固有模式，难以实现更深层次的创新。</span></p><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-image: none;background-position: 0% 0%;background-size: auto;background-repeat: no-repeat;background-attachment: scroll;background-origin: padding-box;background-clip: border-box;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset;\"><span style=\"padding: 2px 13px; color: rgb(81, 81, 81); line-height: 1.5em; letter-spacing: 0em; background-image: linear-gradient(0deg, rgb(255, 177, 27) 40%, transparent 40%); background-position: 0% 0%; background-size: auto; background-repeat: no-repeat; background-attachment: scroll; background-origin: padding-box; background-clip: border-box; width: auto; height: auto; align-items: unset; border-style: none; border-width: 1px; border-color: rgb(0, 0, 0); border-radius: 0px; box-shadow: none; display: block; flex-direction: unset; float: unset; justify-content: unset; overflow: unset; text-indent: 0em; text-shadow: none; transform: none; -webkit-box-reflect: unset; font-size: 14px; font-family: 宋体, SimSun;\">7.2 多模态数据的蒸馏挑战</span></h2><p data-tool=\"mdnice编辑器\" style=\"padding-top: 8px;padding-bottom: 8px;color: rgb(58, 58, 58);line-height: 1.8em;letter-spacing: 0em;text-indent: 0em;\"><span style=\"font-size: 14px; font-family: 宋体, SimSun;\">多模态数据的蒸馏是当前蒸馏技术面临的另一大挑战。多模态数据包括图像、文本、语音等多种模态，其复杂性和多样性使得蒸馏过程更加困难。</span></p><h3 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0% 0%;background-repeat: no-repeat;background-size: auto;border-style: none;border-width: 1px;border-color: rgb(0, 0, 0);border-radius: 0px;box-shadow: none;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow: unset;text-shadow", "CreateTime": "1738653020", "UpdateTime": "0", "Table": [{"Level1": {"ID": "3352", "Name": "云平台", "ZSCode": "0", "FirstShelveTime": "1738724202", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "600666", "IsZz": "2", "IsHot": "1", "Reason": "2025年2月6日公众号：丝路新云平台正式上线DeepSeek-R1系列模型", "FirstShelveTime": "1739268776", "UpdateCacheTime": "1739268776", "prod_name": "奥瑞德  ", "Hot": 14542}, {"StockID": "002583", "IsZz": "2", "IsHot": "0", "Reason": "公司“情指行一体化实战平台”深度融合国产大模型DeepSeek全面开启智能化新时代，为公安实战指挥体系注入新质战斗力。", "FirstShelveTime": "1740380186", "UpdateCacheTime": "1740380186", "prod_name": "海能达", "Hot": 5080}, {"StockID": "002229", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日全资子公司英博云官微：英博云智算服务平台凭借前沿技术与创新理念，成功实现DeepSeek V3 &amp; R1 的深度部署与推理优化", "FirstShelveTime": "1739770265", "UpdateCacheTime": "1739770265", "prod_name": "鸿博股份", "Hot": 4639}, {"StockID": "000158", "IsZz": "2", "IsHot": "0", "Reason": "常山云数据中心成功完成DeepSeek-R1模型本地化部署并开放使用，更好地服务于政务、金融、医疗、教育等各行业领域，推动产业转型升级，赋能数字经济高质量发展", "FirstShelveTime": "1739958043", "UpdateCacheTime": "1739958043", "prod_name": "常山北明", "Hot": 2844}, {"StockID": "300846", "IsZz": "2", "IsHot": "0", "Reason": "首都在线云平台在DeepSeek-R1推理能力的基础上，通过蒸馏技术使得模型更小、推理更快、资源消耗更少，方便部署在移动设备或边缘设备上", "FirstShelveTime": "1738890320", "UpdateCacheTime": "1738890320", "prod_name": "首都在线", "Hot": 2470}, {"StockID": "002065", "IsZz": "2", "IsHot": "0", "Reason": "2025年02月07日官微：东华云联合GPU知名独角兽企业燧原科技加速推进DeepSeek全量模型高效适配", "FirstShelveTime": "1739681288", "UpdateCacheTime": "1739681288", "prod_name": "东华软件", "Hot": 2454}, {"StockID": "688316", "IsZz": "2", "IsHot": "0", "Reason": "青云科技2025年2月4日官微，青云科技旗下 AI 算力云服务——基石智算CoresHub 正式上线 DeepSeek-R1 系列模型，限时免费", "FirstShelveTime": "1738673422", "UpdateCacheTime": "1738673422", "prod_name": "青云科技", "Hot": 2207}, {"StockID": "000034", "IsZz": "2", "IsHot": "0", "Reason": "神州数码将DeepSeek集成到其自主研发的神州问学平台中，仅需3分钟部署DeepSeek模型", "FirstShelveTime": "1739348507", "UpdateCacheTime": "1739348507", "prod_name": "神州数码", "Hot": 2049}, {"StockID": "000938", "IsZz": "2", "IsHot": "0", "Reason": "子公司自研一站式大模型服务平台软件-灵犀使能平台（LinSeer Hub）已经实现对DeepSeek V3/R1模型的纳管和上架", "FirstShelveTime": "1739080672", "UpdateCacheTime": "1739080672", "prod_name": "紫光股份", "Hot": 1660}, {"StockID": "688158", "IsZz": "2", "IsHot": "0", "Reason": "优刻得云平台已上线了DeepSeek Janus-Pro-7B大模型镜像。按相关操作即可在云平台上部署和调用模型", "FirstShelveTime": "1738723608", "UpdateCacheTime": "1738723608", "prod_name": "优刻得  ", "Hot": 1486}, {"StockID": "300895", "IsZz": "2", "IsHot": "0", "Reason": "铜牛信息国资云平台正式上线DeepSeek-R1系列模型并提供私有化部署方案", "FirstShelveTime": "1739174800", "UpdateCacheTime": "1739174800", "prod_name": "铜牛信息", "Hot": 1270}, {"StockID": "600050", "IsZz": "2", "IsHot": "0", "Reason": "联通云发布与星罗平台融合的 DeepSeek-R1 系列模型", "FirstShelveTime": "1738770543", "UpdateCacheTime": "1738770543", "prod_name": "XD中国联", "Hot": 964}, {"StockID": "300603", "IsZz": "2", "IsHot": "0", "Reason": "立昂领算云平台已上架最新版DeepSeek-R1660B推理模型，同时支持 DeepSeek-V3和DeepSeek-R1-Distill的多个版本模型预训练镜像", "FirstShelveTime": "1739174394", "UpdateCacheTime": "1739174394", "prod_name": "立昂技术", "Hot": 883}, {"StockID": "839493", "IsZz": "2", "IsHot": "0", "Reason": "并行科技智算云平台已部署DeepSeek模型，用户可根据使用习惯随意选择。", "FirstShelveTime": "1738893971", "UpdateCacheTime": "1738893971", "prod_name": "并行科技", "Hot": 877}, {"StockID": "300113", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日公众号：顺网算力云全面拥抱DeepSeek", "FirstShelveTime": "1739348171", "UpdateCacheTime": "1739348171", "prod_name": "顺网科技", "Hot": 781}, {"StockID": "300017", "IsZz": "2", "IsHot": "0", "Reason": "爱捷云算力云平台已上架最新版DeepSeek-R1 660B 推理模型", "FirstShelveTime": "1739078709", "UpdateCacheTime": "1739078709", "prod_name": "网宿科技", "Hot": 746}, {"StockID": "601728", "IsZz": "2", "IsHot": "0", "Reason": "中国电信天翼云已经上架DeepSeek", "FirstShelveTime": "1738724253", "UpdateCacheTime": "1738724253", "prod_name": "中国电信", "Hot": 699}, {"StockID": "600941", "IsZz": "2", "IsHot": "0", "Reason": "移动云全面上线DeepSeek，实现全版本覆盖、全尺寸适配、全功能畅用", "FirstShelveTime": "1738770440", "UpdateCacheTime": "1738770440", "prod_name": "中国移动", "Hot": 640}, {"StockID": "688227", "IsZz": "2", "IsHot": "0", "Reason": "品高AI大模型融合平台--AISTACK在2024年便开始支持DeepSeek的混合专家模型(MoE)，可用AISTACK本地部署DeepSeek R1，并为其推理服务实现了加速", "FirstShelveTime": "1739076655", "UpdateCacheTime": "1739076655", "prod_name": "品高股份", "Hot": 489}]}, "Level2": []}, {"Level1": {"ID": "3346", "Name": "端侧应用商", "ZSCode": "0", "FirstShelveTime": "1738638513", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "600839", "IsZz": "2", "IsHot": "0", "Reason": "长虹官宣其AI TV正式接入DeepSeek，并支持在“深度思考（满血R1）”和“快速响应”两个版本间自由切换", "FirstShelveTime": "1739423143", "UpdateCacheTime": "1739423143", "prod_name": "四川长虹", "Hot": 3315}, {"StockID": "002881", "IsZz": "2", "IsHot": "0", "Reason": "美格智能凭借其高算力AI模组矩阵与端侧大模型部署经验，正加速开发DeepSeek-R1模型在端侧落地应用及端云结合整体方案", "FirstShelveTime": "1738638240", "UpdateCacheTime": "1738638240", "prod_name": "美格智能", "Hot": 2981}, {"StockID": "002413", "IsZz": "2", "IsHot": "0", "Reason": "子公司理工雷科智算技术团队成功完成DeepSeek R1模型与旗下“山海”边缘智算模组、“雁门”智算服务器等产品的适配工作", "FirstShelveTime": "1739269218", "UpdateCacheTime": "1739269218", "prod_name": "雷科防务", "Hot": 2955}, {"StockID": "300638", "IsZz": "2", "IsHot": "0", "Reason": "广和通AI玩具解决方案通过火山引擎接入DeepSeek开源模型，满足AI玩具场景在多模态交互、自然语言处理、情感分析、教育功能等方面的需求", "FirstShelveTime": "1739267833", "UpdateCacheTime": "1739267833", "prod_name": "广和通", "Hot": 2451}, {"StockID": "301236", "IsZz": "2", "IsHot": "0", "Reason": "机械革命AI PC产品线也正式接入DeepSeek，推动端侧AI的应用普及", "FirstShelveTime": "1739171920", "UpdateCacheTime": "1739171920", "prod_name": "软通动力", "Hot": 1642}, {"StockID": "688590", "IsZz": "2", "IsHot": "0", "Reason": "DeepSeek模型已经成功接入新致软件新知平台保险、司法、汽车等重点行业的智能机器人系统中", "FirstShelveTime": "1738767366", "UpdateCacheTime": "1738767366", "prod_name": "新致软件", "Hot": 1582}, {"StockID": "301171", "IsZz": "2", "IsHot": "0", "Reason": "易点天下旗下公众号发文称，AI技术中台已完成DeepSeek-R1私有化部署，旗下KreadoAI、数眼智能、zMaticoo ADX等多款产品将融合大模型相关能力", "FirstShelveTime": "1738737155", "UpdateCacheTime": "1738737155", "prod_name": "易点天下", "Hot": 843}, {"StockID": "688343", "IsZz": "2", "IsHot": "0", "Reason": "云天励飞芯片团队完成 DeepEdge10 “算力积木”芯片平台与DeepSeek的多模型适配", "FirstShelveTime": "1738770499", "UpdateCacheTime": "1738770499", "prod_name": "云天励飞", "Hot": 826}, {"StockID": "688620", "IsZz": "2", "IsHot": "0", "Reason": "公司AK39系列芯片对接了豆包、通义千问、文心一言、DeepSeek、Kimi等多家大语言模型", "FirstShelveTime": "1738638240", "UpdateCacheTime": "1738638240", "prod_name": "安凯微  ", "Hot": 778}, {"StockID": "688365", "IsZz": "2", "IsHot": "0", "Reason": "官微称，绫智（快麦小智推出的基于大模型智能体产品）的多项业务场景和功能已经接入并在支持DeepSeek各个版本", "FirstShelveTime": "1738804975", "UpdateCacheTime": "1738804975", "prod_name": "光云科技", "Hot": 530}]}, "Level2": []}, {"Level1": {"ID": "3343", "Name": "部署适配商", "ZSCode": "0", "FirstShelveTime": "1738638022", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "002261", "IsZz": "2", "IsHot": "0", "Reason": "兆瀚AI算力产品基于鲲鹏+昇腾“处理器已完成DeepSeek多版本适配，为行业用户提供从训练到推理、从云端到边缘的完整部署能力。", "FirstShelveTime": "1739079353", "UpdateCacheTime": "1739079353", "prod_name": "拓维信息", "Hot": 4208}, {"StockID": "000818", "IsZz": "2", "IsHot": "0", "Reason": "超擎数智以自主研发的AI Engine人工智能开发平台、NVAIE及定制化AI软件产品，全面加速用户人工智能应用的开发和部署，帮助企业和个人快速完成DeepSeek私有化部署落地", "FirstShelveTime": "1739950256", "UpdateCacheTime": "1739950256", "prod_name": "航锦科技", "Hot": 3931}, {"StockID": "600126", "IsZz": "2", "IsHot": "0", "Reason": "子公司浙江省数据管理有限公司成功完成DeepSeek-R1的适配并实现DeepSeek-R1 70B参数及以下全部蒸馏模型的部署。5号公告称与DeepSeek系统的开发、应用等核心技术无关", "FirstShelveTime": "1738638262", "UpdateCacheTime": "1738638262", "prod_name": "杭钢股份", "Hot": 3251}, {"StockID": "688041", "IsZz": "2", "IsHot": "0", "Reason": "海光信息技术团队成功完成DeepSeek V3和R1模型与海光DCU（深度计算单元）的国产化适配，并正式上线", "FirstShelveTime": "1738638262", "UpdateCacheTime": "1738638262", "prod_name": "海光信息", "Hot": 2741}, {"StockID": "603322", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月2日，公司联手开源中国、沐曦、米塔碳等合作伙伴完成DeepSeek-R1 70B部署", "FirstShelveTime": "1738638262", "UpdateCacheTime": "1738638262", "prod_name": "超讯通信", "Hot": 1451}, {"StockID": "600498", "IsZz": "2", "IsHot": "0", "Reason": "2025年02月07日官微：长江计算自主研发的G440K V2服务器，现已实现DeepSeek系列模型的推理适配和优化", "FirstShelveTime": "1739347662", "UpdateCacheTime": "1739347662", "prod_name": "烽火通信", "Hot": 1297}, {"StockID": "603220", "IsZz": "2", "IsHot": "0", "Reason": "中贝通信合肥智算中心已完成DeepSeek全系列版本部署自主研发的DeepSeek-ZB训推一体机正加速推向全国市场。", "FirstShelveTime": "1741057483", "UpdateCacheTime": "1741057483", "prod_name": "中贝通信", "Hot": 1121}, {"StockID": "603859", "IsZz": "2", "IsHot": "0", "Reason": "能科科技在互动平台表示，公司构建的Al Agent能与DeepSeek进行适配。公司构建的Al Agent能够适配不同的开源和闭源大模型，形成多个行业的AI Agent。", "FirstShelveTime": "1740548935", "UpdateCacheTime": "1740548935", "prod_name": "能科科技", "Hot": 841}]}, "Level2": []}, {"Level1": {"ID": "3452", "Name": "昇腾一体机", "ZSCode": "0", "FirstShelveTime": "1740903435", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "600410", "IsZz": "2", "IsHot": "0", "Reason": "官微称，将推出全栈国产智能客服解决方案一体机，采用昇腾算力+华为AICC软件+DeepSeek大模型+华胜天成智能客服应用软件", "FirstShelveTime": "1740904696", "UpdateCacheTime": "1740904696", "prod_name": "华胜天成", "Hot": 4908}, {"StockID": "002261", "IsZz": "2", "IsHot": "0", "Reason": "公司与整数智能联合，共同推出业内首款搭载DeepSeek全系列模型的智能数据标注一体机，该一体机产品基于“昇腾+鲲鹏”打造的“兆瀚”AI算力硬件", "FirstShelveTime": "1740310048", "UpdateCacheTime": "1740310048", "prod_name": "拓维信息", "Hot": 4208}, {"StockID": "603496", "IsZz": "2", "IsHot": "0", "Reason": "公司推出的昇腾Deepseek一体机分为训推一体机与推理一体机两种类型", "FirstShelveTime": "1740720937", "UpdateCacheTime": "1740720937", "prod_name": "恒为科技", "Hot": 3113}, {"StockID": "300377", "IsZz": "2", "IsHot": "0", "Reason": "官微称，赢时胜AI一体机获昇腾认证，是DeepSeek与华为合作主推的硬件平台", "FirstShelveTime": "1739424365", "UpdateCacheTime": "1739424365", "prod_name": "赢时胜", "Hot": 2640}, {"StockID": "000034", "IsZz": "2", "IsHot": "0", "Reason": "神州鲲泰问学一体机DeepSeek版在第三届北京人工智能产业创新发展大会上正式发布，并提供基于昇腾的智算服务", "FirstShelveTime": "1740904236", "UpdateCacheTime": "1740904236", "prod_name": "神州数码", "Hot": 2049}, {"StockID": "301236", "IsZz": "2", "IsHot": "0", "Reason": "软通动力推出 “DeepSeek 应用方案一体机”，其提供了昇腾、NVIDIA 等不同算力配置", "FirstShelveTime": "1740904134", "UpdateCacheTime": "1740904134", "prod_name": "软通动力", "Hot": 1642}, {"StockID": "002236", "IsZz": "2", "IsHot": "0", "Reason": "根据子公司华启智慧2025年3月22日官微：此次发布的DeepSeek一体机基于鲲鹏+昇腾处理器，可满足DeepSeek大模型从7B到671B不同参数规模版本的灵活部署", "FirstShelveTime": "1743053097", "UpdateCacheTime": "1743053097", "prod_name": "大华股份", "Hot": 1381}, {"StockID": "002230", "IsZz": "2", "IsHot": "0", "Reason": "科大讯飞发布“星火+DeepSeek双引擎一体机”，多款搭载昇腾卡", "FirstShelveTime": "1740309938", "UpdateCacheTime": "1740309938", "prod_name": "科大讯飞", "Hot": 1239}, {"StockID": "301085", "IsZz": "2", "IsHot": "0", "Reason": "公司新推出的搭载昇腾芯片和DeepSeek系列模型的桌面级智能一体机D-BOXPro，该产品目前正处于市场推广初期，尚未产生相关收入", "FirstShelveTime": "1739435505", "UpdateCacheTime": "1739435505", "prod_name": "亚康股份", "Hot": 909}, {"StockID": "688152", "IsZz": "2", "IsHot": "0", "Reason": "公司推出麒麟信安全国产化智算一体机，该一体机产品采用国产鲲鹏920架构服务器+华为昇腾AI卡，搭载麒麟信安安全操作系统，内置DeepSeek大模型", "FirstShelveTime": "1740310203", "UpdateCacheTime": "1740310203", "prod_name": "麒麟信安", "Hot": 677}, {"StockID": "688228", "IsZz": "2", "IsHot": "0", "Reason": "官微称，开普云推出升级版AI一体机，昇腾算力加持并深度适配DeepSeek开普云", "FirstShelveTime": "1740903921", "UpdateCacheTime": "1740903921", "prod_name": "开普云  ", "Hot": 645}]}, "Level2": []}, {"Level1": {"ID": "3368", "Name": "一体机", "ZSCode": "0", "FirstShelveTime": "1738897033", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "688047", "IsZz": "2", "IsHot": "2", "Reason": "龙芯中科成功发布基于DeepSeek大模型的软硬全栈推理一体机", "FirstShelveTime": "1740453687", "UpdateCacheTime": "1740453687", "prod_name": "龙芯中科", "Hot": 26613}, {"StockID": "603300", "IsZz": "2", "IsHot": "0", "Reason": "公众号：华铁DeepSeek大模型一体机——海南华铁联合新华三面向企业级市场的人工智能基础设施解决方案，近日成功落地。", "FirstShelveTime": "1741843046", "UpdateCacheTime": "1741843046", "prod_name": "海南华铁", "Hot": 11814}, {"StockID": "603019", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月14日公众号：曙光云发布全国产DeepSeek大模型超融合一体机", "FirstShelveTime": "1739682394", "UpdateCacheTime": "1739682394", "prod_name": "中科曙光", "Hot": 3176}, {"StockID": "000066", "IsZz": "2", "IsHot": "0", "Reason": "长城擎天GF7280 V5 AI训推一体机已全面适配DeepSeek R1系列模型", "FirstShelveTime": "1739076469", "UpdateCacheTime": "1739076469", "prod_name": "中国长城", "Hot": 2954}, {"StockID": "000977", "IsZz": "2", "IsHot": "0", "Reason": "浪潮云率先发布671B DeepSeek大模型一体机解决方案", "FirstShelveTime": "1738897049", "UpdateCacheTime": "1738897049", "prod_name": "浪潮信息", "Hot": 2838}, {"StockID": "000063", "IsZz": "2", "IsHot": "0", "Reason": "公司推出AiCube一体机，已全面适配DeepSeek V3 &amp; R1，仅需数小时即可完成DeepSeek R1全系列蒸馏模型的适配。", "FirstShelveTime": "1739095881", "UpdateCacheTime": "1739095881", "prod_name": "中兴通讯", "Hot": 2783}, {"StockID": "002335", "IsZz": "2", "IsHot": "0", "Reason": "科华数据联合希姆计算深耕政务场景，打造DeepSeek加持的政务智能体一体机", "FirstShelveTime": "1739179895", "UpdateCacheTime": "1739179895", "prod_name": "科华数据", "Hot": 2713}, {"StockID": "002929", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月10日公众号：DeepSeek加持！润建股份&amp;希姆计算打造全国产政务算力一体机", "FirstShelveTime": "1739268034", "UpdateCacheTime": "1739268034", "prod_name": "润建股份", "Hot": 2669}, {"StockID": "300846", "IsZz": "2", "IsHot": "0", "Reason": "首都在线DeepSeek一体机深度融合软硬件技术，搭载自研的DeepSeek R1/V3系列大模型，覆盖从边缘端到云端的多样化场景需求", "FirstShelveTime": "1740645554", "UpdateCacheTime": "1740645554", "prod_name": "首都在线", "Hot": 2470}, {"StockID": "001339", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日公众号：智微智能DeepSeek算力一体机，释放AI算力巅峰，加速智能未来", "FirstShelveTime": "1739522827", "UpdateCacheTime": "1739522827", "prod_name": "智微智能", "Hot": 2382}, {"StockID": "600353", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月19日四川省上市公司协会公众号：旭光电子发布DeepSeek边缘一体机， 强势赋能国产智能边算", "FirstShelveTime": "1739956754", "UpdateCacheTime": "1739956754", "prod_name": "旭光电子", "Hot": 2281}, {"StockID": "688316", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月24日青云科技发布 DeepSeek 一体机，开箱即用，AI 转型一键启动", "FirstShelveTime": "1740898308", "UpdateCacheTime": "1740898308", "prod_name": "青云科技", "Hot": 2207}, {"StockID": "002290", "IsZz": "2", "IsHot": "0", "Reason": "子公司海曦技术2025年2月28日官微：单机也可跑DeepSeek 671B满血大模型--海曦大模型一体机推新品", "FirstShelveTime": "1741170471", "UpdateCacheTime": "1741170471", "prod_name": "禾盛新材", "Hot": 1705}, {"StockID": "300657", "IsZz": "2", "IsHot": "0", "Reason": "燧弘华创的国产算力一体机以全栈自主可控为核心，深度融合国产GPU硬件与深度优化软件栈，产品支持从1.5B到千亿级模型的DeepSeek全版本覆盖", "FirstShelveTime": "1739957358", "UpdateCacheTime": "1739957358", "prod_name": "弘信电子", "Hot": 1674}, {"StockID": "000938", "IsZz": "2", "IsHot": "0", "Reason": "互动易称核心子公司新华三将根据DeepSeek在行业侧的应用情况，推出训推一体机解决方案", "FirstShelveTime": "1739080700", "UpdateCacheTime": "1739080700", "prod_name": "紫光股份", "Hot": 1660}, {"StockID": "301236", "IsZz": "2", "IsHot": "0", "Reason": "软通动力全线产品接入和支持DeepSeek，并重磅推出 “DeepSeek 应用方案一体机”系列产品", "FirstShelveTime": "1740309887", "UpdateCacheTime": "1740309887", "prod_name": "软通动力", "Hot": 1642}, {"StockID": "688590", "IsZz": "2", "IsHot": "0", "Reason": "新致软件联合中科海光，正式发布新致信创一体机——以海光K100 GPU服务器为算力基石，深度融合新致新知人工智能平台与DeepSeek系列大模型", "FirstShelveTime": "1740309801", "UpdateCacheTime": "1740309801", "prod_name": "新致软件", "Hot": 1582}, {"StockID": "603636", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月28日官微：发布通用DeepSeek＋智能体一体机系列产品，助力AI大模型价值释放", "FirstShelveTime": "1740898317", "UpdateCacheTime": "1740898317", "prod_name": "南威软件", "Hot": 1539}, {"StockID": "688158", "IsZz": "2", "IsHot": "0", "Reason": "优刻得发布预置DeepSeek满血版大模型一体机", "FirstShelveTime": "1739426797", "UpdateCacheTime": "1739426797", "prod_name": "优刻得  ", "Hot": 1486}, {"StockID": "603322", "IsZz": "2", "IsHot": "0", "Reason": "公司是国产 GPU 品牌 “沐曦” 的特定行业总代理商，且注册了 “元醒” 算力设备商标，产品有“元醒曦云 C500-P PCIe 训推一体服务器”", "FirstShelveTime": "1740645876", "UpdateCacheTime": "1740645876", "prod_name": "超讯通信", "Hot": 1451}, {"StockID": "603660", "IsZz": "2", "IsHot": "0", "Reason": "公司具备为客户提供基于DeepSeek不同参数的一体机产品和行业定制需要", "FirstShelveTime": "1740903640", "UpdateCacheTime": "1740903640", "prod_name": "苏州科达", "Hot": 1316}, {"StockID": "000032", "IsZz": "2", "IsHot": "0", "Reason": "公司所属的中国电子云推出了为DeepSeek优化的推理一体机", "FirstShelveTime": "1739095881", "UpdateCacheTime": "1739095881", "prod_name": "深桑达Ａ", "Hot": 1275}, {"StockID": "300857", "IsZz": "2", "IsHot": "0", "Reason": "协创数据旗下FCloud推出DeepSeek满血版一体机", "FirstShelveTime": "1740310104", "UpdateCacheTime": "1740310104", "prod_name": "协创数据", "Hot": 1273}, {"StockID": "603220", "IsZz": "2", "IsHot": "0", "Reason": "2025年3月5日官微：中贝通信已推出多款面向政府、企业用户，搭载DeepSeek R1/V3满血版模型和蒸馏版模型的DeepSeek-ZB训推一体机", "FirstShelveTime": "1741947209", "UpdateCacheTime": "1741947209", "prod_name": "中贝通信", "Hot": 1121}, {"StockID": "002212", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日公众号：发布DeepSeek安全智算一体机", "FirstShelveTime": "1739268535", "UpdateCacheTime": "1739268535", "prod_name": "天融信", "Hot": 966}, {"StockID": "688343", "IsZz": "2", "IsHot": "0", "Reason": "公司与华为联合推出的云天天书大模型训推一体机成功适配DeepSeek，已经在深圳市龙岗区、南山区实现双区部署", "FirstShelveTime": "1739495389", "UpdateCacheTime": "1739495389", "prod_name": "云天励飞", "Hot": 826}, {"StockID": "600271", "IsZz": "2", "IsHot": "0", "Reason": "公司联营企业推出航天联志DeepSeek一体机", "FirstShelveTime": "1739076660", "UpdateCacheTime": "1739076660", "prod_name": "航天信息", "Hot": 700}, {"StockID": "601728", "IsZz": "2", "IsHot": "0", "Reason": "公司推出息壤智算一体机-DeepSeek版", "FirstShelveTime": "1740310272", "UpdateCacheTime": "1740310272", "prod_name": "中国电信", "Hot": 699}, {"StockID": "688039", "IsZz": "2", "IsHot": "0", "Reason": "当虹科技正式发布了全新一代“DeepSeek+BlackEye”多模态大模型一体机", "FirstShelveTime": "1740453705", "UpdateCacheTime": "1740453705", "prod_name": "当虹科技", "Hot": 652}, {"StockID": "600941", "IsZz": "2", "IsHot": "0", "Reason": "公司推出智算一体机-DeepSeek版", "FirstShelveTime": "1740310299", "UpdateCacheTime": "1740310299", "prod_name": "中国移动", "Hot": 640}, {"StockID": "603025", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月26日公司控股子公司兴汉网际正式推出DeepSeeK AI智算一体机全平台系列产品", "FirstShelveTime": "1740895040", "UpdateCacheTime": "1740895040", "prod_name": "大豪科技", "Hot": 570}, {"StockID": "688207", "IsZz": "2", "IsHot": "0", "Reason": "格灵深瞳Deepseek-R1大模型一体机，提供私有化、易运维、高性价比的产品，助力公共安全能源、教育等行业集成商/服务商转型升级", "FirstShelveTime": "1740654139", "UpdateCacheTime": "1740654139", "prod_name": "格灵深瞳", "Hot": 409}]}, "Level2": []}, {"Level1": {"ID": "3342", "Name": "接入商", "ZSCode": "0", "FirstShelveTime": "1738638022", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": []}, "Level2": [{"ID": "3377", "Name": "AIGC", "ZSCode": "801682", "FirstShelveTime": "1739086039", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "000681", "IsZz": "2", "IsHot": "0", "Reason": "公司完成深度求索公司开源大模型DeepSeek-R1的接入与本地化部署，并在多个产品中深度应用其能力", "FirstShelveTime": "1738749866", "UpdateCacheTime": "1738749866", "prod_name": "视觉中国", "Hot": 2358}, {"StockID": "300624", "IsZz": "2", "IsHot": "0", "Reason": "公司已完成深度求索最新推理大模型DeepSeek-R1的深入适配，涵盖旗下视频创意、绘图创意及文档创意软件业务多款产品", "FirstShelveTime": "1738670595", "UpdateCacheTime": "1738670595", "prod_name": "万兴科技", "Hot": 1061}, {"StockID": "600640", "IsZz": "2", "IsHot": "0", "Reason": "025年2月11日互动易：公司的“国脉文化AIGC+生态合作平台”产品已接入DeepSeek等大模型。该平台是一款集AI企业治理、AIGC行业应用场景及算力资源调度的综合服务平台，面向多个行业场景，平台由三大模块组成：国脉云治、国脉云生和国脉云擎，平台自发布以来已为多家客户提供一站式的AIGC应用技术支撑、算力训练、推理等服务。", "FirstShelveTime": "1740540439", "UpdateCacheTime": "1740540439", "prod_name": "国脉文化", "Hot": 952}, {"StockID": "300634", "IsZz": "2", "IsHot": "0", "Reason": "公司Rich AIBox已正式接入DeepSeek-V3、DeepSeek-R1大模型，进一步提升了垂直领域大模型能力，实现逻辑推理、内容生成、图片理解等多方面的优化。", "FirstShelveTime": "1738838584", "UpdateCacheTime": "1738838584", "prod_name": "彩讯股份", "Hot": 799}]}, {"ID": "3375", "Name": "智能体", "ZSCode": "0", "FirstShelveTime": "1739085159", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "003040", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月12日微信公众号发布，公司将DeepSeek-V3、DeepSeek-R1在线模型接入公司咨询问答数智人、辅助办事机器人及业务经办数字员工等AI智能体平台", "FirstShelveTime": "1739435408", "UpdateCacheTime": "1739435408", "prod_name": "楚天龙", "Hot": 21503}, {"StockID": "600410", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月9日公众号：2月6日，公司智能客服系列产品正式完成与DeepSeek模型各版本适配", "FirstShelveTime": "1739172122", "UpdateCacheTime": "1739172122", "prod_name": "华胜天成", "Hot": 4908}, {"StockID": "600839", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月13日，长虹官宣其AITV正式接入DeepSeek，并支持在“深度思考(满血R1)”和“快速响应”两个版本间自由切换，这意味着行业首个能深度思考的 AITV智能体已全面上线", "FirstShelveTime": "1739435269", "UpdateCacheTime": "1739435269", "prod_name": "四川长虹", "Hot": 3315}, {"StockID": "600186", "IsZz": "2", "IsHot": "0", "Reason": "官微称，莲花紫星已在自有算力上成功部署DeepSeek R1大模型，并全面支持智能体调用", "FirstShelveTime": "1738804641", "UpdateCacheTime": "1738804641", "prod_name": "莲花控股", "Hot": 2113}, {"StockID": "300378", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月9日公众号：DeepSeek大模型已全面集成至鼎捷IndepthAl智能体平台及鼎捷全线智能应用", "FirstShelveTime": "1739174960", "UpdateCacheTime": "1739174960", "prod_name": "鼎捷数智", "Hot": 1062}, {"StockID": "688327", "IsZz": "2", "IsHot": "0", "Reason": "公司持续致力于通过模型蒸馏和工程优化降低模型成本，提升技术效率，提供自研模型和适配各类第三方模型（包括但不限于DeepSeek)的Agent应用及服务", "FirstShelveTime": "1739150689", "UpdateCacheTime": "1739150689", "prod_name": "云从科技", "Hot": 690}, {"StockID": "300168", "IsZz": "2", "IsHot": "0", "Reason": "互动易称公司目前正在将DeepSeek集成到公司数字智脑智能体、大模型服务支撑平台等产品中", "FirstShelveTime": "1739087030", "UpdateCacheTime": "1739087030", "prod_name": "万达信息", "Hot": 556}, {"StockID": "603766", "IsZz": "2", "IsHot": "0", "Reason": "隆鑫通用旗下无极机车“极屿OS”全场景生态AI车机系统（骑行助理）与DeepSeek大模型实现深度融合", "FirstShelveTime": "1739184811", "UpdateCacheTime": "1739184811", "prod_name": "隆鑫通用", "Hot": 553}, {"StockID": "002110", "IsZz": "2", "IsHot": "0", "Reason": "互动平台表示，公司将根据实际情况，积极学习运用DeepSeek等相关技术，强化推进数字化、智能化应用，推动公司数字化转型。", "FirstShelveTime": "1739184938", "UpdateCacheTime": "1739184938", "prod_name": "三钢闽光", "Hot": 486}, {"StockID": "688058", "IsZz": "2", "IsHot": "0", "Reason": "公司相关产品目前已接入DeepSeek，助力行业用户打造面向垂直领域的AI Agent应用", "FirstShelveTime": "1738805267", "UpdateCacheTime": "1738805267", "prod_name": "宝兰德  ", "Hot": 412}, {"StockID": "836263", "IsZz": "2", "IsHot": "0", "Reason": "同步接入的DeepSeek“满血版”与“星曜之眼”合力打造智能体应用赋能集团日常业务，覆盖了工艺专家咨询、合同审查、造价评估、标书编撰、AI报表等业务场景", "FirstShelveTime": "1741334546", "UpdateCacheTime": "1741334546", "prod_name": "中航泰达", "Hot": 361}]}, {"ID": "3378", "Name": "大模型融合", "ZSCode": "0", "FirstShelveTime": "1739086299", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "600570", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月10日公众号：恒生大模型系列应用全面接入DeepSeek主流模型(DeepSeek-V3/DeepSeek-R1)", "FirstShelveTime": "1739173961", "UpdateCacheTime": "1739173961", "prod_name": "恒生电子", "Hot": 7157}, {"StockID": "300872", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月10日公众号：旗下产融大模型正式集成国产大模型DeepSeek，完成基于DeepSeek R1的大模型服务平台升级，推出DeepSeek版包含产融分析和拓客智能体的产融大模型产品", "FirstShelveTime": "1739349973", "UpdateCacheTime": "1739349973", "prod_name": "天阳科技", "Hot": 5772}, {"StockID": "603017", "IsZz": "2", "IsHot": "0", "Reason": "2024年半年报：公司持有园测信息科技股份有限公司21%股份。据园测信息2025年2月13日官微：当前槿墨AI已全面接入De­e­p­S­e­ek主流模型，包括De­e­p­S­e­ek-V3和De­e­p­S­e­ek-R1，将充分发挥De­e­p­S­e­ek “低成本+高性能+高开放度”的应用优势，实现大模型系列应用能力的全面跃升，为地理信息和社会治理领域提供更智能、更高效、更低成本的智能解决方案。", "FirstShelveTime": "1740540388", "UpdateCacheTime": "1740540388", "prod_name": "中衡设计", "Hot": 5569}, {"StockID": "002123", "IsZz": "2", "IsHot": "0", "Reason": "公司将DeepSeek大模型深度集成至多源AI调度引擎“天慧智汇台2.0”", "FirstShelveTime": "1738670550", "UpdateCacheTime": "1738670550", "prod_name": "梦网科技", "Hot": 3747}, {"StockID": "002354", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日公众号：天娱数自研基座大模型天星大模型现已接入DeepSeek", "FirstShelveTime": "1739409069", "UpdateCacheTime": "1739409069", "prod_name": "天娱数科", "Hot": 3190}, {"StockID": "600797", "IsZz": "2", "IsHot": "0", "Reason": "与浙大共研的 OpenBuddy 开源大模型于2023年发布首款基于DeepSeek基座的跨语言模型", "FirstShelveTime": "1738720070", "UpdateCacheTime": "1738720070", "prod_name": "浙大网新", "Hot": 2752}, {"StockID": "002335", "IsZz": "2", "IsHot": "0", "Reason": "公司携手壁仞科技顺利部署DeepSeek-R1-Distil蒸馏模型，并已上线Gitee AI，依然面向全体开发者免费使用", "FirstShelveTime": "1739154403", "UpdateCacheTime": "1739154403", "prod_name": "科华数据", "Hot": 2713}, {"StockID": "300377", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月6日官微：赢时胜大模型技术栈与DeepSeek主力模型全面对接", "FirstShelveTime": "1739172415", "UpdateCacheTime": "1739172415", "prod_name": "赢时胜", "Hot": 2640}, {"StockID": "301302", "IsZz": "2", "IsHot": "0", "Reason": "华如科技基于军事“仿真+AI”技术积累，结合DeepSeek等开源大模型，推出XSimVerse®军事大模型", "FirstShelveTime": "1740286914", "UpdateCacheTime": "1740286914", "prod_name": "华如科技", "Hot": 1968}, {"StockID": "300166", "IsZz": "2", "IsHot": "0", "Reason": "东方国信自主研发的幕僚智数、大模型训推平台等多款核心产品成功完成与DeepSeek-R1系列大模型的深度集成", "FirstShelveTime": "1739092791", "UpdateCacheTime": "1739092791", "prod_name": "东方国信", "Hot": 1874}, {"StockID": "300418", "IsZz": "2", "IsHot": "0", "Reason": "昆仑万维旗下「天工AI」正式推出PC版重大更新——上线“DeepSeek R1 + 联网搜索”功能，旨在解决了DeepSeek联网功能无法使用的问题", "FirstShelveTime": "1739086540", "UpdateCacheTime": "1739086540", "prod_name": "昆仑万维", "Hot": 1857}, {"StockID": "000988", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月28日官微：DeepSeek+共工大模型，双擎驱动水利水电智能运维效率革命", "FirstShelveTime": "1740894081", "UpdateCacheTime": "1740894081", "prod_name": "华工科技", "Hot": 1847}, {"StockID": "603636", "IsZz": "2", "IsHot": "0", "Reason": "公司推出的深海大模型和白泽政务大模型均已接入DeepSeek 相关版本并进行私有化部署，并利用模型蒸馏技术优化模型性能", "FirstShelveTime": "1738890549", "UpdateCacheTime": "1738890549", "prod_name": "南威软件", "Hot": 1539}, {"StockID": "000625", "IsZz": "2", "IsHot": "0", "Reason": "DeepSeek接入长安天枢大模型，首发搭载启源E07", "FirstShelveTime": "1739172337", "UpdateCacheTime": "1739172337", "prod_name": "长安汽车", "Hot": 1248}, {"StockID": "002777", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月6日互动易：公司通过对DeepSeek-R1的接入和融合其相关能力，实现了公司银海“闻语”大模型训练质效的大幅度提升", "FirstShelveTime": "1739682288", "UpdateCacheTime": "1739682288", "prod_name": "久远银海", "Hot": 1142}, {"StockID": "301248", "IsZz": "2", "IsHot": "0", "Reason": "旗下常青云高性能AI超融合平台完成与国产高性能推理模型DeepSeek系列大模型的深度适配优化，平台支持DeepSeekV3、推理模型R1、多模态模型Janus Pro等三大模型", "FirstShelveTime": "1739171544", "UpdateCacheTime": "1739171544", "prod_name": "杰创智能", "Hot": 966}, {"StockID": "600104", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日公众号：上汽通用汽车成为首家将推理大模型深度融入智舱的合资车企DeepSeek-R1", "FirstShelveTime": "1739347857", "UpdateCacheTime": "1739347857", "prod_name": "上汽集团", "Hot": 842}, {"StockID": "600728", "IsZz": "2", "IsHot": "0", "Reason": "佳都知行交通大模型与DeepSeek优化融合，加速交通数智化升级", "FirstShelveTime": "1740999001", "UpdateCacheTime": "1740999001", "prod_name": "佳都科技", "Hot": 795}, {"StockID": "301095", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月10日公众号：旗下SemiMind半导体大模型平台正式接入DeepSeek-R1大模型，深度协同，让SemiMind更“懂”半导体", "FirstShelveTime": "1739267918", "UpdateCacheTime": "1739267918", "prod_name": "广立微", "Hot": 792}, {"StockID": "600845", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月14日官微：近期，天行IDE又快速与本地部署的DeepSeek大模型实现了深度集成。", "FirstShelveTime": "1740125942", "UpdateCacheTime": "1740125942", "prod_name": "宝信软件", "Hot": 652}, {"StockID": "688039", "IsZz": "2", "IsHot": "0", "Reason": "当虹科技BlackEye多模态视听大模型完成DeepSeek深度融合", "FirstShelveTime": "1738736265", "UpdateCacheTime": "1738736265", "prod_name": "当虹科技", "Hot": 652}, {"StockID": "300525", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月10日微信公众号，博思软件旗下博智星、数字凭证基座、阳光公采大模型等系列产品完成与“国运级AI”DeepSeek大模型的接入测试，共同开启数据智能新阶段，持续升级AI智能体产品，融合AI提升政企服务价值", "FirstShelveTime": "1739870219", "UpdateCacheTime": "1739870219", "prod_name": "博思软件", "Hot": 624}, {"StockID": "300168", "IsZz": "2", "IsHot": "0", "Reason": "互动易称公司目前正在将DeepSeek集成到公司数字智脑智能体、大模型服务支撑平台等产品中", "FirstShelveTime": "1739087056", "UpdateCacheTime": "1739087056", "prod_name": "万达信息", "Hot": 556}, {"StockID": "300275", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月17日投资者互动平台：公司已完成DeepSeek-R1本地化部署，并将DeepSeek与公司自研SPG矿山安全大模型深度集成，实现公司全线软件产品接入DeepSeek", "FirstShelveTime": "1740043881", "UpdateCacheTime": "1740043881", "prod_name": "梅安森", "Hot": 539}, {"StockID": "688777", "IsZz": "2", "IsHot": "0", "Reason": "中控技术依托DeepSeek打造“工业BA超级智能系统”", "FirstShelveTime": "1739164079", "UpdateCacheTime": "1739164079", "prod_name": "中控技术", "Hot": 400}, {"StockID": "688367", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日公众号：工大高科盛视F1.0工业视觉大模型是面向工业场景的AI创新平台，目前已完成对接DeepSeek-R1、DeepSeek-V3等系列大模型", "FirstShelveTime": "1739350357", "UpdateCacheTime": "1739350357", "prod_name": "工大高科", "Hot": 331}]}, {"ID": "3371", "Name": "券商", "ZSCode": "0", "FirstShelveTime": "1739084485", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "002670", "IsZz": "2", "IsHot": "0", "Reason": "据中国基金报，国盛证券已完成 DeepSeek-R1大模型的本地化部署及场景适配，未来将基于DeepSeek实现智能客户问答、智能系统运维等场景", "FirstShelveTime": "1739080339", "UpdateCacheTime": "1739080339", "prod_name": "国盛金控", "Hot": 34375}, {"StockID": "601211", "IsZz": "2", "IsHot": "0", "Reason": "公司已完成DeepSeek-R1模型的本地化部署，目前已在场景应用测试中，将赋能和拓展“君弘灵犀”大模型", "FirstShelveTime": "1739080339", "UpdateCacheTime": "1739080339", "prod_name": "国泰海通", "Hot": 2706}, {"StockID": "000776", "IsZz": "2", "IsHot": "0", "Reason": "已完成DeepSeek-V3和R1的接入，并上线基于DeepSeek的微信小程序", "FirstShelveTime": "1739080339", "UpdateCacheTime": "1739080339", "prod_name": "广发证券", "Hot": 2517}, {"StockID": "601108", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月12日互动易：目前公司自主研发的“财小智”已完成与deepseek模型的对接，可通过使用deepseek的模型能力", "FirstShelveTime": "1739408898", "UpdateCacheTime": "1739408898", "prod_name": "财通证券", "Hot": 2346}, {"StockID": "601788", "IsZz": "2", "IsHot": "0", "Reason": "光大证券AI中台新增DeepSeek大模型本地化部署和多场景应用测试，并基于华为NPU算力平台实现国产化适配", "FirstShelveTime": "1739080339", "UpdateCacheTime": "1739080339", "prod_name": "光大证券", "Hot": 1788}, {"StockID": "601995", "IsZz": "2", "IsHot": "0", "Reason": "子公司中金财富宣布完成与深度求索（DeepSeek）研发的DeepSeek-R1大模型深度融合。", "FirstShelveTime": "1739088602", "UpdateCacheTime": "1739088602", "prod_name": "中金公司", "Hot": 1614}, {"StockID": "600109", "IsZz": "2", "IsHot": "0", "Reason": "公司已完成DeepSeek大模型的本地化部署测试", "FirstShelveTime": "1739080339", "UpdateCacheTime": "1739080339", "prod_name": "国金证券", "Hot": 1240}, {"StockID": "600909", "IsZz": "2", "IsHot": "0", "Reason": "公司已完成 DeepSeek-R1大模型的本地化部署及场景适配", "FirstShelveTime": "1739080339", "UpdateCacheTime": "1739080339", "prod_name": "华安证券", "Hot": 1168}, {"StockID": "002736", "IsZz": "2", "IsHot": "0", "Reason": "公司已完成DeepSeek模型本地部署、后续计划将更广泛应用于金太阳APP、等核心的证券业务领域", "FirstShelveTime": "1739080889", "UpdateCacheTime": "1739080889", "prod_name": "国信证券", "Hot": 774}, {"StockID": "601377", "IsZz": "2", "IsHot": "0", "Reason": "完成了DeepSeek V3和R1两款大模型产品接入中台大模型矩阵，为业务场景赋能", "FirstShelveTime": "1739080339", "UpdateCacheTime": "1739080339", "prod_name": "兴业证券", "Hot": 669}, {"StockID": "000728", "IsZz": "2", "IsHot": "0", "Reason": "公司已完成deepseek在金融场景的本地化部署及适配性测试，计划将其深度整合至自主研发的智能服务平台“燎元智能助手”中", "FirstShelveTime": "1739080339", "UpdateCacheTime": "1739080339", "prod_name": "国元证券", "Hot": 499}, {"StockID": "600918", "IsZz": "2", "IsHot": "0", "Reason": "已完成DeepSeek-R1的本地化部署，计划应用于财富管理、投行业务等领域", "FirstShelveTime": "1739080339", "UpdateCacheTime": "1739080339", "prod_name": "中泰证券", "Hot": 435}]}, {"ID": "3376", "Name": "信息安全", "ZSCode": "801073", "FirstShelveTime": "1739085239", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "300921", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日互动易：公司目前已接入DeepSeek大模型，赋能SASE服务，提升公司云智网安一体化服务能力", "FirstShelveTime": "1739522518", "UpdateCacheTime": "1739522518", "prod_name": "南凌科技", "Hot": 2058}, {"StockID": "601360", "IsZz": "2", "IsHot": "0", "Reason": "360数字安全集团宣布其安全大模型正式接入DeepSeek，将以DeepSeek为安全大模型基座，训练出“DeepSeek版”安全大模型", "FirstShelveTime": "1738912910", "UpdateCacheTime": "1738912910", "prod_name": "三六零  ", "Hot": 1980}, {"StockID": "300352", "IsZz": "2", "IsHot": "0", "Reason": "微信公众号发布，公司密信AI能力平台已成功对接DeepSeek", "FirstShelveTime": "1738804747", "UpdateCacheTime": "1738804747", "prod_name": "北信源", "Hot": 1223}, {"StockID": "002439", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月10日公众号：全面接入DeepSeek，启明星辰的云安全能力迎来AI大模型驱动的重大升级", "FirstShelveTime": "1739175341", "UpdateCacheTime": "1739175341", "prod_name": "启明星辰", "Hot": 1135}, {"StockID": "300271", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月13日公众号：满血法律版DeepSeek-R1——元典问达2.0，正式上线", "FirstShelveTime": "1739682612", "UpdateCacheTime": "1739682612", "prod_name": "华宇软件", "Hot": 1130}, {"StockID": "688023", "IsZz": "2", "IsHot": "0", "Reason": "旗下恒脑·安全垂域大模型正式集成DeepSeek，完成基于DeepSeek R1的安全大模型的训练，推出首个“DeepSeek”版安全智能体。", "FirstShelveTime": "1738647794", "UpdateCacheTime": "1738647794", "prod_name": "安恒信息", "Hot": 1003}, {"StockID": "688561", "IsZz": "2", "IsHot": "0", "Reason": "奇安信宣布完成与 DeepSeek 全面深度接入，安全专业问答性能分数提升约 16%", "FirstShelveTime": "1738770595", "UpdateCacheTime": "1738770595", "prod_name": "奇安信  ", "Hot": 652}, {"StockID": "688168", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月7日官微消息,安博通下一代AI防火墙与人工智能大模型强强联合,实现了全新升级,搭载DeepSeek-R1-Distill-Qwen-32B模型", "FirstShelveTime": "1739524530", "UpdateCacheTime": "1739524530", "prod_name": "安博通  ", "Hot": 525}, {"StockID": "300369", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日公众号：绿盟科技已经成功完成了对DeepSeek-R1的接入，并实现绿盟风云卫AI安全能力平台与DeepSeek-R1双模型共同研判分析", "FirstShelveTime": "1739349764", "UpdateCacheTime": "1739349764", "prod_name": "绿盟科技", "Hot": 470}]}, {"ID": "3372", "Name": "AI医疗", "ZSCode": "801710", "FirstShelveTime": "1739084485", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "300253", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日互动易：公司的医护智能助手WiNEX Copilot产品已经适配DeepSeek，未来可以给医院用户更多选择", "FirstShelveTime": "1740124652", "UpdateCacheTime": "1740124652", "prod_name": "卫宁健康", "Hot": 4632}, {"StockID": "000710", "IsZz": "2", "IsHot": "0", "Reason": "公司目前已接入 Deepseek等多种开源模型，为公司临床客户提供更优质的服务", "FirstShelveTime": "1739408837", "UpdateCacheTime": "1739408837", "prod_name": "贝瑞基因", "Hot": 2493}, {"StockID": "603990", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月18日公告，DeepSeek 系开源模型，公司已进行了该模型的接入。", "FirstShelveTime": "1739865093", "UpdateCacheTime": "1739865093", "prod_name": "麦迪科技", "Hot": 2187}, {"StockID": "300451", "IsZz": "2", "IsHot": "0", "Reason": "创业慧康已将DeepSeek接入创业慧康大模型服务平台BsoftGPT，利用其模型特点，更好地赋能各项AI+医疗业务场景", "FirstShelveTime": "1739105006", "UpdateCacheTime": "1739105006", "prod_name": "创业慧康", "Hot": 1498}, {"StockID": "600276", "IsZz": "2", "IsHot": "0", "Reason": "恒瑞医药向界面新闻回应企业文件《关于在公司内部全面开展DeepSeek应用的通知》消息属实", "FirstShelveTime": "1738900438", "UpdateCacheTime": "1738900438", "prod_name": "恒瑞医药", "Hot": 1151}, {"StockID": "301070", "IsZz": "2", "IsHot": "0", "Reason": "豫资开勒旗下的医疗AI平台“灵曦助手”完成DeepSeek大模型的升级部署", "FirstShelveTime": "1739154688", "UpdateCacheTime": "1739154688", "prod_name": "开勒股份", "Hot": 1075}, {"StockID": "300290", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月13日互动易：目前公司研发中心已经完成DeepSeek-R1-Distill-Qwen-14B 模型在自有环境荣科数据中心的本地化部署", "FirstShelveTime": "1739524888", "UpdateCacheTime": "1739524888", "prod_name": "荣科科技", "Hot": 949}, {"StockID": "002219", "IsZz": "2", "IsHot": "0", "Reason": "公司已经完成了DeepSeek-R1(深度求索智能医疗系统)的本地化部署。", "FirstShelveTime": "1740980812", "UpdateCacheTime": "1740980812", "prod_name": "新里程", "Hot": 714}, {"StockID": "688246", "IsZz": "2", "IsHot": "0", "Reason": "公司已经已经接入DeepSeek，正在进行专业领域内的训练，后续将基于DeepSeek模型升级目前已有的医疗AI应用", "FirstShelveTime": "1739184538", "UpdateCacheTime": "1739184538", "prod_name": "嘉和美康", "Hot": 500}, {"StockID": "300937", "IsZz": "2", "IsHot": "0", "Reason": "信阳新闻网：公司全面本地化部署国内顶尖AI大模型DeepSeek", "FirstShelveTime": "1739177149", "UpdateCacheTime": "1739177149", "prod_name": "药易购", "Hot": 476}]}, {"ID": "3374", "Name": "其他", "ZSCode": "0", "FirstShelveTime": "1739084976", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "300468", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月17日互动易：公司技术团队已完成了deepseek R1模型的本地化部署工作", "FirstShelveTime": "1741072900", "UpdateCacheTime": "1741072900", "prod_name": "四方精创", "Hot": 20427}, {"StockID": "002657", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月7日公众号：中科金财与海光联合推出软硬一体解决方案，深度适配DeepSeek模型", "FirstShelveTime": "1739269795", "UpdateCacheTime": "1739269795", "prod_name": "中科金财", "Hot": 9955}, {"StockID": "300348", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日互动易回复，自DeepSeekV3版本开源发布以来，我们对此保持了密切关注，并在春节前已经在部分公司内部应用场景接入该模型进行使用", "FirstShelveTime": "1739524798", "UpdateCacheTime": "1739524798", "prod_name": "长亮科技", "Hot": 7101}, {"StockID": "002583", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月24日官微：DeepSeek遇见海能达，“AI警察”增持智能情指行，助力公安大部制改革提质增效", "FirstShelveTime": "1740389810", "UpdateCacheTime": "1740389810", "prod_name": "海能达", "Hot": 5080}, {"StockID": "000016", "IsZz": "2", "IsHot": "0", "Reason": "2025年02月14日康佳电视官微：2月14日，康佳电视宣布将DeepSeek先进的AI技术融入康佳智能电视系统当中，标志着电视行业正式迈入AI深度思考的新纪元", "FirstShelveTime": "1740895289", "UpdateCacheTime": "1740895289", "prod_name": "深康佳Ａ", "Hot": 4689}, {"StockID": "603209", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月16日官网新闻，公司成功接入DeepSeek API，逐步构建智能化应用场景，以提升人力资源调配、知识管理和船舶调度的智能化水平。", "FirstShelveTime": "1739956254", "UpdateCacheTime": "1739956254", "prod_name": "兴通股份", "Hot": 4625}, {"StockID": "600734", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月13日官微：公司算力在线平台通过整合行业领先的算力资源与技术，并已成功实现 DeepSeek-R1 的全参数模型部署", "FirstShelveTime": "1740043807", "UpdateCacheTime": "1740043807", "prod_name": "实达集团", "Hot": 4026}, {"StockID": "600678", "IsZz": "2", "IsHot": "0", "Reason": "参股子公司开物信息旗下矿拉拉数字平台2025年2月11日公众号：开物信息紧跟技术发展趋势，已实现公司AI管理中台与DeepSeek等多个主流大模型的融合", "FirstShelveTime": "1739943039", "UpdateCacheTime": "1739943039", "prod_name": "四川金顶", "Hot": 3901}, {"StockID": "300766", "IsZz": "2", "IsHot": "0", "Reason": "公司早期便接入Deepseek，并表示将拥抱其大模型开放的技术生态；幻方的一位重要股东曾为每日互动创始核心骨干成员，但公司未持有深度求索和幻方科技的股权，也尚未提供语料数据", "FirstShelveTime": "1738638240", "UpdateCacheTime": "1738638240", "prod_name": "每日互动", "Hot": 3647}, {"StockID": "002811", "IsZz": "2", "IsHot": "0", "Reason": "子公司目前“IDEAFUSION兆材云库数字化平台”部分功能已经接入DeepSeek模型，包括物料顾问检索推理场景、黑洞文本重写场景 、知识库顾问场景。", "FirstShelveTime": "1740722128", "UpdateCacheTime": "1740722128", "prod_name": "郑中设计", "Hot": 3507}, {"StockID": "300925", "IsZz": "2", "IsHot": "0", "Reason": "官微称，法本信息AI产品矩阵全面融入DeepSeek模型，自2023年12月起便率先接入DeepSeek模型", "FirstShelveTime": "1738890457", "UpdateCacheTime": "1738890457", "prod_name": "法本信息", "Hot": 3245}, {"StockID": "300674", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月10日公众号：公司正式宣布全系产品接入DeepSeek大模型", "FirstShelveTime": "1739262833", "UpdateCacheTime": "1739262833", "prod_name": "宇信科技", "Hot": 3120}, {"StockID": "300645", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日互动易：目前公司部分产品已经与通义千问、智谱和DeepSeek在内的大模型进行了适配与本地化部署", "FirstShelveTime": "1739681230", "UpdateCacheTime": "1739681230", "prod_name": "正元智慧", "Hot": 3005}, {"StockID": "603236", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月12日官微：公司基于边缘计算模组SG885G，已成功实现DeepSeek模型的稳定运行，并完成了针对性微调。", "FirstShelveTime": "1741164307", "UpdateCacheTime": "1741164307", "prod_name": "移远通信", "Hot": 3002}, {"StockID": "603956", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月10日互动易回复，威派格研究院自2024年就开始对DeepSeek进行研究。目前，公司研发的“河图 AI” 平台产品已接入包括DeepSeek、智谱GLM等多款大模型", "FirstShelveTime": "1739263484", "UpdateCacheTime": "1739263484", "prod_name": "威派格  ", "Hot": 2925}, {"StockID": "002465", "IsZz": "2", "IsHot": "0", "Reason": "2025年3月18日互动易回复，目前已经私有化部署了deepseek，将逐步构建起一个高效强大的AI系统", "FirstShelveTime": "1742544029", "UpdateCacheTime": "1742544029", "prod_name": "海格通信", "Hot": 2892}, {"StockID": "000158", "IsZz": "2", "IsHot": "0", "Reason": "据公司官网：公司算力服务器上已经部署DeepSeek模型并进行研究", "FirstShelveTime": "1739525010", "UpdateCacheTime": "1739525010", "prod_name": "常山北明", "Hot": 2844}, {"StockID": "300047", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月7日公众号：旗下的商品治理、价格监测、智能物资管治一体化平台、智能辅助评标、智能采购商城等产品全面接入DeepSeek大模型", "FirstShelveTime": "1739268591", "UpdateCacheTime": "1739268591", "prod_name": "天源迪科", "Hot": 2558}, {"StockID": "300364", "IsZz": "2", "IsHot": "0", "Reason": "中文在线目前已在部分内部 AI 网文创作流程中部署 DeepSeek-R1，旨在通过调用其能力提升创作效率。", "FirstShelveTime": "1738764033", "UpdateCacheTime": "1738764033", "prod_name": "中文在线", "Hot": 2335}, {"StockID": "002862", "IsZz": "2", "IsHot": "0", "Reason": "2025年02月08日灵优智学官微：灵优智学全面接入DeepSeek，为AI玩具提供“最优解”", "FirstShelveTime": "1739408968", "UpdateCacheTime": "1739408968", "prod_name": "实丰文化", "Hot": 2332}, {"StockID": "300380", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月13日官微：早在2024年5月，安硕即启动了千寻金融垂直领域大模型与DeepSeek的对接工作", "FirstShelveTime": "1740895392", "UpdateCacheTime": "1740895392", "prod_name": "安硕信息", "Hot": 2286}, {"StockID": "002050", "IsZz": "2", "IsHot": "0", "Reason": "2025年03月05日官微：三花已成功搭建DeepSeek - R1模型私有化部署架构并开启内测，专属 “数字助手” 雏形初现", "FirstShelveTime": "1741851212", "UpdateCacheTime": "1741851212", "prod_name": "三花智控", "Hot": 2254}, {"StockID": "002313", "IsZz": "2", "IsHot": "0", "Reason": "孙公司芯讯通2025年2月12日官微：目前，芯讯通高算力AI模组SIM9650L已实测跑通DeepSeek R1模型", "FirstShelveTime": "1739685312", "UpdateCacheTime": "1739685312", "prod_name": "日海智能", "Hot": 2212}, {"StockID": "002195", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月14日互动易：公司持股比例1.42%的墨芯人工智能已完成DeepSeek R1全系列蒸馏模型的推理部署", "FirstShelveTime": "1741170988", "UpdateCacheTime": "1741170988", "prod_name": "岩山科技", "Hot": 2138}, {"StockID": "600602", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月7日子公司上海南洋万邦软件技术有限公司微信公众号：南洋万邦星宫 AI 平台全面接入 Deepseek 系列模型", "FirstShelveTime": "1739269687", "UpdateCacheTime": "1739269687", "prod_name": "云赛智联", "Hot": 2133}, {"StockID": "002117", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月13日互动易：我公司关注DeepSeek的发展，已开始进行实施DeepSeek的本地化部署，并与公司现有大模型进行适配。", "FirstShelveTime": "1739868075", "UpdateCacheTime": "1739868075", "prod_name": "东港股份", "Hot": 2121}, {"StockID": "300465", "IsZz": "2", "IsHot": "0", "Reason": "官微称，公司对接DeepSeek平台，助力金融服务智能化升级", "FirstShelveTime": "1739428613", "UpdateCacheTime": "1739428613", "prod_name": "高伟达", "Hot": 2114}, {"StockID": "002599", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日互动易：公司旗下的中鸣机器人已正式接入国产开源大模型DeepSeek接口", "FirstShelveTime": "1739269507", "UpdateCacheTime": "1739269507", "prod_name": "盛通股份", "Hot": 2034}, {"StockID": "600588", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月9日公众号：春节期间用友BIP就完成了DeepSeek-V3 和 DeepSeek-R1的适配", "FirstShelveTime": "1739174779", "UpdateCacheTime": "1739174779", "prod_name": "用友网络", "Hot": 2008}, {"StockID": "300383", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日公众号：全面适配DeepSeek本地私有化部署和R1模型服务", "FirstShelveTime": "1739267483", "UpdateCacheTime": "1739267483", "prod_name": "光环新网", "Hot": 1935}, {"StockID": "300287", "IsZz": "2", "IsHot": "0", "Reason": "公司已经将DeepSeek作为基础大模型能力之一适配到了“利智方”产品中", "FirstShelveTime": "1739498581", "UpdateCacheTime": "1739498581", "prod_name": "飞利信", "Hot": 1927}, {"StockID": "300170", "IsZz": "2", "IsHot": "0", "Reason": "2024年年中汉得就已经完成了和Deepseek-V2的对接，在2025年1月份完成了和R1版本的对接", "FirstShelveTime": "1738832832", "UpdateCacheTime": "1738832832", "prod_name": "汉得信息", "Hot": 1916}, {"StockID": "002575", "IsZz": "2", "IsHot": "0", "Reason": "旗下图灵引擎全面启动DeepSeek大模型云平台部署", "FirstShelveTime": "1739174092", "UpdateCacheTime": "1739174092", "prod_name": "群兴玩具", "Hot": 1900}, {"StockID": "300166", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月8日公众号：公司已完成DeepSeek与幕僚智数产品的全面接入", "FirstShelveTime": "1739173317", "UpdateCacheTime": "1739173317", "prod_name": "东方国信", "Hot": 1874}, {"StockID": "000785", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日视频号：2月10日，居然智家全面接入DeepSeek大模型", "FirstShelveTime": "1739347982", "UpdateCacheTime": "1739347982", "prod_name": "居然智家", "Hot": 1832}, {"StockID": "002290", "IsZz": "2", "IsHot": "0", "Reason": "子公司海曦技术2025年2月10日官微：公司成功实现了基于中科方德操作系统的 DeepSeek R1模型接入，为国产化人工智能基础设施建设再添新成果。", "FirstShelveTime": "1740126600", "UpdateCacheTime": "1740126600", "prod_name": "禾盛新材", "Hot": 1705}, {"StockID": "300657", "IsZz": "2", "IsHot": "0", "Reason": "弘信电子集团于2023年12月20日点亮的全国首个国产燧原大规模集群庆阳智算中心已适配DeepSeek的全量模型", "FirstShelveTime": "1738770375", "UpdateCacheTime": "1738770375", "prod_name": "弘信电子", "Hot": 1674}, {"StockID": "301236", "IsZz": "2", "IsHot": "0", "Reason": "公司积极拥抱DeepSeek,率先进行产品的创新融合,通过把DeepSeek-R1接入天璇MaaS平台,以全栈AI技术服务加速企业智能化转型", "FirstShelveTime": "1738750251", "UpdateCacheTime": "1738750251", "prod_name": "软通动力", "Hot": 1642}, {"StockID": "600986", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月6日官微，春节前浙文互联完成本地化部署的DeepSeek-R1推理大模型", "FirstShelveTime": "1739172518", "UpdateCacheTime": "1739172518", "prod_name": "浙文互联", "Hot": 1607}, {"StockID": "600536", "IsZz": "2", "IsHot": "0", "Reason": "麒麟软件微信公众号2025年2月10日发布，DeepSeek全面接入银河麒麟操作系统。", "FirstShelveTime": "1739435350", "UpdateCacheTime": "1739435350", "prod_name": "中国软件", "Hot": 1474}, {"StockID": "300229", "IsZz": "2", "IsHot": "0", "Reason": "多维集成融合DeepSeek，拓尔思以“平台+系统+服务”三重奏领航行业大模型应用", "FirstShelveTime": "1738890206", "UpdateCacheTime": "1738890206", "prod_name": "拓尔思", "Hot": 1376}, {"StockID": "002398", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月12日公众号：垒知AI接入DeepSeek，推进工程建设行业智能革命", "FirstShelveTime": "1739408674", "UpdateCacheTime": "1739408674", "prod_name": "垒知集团", "Hot": 1362}, {"StockID": "603171", "IsZz": "2", "IsHot": "0", "Reason": "互动易称，目前deepseek已经应用于公司亿企赢SaaS平台坐席咨询服务、数智化运维等场景", "FirstShelveTime": "1739087208", "UpdateCacheTime": "1739087208", "prod_name": "税友股份", "Hot": 1324}, {"StockID": "300071", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月6日公司子公司迪思官微：自2月6日，福石控股旗下迪思传媒宣布其AI营销平台“迪思AI智链”、“FlinkAi”平台均已接入DeepSeek大模型。", "FirstShelveTime": "1739435115", "UpdateCacheTime": "1739435115", "prod_name": "福石控股", "Hot": 1277}, {"StockID": "300746", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月24日官微：控股子公司伏泰科技全面接入DeepSeek，技术共振助力城市精管善治。", "FirstShelveTime": "1740895507", "UpdateCacheTime": "1740895507", "prod_name": "汉嘉设计", "Hot": 1165}, {"StockID": "300459", "IsZz": "2", "IsHot": "0", "Reason": "公司研发的第一代产品——汤姆猫 AI 语音情感陪伴机器人接入了DeepSeek等模型的部分能力", "FirstShelveTime": "1741336638", "UpdateCacheTime": "1741336638", "prod_name": "汤姆猫", "Hot": 1037}, {"StockID": "601929", "IsZz": "2", "IsHot": "0", "Reason": "2025年02月16日官微：吉视传媒股份有限公司宣布成功完成DeepSeek大模型的本地化部署，成为吉林省首家实现大模型本地化部署的国有企业", "FirstShelveTime": "1740122505", "UpdateCacheTime": "1740122505", "prod_name": "吉视传媒", "Hot": 1034}, {"StockID": "300687", "IsZz": "2", "IsHot": "0", "Reason": "2月7日公司官微发布：赛意善谋GPT、知识库等产品也已全面接入DeepSeek-R1模型", "FirstShelveTime": "1739172697", "UpdateCacheTime": "1739172697", "prod_name": "赛意信息", "Hot": 1027}, {"StockID": "600100", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月12号公众号：知网华知大模型 + DeepSeek，知识服务“超强大脑”呼之欲出", "FirstShelveTime": "1739408728", "UpdateCacheTime": "1739408728", "prod_name": "同方股份", "Hot": 995}, {"StockID": "002908", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月12日，公司正式全面接入DeepSeek大模型，赋能就业、就医、政务等民生场景应用", "FirstShelveTime": "1739697602", "UpdateCacheTime": "1739697602", "prod_name": "德生科技", "Hot": 957}, {"StockID": "002642", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日官微：目前，荣联知识库管理与智能应答平台已完成了DeepSeek-R1模型的接入，并正在积极推动应用。", "FirstShelveTime": "1740045052", "UpdateCacheTime": "1740045052", "prod_name": "荣联科技", "Hot": 917}, {"StockID": "003007", "IsZz": "2", "IsHot": "0", "Reason": "公司官网：直真运维大模型接入DeepSeek，深度探索通信运维新范式", "FirstShelveTime": "1739956218", "UpdateCacheTime": "1739956218", "prod_name": "直真科技", "Hot": 905}, {"StockID": "002649", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月17日公司官网新闻：红麦聚信通过整合DeepSeek的技术优势，实现 AI 技术在核心业务板块的深度嵌入与全面赋能", "FirstShelveTime": "1741337334", "UpdateCacheTime": "1741337334", "prod_name": "博彦科技", "Hot": 850}, {"StockID": "300571", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月11日公众号：本地化部署DeepSeek，开启智能制造新篇章！", "FirstShelveTime": "1739350277", "UpdateCacheTime": "1739350277", "prod_name": "平治信息", "Hot": 846}, {"StockID": "603859", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月7日官微：DeepSeek为工业AI注入新动力，能科持续深化大模型融合", "FirstShelveTime": "1740043049", "UpdateCacheTime": "1740043049", "prod_name": "能科科技", "Hot": 841}, {"StockID": "600756", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月9日官微：灵犀有言人工智能平台已实现DeepSeek-R1/V3模型适配，支持用户在互联网、局域网等各类环境下调用AI能力", "FirstShelveTime": "1740287682", "UpdateCacheTime": "1740287682", "prod_name": "浪潮软件", "Hot": 834}, {"StockID": "300079", "IsZz": "2", "IsHot": "0", "Reason": "公司已基于DeepSeek-R1开源大模型完成接入及本地化部署", "FirstShelveTime": "1739184603", "UpdateCacheTime": "1739184603", "prod_name": "数码视讯", "Hot": 758}, {"StockID": "002301", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月8日公众号：数字化运营平台顺利接入DeepSeek大模型", "FirstShelveTime": "1739350306", "UpdateCacheTime": "1739350306", "prod_name": "齐心集团", "Hot": 755}, {"StockID": "300494", "IsZz": "2", "IsHot": "0", "Reason": "互动易称公司目前已有相关合作，正在进行对接使用deepseek模型", "FirstShelveTime": "1738752695", "UpdateCacheTime": "1738752695", "prod_name": "盛天网络", "Hot": 748}, {"StockID": "301336", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月6日官微： 趣睡科技携手DeepSeek：AI赋能睡眠，开拓创新应用场景", "FirstShelveTime": "1740043989", "UpdateCacheTime": "1740043989", "prod_name": "趣睡科技", "Hot": 731}, {"StockID": "000156", "IsZz": "2", "IsHot": "0", "Reason": "旗下诗画浙江·文旅惠民卡已经接入DeepSeek", "FirstShelveTime": "1739434865", "UpdateCacheTime": "1739434865", "prod_name": "华数传媒", "Hot": 727}, {"StockID": "002264", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月17日互动易：公司已完成DeepSeek R1模型的本地化部署，并应用于营销、广告投放、供应链、客服等条线", "FirstShelveTime": "1740646015", "UpdateCacheTime": "1740646015", "prod_name": "新华都", "Hot": 696}, {"StockID": "002918", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月21日南海西樵官微：蒙娜丽莎基于DeepSeek本地化部署近日成功搭建蒙娜丽莎营销M-AI平台", "FirstShelveTime": "1740131907", "UpdateCacheTime": "1740131907", "prod_name": "蒙娜丽莎", "Hot": 656}, {"StockID": "002368", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月8日公众号：旗下慧点科技宣布，其自主研发的CUBE智能应用支撑平台全面接入国产大模型DeepSeek", "FirstShelveTime": "1739350231", "UpdateCacheTime": "1739350231", "prod_name": "太极股份", "Hot": 636}, {"StockID": "603888", "IsZz": "2", "IsHot": "0", "Reason": "根据新华妙笔小程序：新华妙笔工具支持小智A集成Deepseek满血版", "FirstShelveTime": "1741169551", "UpdateCacheTime": "1741169551", "prod_name": "新华网  ", "Hot": 632}, {"StockID": "002373", "IsZz": "2", "IsHot": "0", "Reason": "2025年02月21日官微：千方科技引入DeepSeek赋能城市交通综合治理", "FirstShelveTime": "1740286613", "UpdateCacheTime": "1740286613", "prod_name": "千方科技", "Hot": 619}, {"StockID": "688609", "IsZz": "2", "IsHot": "0", "Reason": "互动易称：公司为进一步提升产品智能化水平，已有产品接入DeepSeek应用", "FirstShelveTime": "1739185228", "UpdateCacheTime": "1739185228", "prod_name": "九联科技", "Hot": 591}, {"StockID": "300795", "IsZz": "2", "IsHot": "0", "Reason": "2月11日互动易：公司已完成DeepSeek的部署和测试", "FirstShelveTime": "1739347571", "UpdateCacheTime": "1739347571", "prod_name": "米奥会展", "Hot": 548}, {"StockID": "300788", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月17日互动易：公司数智出版平台已接入包括DeepSeek在内的多个大模型，以提升出版效率、优化精准营销，持续降本增效，并逐步应用于其他数字内容和学习产品", "FirstShelveTime": "1739789483", "UpdateCacheTime": "1739789483", "prod_name": "中信出版", "Hot": 506}, {"StockID": "603613", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月10日互动易：公司国联云业务在2024年就已经接入了DeepSeek大模型", "FirstShelveTime": "1739348256", "UpdateCacheTime": "1739348256", "prod_name": "国联股份", "Hot": 470}, {"StockID": "002063", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月7日公众号：远光软件已全面集成DeepSeek大模型", "FirstShelveTime": "1739269084", "UpdateCacheTime": "1739269084", "prod_name": "远光软件", "Hot": 469}, {"StockID": "689009", "IsZz": "2", "IsHot": "0", "Reason": "2025年2月14日，公司宣布将深度融合DeepSeek，开启智能出行新篇章", "FirstShelveTime": "1739684714", "UpdateCacheTime": "1739684714", "prod_name": "九号公司", "Hot": 467}, {"StockID": "688078", "IsZz": "2", "IsHot": "0", "Reason": "龙软科技已相继实现与DeepSeek-V2、DeepSeek-R1大模型的对接，顺利完成大模型在龙软智能管控平台、龙软智图等核心产品的接入", "FirstShelveTime": "1739087382", "UpdateCacheTime": "1739087382", "prod_name": "龙软科技", "Hot": 435}, {"StockID": "002841", "IsZz": "2", "IsHot": "0", "Reason": "公司旗下教育品牌希沃全系列产品自2月8日起有序接入DeepSeek大模型", "FirstShelveTime": "1739185064", "UpdateCacheTime": "1739185064", "prod_name": "视源股份", "Hot": 425}]}]}, {"Level1": {"ID": "3344", "Name": "服务商", "ZSCode": "0", "FirstShelveTime": "1738638022", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "601360", "IsZz": "2", "IsHot": "0", "Reason": "360集团创始人周鸿祎宣布将无偿为DeepSeek提供全方位网络安全防护（暂未向DeepSeek提供任何服务），此外其安全大模型已正式接入DeepSeek", "FirstShelveTime": "1738638298", "UpdateCacheTime": "1738638298", "prod_name": "三六零  ", "Hot": 1980}, {"StockID": "300418", "IsZz": "2", "IsHot": "0", "Reason": "公司携手新加坡南洋理工大学成功开发Q*算法，在MATH数据集上，Q*帮助DeepSeek-Math-7b提升至55.4%的准确率", "FirstShelveTime": "1738673376", "UpdateCacheTime": "1738673376", "prod_name": "昆仑万维", "Hot": 1857}]}, "Level2": []}, {"Level1": {"ID": "3345", "Name": "知识蒸馏技术", "ZSCode": "0", "FirstShelveTime": "1738638436", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "002819", "IsZz": "2", "IsHot": "0", "Reason": "2020年3月25日北京万里红科技股份有限公司申请了“基于知识蒸馏的语言模型训练方法、文本分类方法及装置”的专利", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "东方中科", "Hot": 7610}, {"StockID": "000997", "IsZz": "2", "IsHot": "0", "Reason": "在感知AI维度，公司主要基于大模型的海量数据，通过提取，蒸馏和剪枝的方式，在深耕的垂直场景里进行优化，形成垂直场景应用的小模型", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "新大陆", "Hot": 3627}, {"StockID": "300846", "IsZz": "2", "IsHot": "0", "Reason": "首都在线云平台在DeepSeek-R1推理能力的基础上，通过蒸馏技术使得模型更小、推理更快、资源消耗更少，方便部署在移动设备或边缘设备上", "FirstShelveTime": "1738913134", "UpdateCacheTime": "1738913134", "prod_name": "首都在线", "Hot": 2470}, {"StockID": "300884", "IsZz": "2", "IsHot": "0", "Reason": "公司专注于 AI 算法模型的小型化和边端化研究， 利用蒸馏等优化技术， 使 AI 更好地赋能公司各种小算力设备， 推动公司产品智能化升级", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "狄耐克", "Hot": 1961}, {"StockID": "603636", "IsZz": "2", "IsHot": "0", "Reason": "公司推出的深海大模型和白泽政务大模型均已接入DeepSeek相关版本并进行私有化部署，并通过深度融合DeepSeek-R1和DeepSeek-V3等先进模型，利用模型蒸馏技术优化模型性能", "FirstShelveTime": "1738913435", "UpdateCacheTime": "1738913435", "prod_name": "南威软件", "Hot": 1539}, {"StockID": "300229", "IsZz": "2", "IsHot": "0", "Reason": "公司研发的TRS智拓语义智能技术平台V9.0，基于TLM框架及知识蒸馏技术，实现领域AI大模型，增强拓尔思人工智能开发平台的能力", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "拓尔思", "Hot": 1376}, {"StockID": "301512", "IsZz": "2", "IsHot": "0", "Reason": "公司的工业缺陷人工智能检测平台采用知识蒸馏、迁移学习等模型优化技术", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "智信精密", "Hot": 1231}, {"StockID": "301318", "IsZz": "2", "IsHot": "0", "Reason": "公司算法团队专注于图像处理算法和 AI 算法在 SOC 的部署应用，通过蒸馏训练等工作，不断迭代升级 AI 算法模型", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "维海德", "Hot": 987}, {"StockID": "300608", "IsZz": "2", "IsHot": "0", "Reason": "国家知识产权局信息显示，思特奇信息技术股份有限公司申请一项名为“一种基于动态自适应蒸馏的模型压缩方法、系统及设备”的专利", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "思特奇", "Hot": 962}, {"StockID": "300496", "IsZz": "2", "IsHot": "0", "Reason": "公司通过模型的压缩技术，包括模型蒸馏等技术，在边缘AI方面将持续优化和升级不断强化在边缘AI上的领先优势", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "中科创达", "Hot": 888}, {"StockID": "688327", "IsZz": "2", "IsHot": "0", "Reason": "公司获得一项发明专利授权“联邦学习模型训练方法、客户端、服务器及存储介质”，该发明具体提供一种基于知识蒸馏的联邦学习模型训练方法。", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "云从科技", "Hot": 690}, {"StockID": "300659", "IsZz": "2", "IsHot": "0", "Reason": "公司获得了一项名为“基于知识蒸馏的涉密文本识别模型训练方法、系统及装置”的发明专利授权", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "中孚信息", "Hot": 621}, {"StockID": "688416", "IsZz": "2", "IsHot": "0", "Reason": "公司的TinyML 的轻量化模型设计技术结合了知识蒸馏等技术", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "恒烁股份", "Hot": 617}, {"StockID": "301169", "IsZz": "2", "IsHot": "0", "Reason": "公司累积了大量业务场景应用算法，如应用于政务图谱自动化数据构建研发蒸馏机制和多时间多标签识别算法以及优化事件要素抽取算法", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "零点有数", "Hot": 506}, {"StockID": "688207", "IsZz": "2", "IsHot": "0", "Reason": "公司新项目旨在研究并运用模型蒸馏、剪枝、压缩等技术，生成具有不同参数量级以适配不同算力能力硬件资源的多模态大模型", "FirstShelveTime": "1738638708", "UpdateCacheTime": "1738638708", "prod_name": "格灵深瞳", "Hot": 409}]}, "Level2": []}], "Stocks": [], "StockList": [{"StockID": "300766", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "公司早期便接入Deepseek，并表示将拥抱其大模型开放的技术生态；幻方的一位重要股东曾为每日互动创始核心骨干成员，但公司未持有深度求索和幻方科技的股权，也尚未提供语料数据"}], "prod_name": "每日互动", "HotNum": 3647}, {"StockID": "002881", "Tag": [{"ID": "3346", "Name": "端侧应用商", "Reason": "美格智能凭借其高算力AI模组矩阵与端侧大模型部署经验，正加速开发DeepSeek-R1模型在端侧落地应用及端云结合整体方案"}], "prod_name": "美格智能", "HotNum": 2981}, {"StockID": "688620", "Tag": [{"ID": "3346", "Name": "端侧应用商", "Reason": "公司AK39系列芯片对接了豆包、通义千问、文心一言、DeepSeek、Kimi等多家大语言模型"}], "prod_name": "安凯微  ", "HotNum": 778}, {"StockID": "688041", "Tag": [{"ID": "3343", "Name": "部署适配商", "Reason": "海光信息技术团队成功完成DeepSeek V3和R1模型与海光DCU（深度计算单元）的国产化适配，并正式上线"}], "prod_name": "海光信息", "HotNum": 2741}, {"StockID": "600126", "Tag": [{"ID": "3343", "Name": "部署适配商", "Reason": "子公司浙江省数据管理有限公司成功完成DeepSeek-R1的适配并实现DeepSeek-R1 70B参数及以下全部蒸馏模型的部署。5号公告称与DeepSeek系统的开发、应用等核心技术无关"}], "prod_name": "杭钢股份", "HotNum": 3251}, {"StockID": "603322", "Tag": [{"ID": "3343", "Name": "部署适配商", "Reason": "2025年2月2日，公司联手开源中国、沐曦、米塔碳等合作伙伴完成DeepSeek-R1 70B部署"}, {"ID": "3368", "Name": "一体机", "Reason": "公司是国产 GPU 品牌 “沐曦” 的特定行业总代理商，且注册了 “元醒” 算力设备商标，产品有“元醒曦云 C500-P PCIe 训推一体服务器”"}], "prod_name": "超讯通信", "HotNum": 1451}, {"StockID": "601360", "Tag": [{"ID": "3344", "Name": "服务商", "Reason": "360集团创始人周鸿祎宣布将无偿为DeepSeek提供全方位网络安全防护（暂未向DeepSeek提供任何服务），此外其安全大模型已正式接入DeepSeek"}, {"ID": "3376", "Name": "信息安全", "Reason": "360数字安全集团宣布其安全大模型正式接入DeepSeek，将以DeepSeek为安全大模型基座，训练出“DeepSeek版”安全大模型"}], "prod_name": "三六零  ", "HotNum": 1980}, {"StockID": "301512", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "公司的工业缺陷人工智能检测平台采用知识蒸馏、迁移学习等模型优化技术"}], "prod_name": "智信精密", "HotNum": 1231}, {"StockID": "301169", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "公司累积了大量业务场景应用算法，如应用于政务图谱自动化数据构建研发蒸馏机制和多时间多标签识别算法以及优化事件要素抽取算法"}], "prod_name": "零点有数", "HotNum": 506}, {"StockID": "301318", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "公司算法团队专注于图像处理算法和 AI 算法在 SOC 的部署应用，通过蒸馏训练等工作，不断迭代升级 AI 算法模型"}], "prod_name": "维海德", "HotNum": 987}, {"StockID": "300884", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "公司专注于 AI 算法模型的小型化和边端化研究， 利用蒸馏等优化技术， 使 AI 更好地赋能公司各种小算力设备， 推动公司产品智能化升级"}], "prod_name": "狄耐克", "HotNum": 1961}, {"StockID": "688416", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "公司的TinyML 的轻量化模型设计技术结合了知识蒸馏等技术"}], "prod_name": "恒烁股份", "HotNum": 617}, {"StockID": "300608", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "国家知识产权局信息显示，思特奇信息技术股份有限公司申请一项名为“一种基于动态自适应蒸馏的模型压缩方法、系统及设备”的专利"}], "prod_name": "思特奇", "HotNum": 962}, {"StockID": "688207", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "公司新项目旨在研究并运用模型蒸馏、剪枝、压缩等技术，生成具有不同参数量级以适配不同算力能力硬件资源的多模态大模型"}, {"ID": "3368", "Name": "一体机", "Reason": "格灵深瞳Deepseek-R1大模型一体机，提供私有化、易运维、高性价比的产品，助力公共安全能源、教育等行业集成商/服务商转型升级"}], "prod_name": "格灵深瞳", "HotNum": 409}, {"StockID": "300659", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "公司获得了一项名为“基于知识蒸馏的涉密文本识别模型训练方法、系统及装置”的发明专利授权"}], "prod_name": "中孚信息", "HotNum": 621}, {"StockID": "002819", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "2020年3月25日北京万里红科技股份有限公司申请了“基于知识蒸馏的语言模型训练方法、文本分类方法及装置”的专利"}], "prod_name": "东方中科", "HotNum": 7610}, {"StockID": "688327", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "公司获得一项发明专利授权“联邦学习模型训练方法、客户端、服务器及存储介质”，该发明具体提供一种基于知识蒸馏的联邦学习模型训练方法。"}, {"ID": "3375", "Name": "智能体", "Reason": "公司持续致力于通过模型蒸馏和工程优化降低模型成本，提升技术效率，提供自研模型和适配各类第三方模型（包括但不限于DeepSeek)的Agent应用及服务"}], "prod_name": "云从科技", "HotNum": 690}, {"StockID": "300229", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "公司研发的TRS智拓语义智能技术平台V9.0，基于TLM框架及知识蒸馏技术，实现领域AI大模型，增强拓尔思人工智能开发平台的能力"}, {"ID": "3374", "Name": "其他", "Reason": "多维集成融合DeepSeek，拓尔思以“平台+系统+服务”三重奏领航行业大模型应用"}], "prod_name": "拓尔思", "HotNum": 1376}, {"StockID": "000997", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "在感知AI维度，公司主要基于大模型的海量数据，通过提取，蒸馏和剪枝的方式，在深耕的垂直场景里进行优化，形成垂直场景应用的小模型"}], "prod_name": "新大陆", "HotNum": 3627}, {"StockID": "300496", "Tag": [{"ID": "3345", "Name": "知识蒸馏技术", "Reason": "公司通过模型的压缩技术，包括模型蒸馏等技术，在边缘AI方面将持续优化和升级不断强化在边缘AI上的领先优势"}], "prod_name": "中科创达", "HotNum": 888}, {"StockID": "688023", "Tag": [{"ID": "3376", "Name": "信息安全", "Reason": "旗下恒脑·安全垂域大模型正式集成DeepSeek，完成基于DeepSeek R1的安全大模型的训练，推出首个“DeepSeek”版安全智能体。"}], "prod_name": "安恒信息", "HotNum": 1003}, {"StockID": "002123", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "公司将DeepSeek大模型深度集成至多源AI调度引擎“天慧智汇台2.0”"}], "prod_name": "梦网科技", "HotNum": 3747}, {"StockID": "300624", "Tag": [{"ID": "3377", "Name": "AIGC", "Reason": "公司已完成深度求索最新推理大模型DeepSeek-R1的深入适配，涵盖旗下视频创意、绘图创意及文档创意软件业务多款产品"}], "prod_name": "万兴科技", "HotNum": 1061}, {"StockID": "300418", "Tag": [{"ID": "3344", "Name": "服务商", "Reason": "公司携手新加坡南洋理工大学成功开发Q*算法，在MATH数据集上，Q*帮助DeepSeek-Math-7b提升至55.4%的准确率"}, {"ID": "3378", "Name": "大模型融合", "Reason": "昆仑万维旗下「天工AI」正式推出PC版重大更新——上线“DeepSeek R1 + 联网搜索”功能，旨在解决了DeepSeek联网功能无法使用的问题"}], "prod_name": "昆仑万维", "HotNum": 1857}, {"StockID": "688316", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "青云科技2025年2月4日官微，青云科技旗下 AI 算力云服务——基石智算CoresHub 正式上线 DeepSeek-R1 系列模型，限时免费"}, {"ID": "3368", "Name": "一体机", "Reason": "2025年2月24日青云科技发布 DeepSeek 一体机，开箱即用，AI 转型一键启动"}], "prod_name": "青云科技", "HotNum": 2207}, {"StockID": "600797", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "与浙大共研的 OpenBuddy 开源大模型于2023年发布首款基于DeepSeek基座的跨语言模型"}], "prod_name": "浙大网新", "HotNum": 2752}, {"StockID": "688158", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "优刻得云平台已上线了DeepSeek Janus-Pro-7B大模型镜像。按相关操作即可在云平台上部署和调用模型"}, {"ID": "3368", "Name": "一体机", "Reason": "优刻得发布预置DeepSeek满血版大模型一体机"}], "prod_name": "优刻得  ", "HotNum": 1486}, {"StockID": "601728", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "中国电信天翼云已经上架DeepSeek"}, {"ID": "3368", "Name": "一体机", "Reason": "公司推出息壤智算一体机-DeepSeek版"}], "prod_name": "中国电信", "HotNum": 699}, {"StockID": "688039", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "当虹科技BlackEye多模态视听大模型完成DeepSeek深度融合"}, {"ID": "3368", "Name": "一体机", "Reason": "当虹科技正式发布了全新一代“DeepSeek+BlackEye”多模态大模型一体机"}], "prod_name": "当虹科技", "HotNum": 652}, {"StockID": "301171", "Tag": [{"ID": "3346", "Name": "端侧应用商", "Reason": "易点天下旗下公众号发文称，AI技术中台已完成DeepSeek-R1私有化部署，旗下KreadoAI、数眼智能、zMaticoo ADX等多款产品将融合大模型相关能力"}], "prod_name": "易点天下", "HotNum": 843}, {"StockID": "000681", "Tag": [{"ID": "3377", "Name": "AIGC", "Reason": "公司完成深度求索公司开源大模型DeepSeek-R1的接入与本地化部署，并在多个产品中深度应用其能力"}], "prod_name": "视觉中国", "HotNum": 2358}, {"StockID": "301236", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "公司积极拥抱DeepSeek,率先进行产品的创新融合,通过把DeepSeek-R1接入天璇MaaS平台,以全栈AI技术服务加速企业智能化转型"}, {"ID": "3346", "Name": "端侧应用商", "Reason": "机械革命AI PC产品线也正式接入DeepSeek，推动端侧AI的应用普及"}, {"ID": "3368", "Name": "一体机", "Reason": "软通动力全线产品接入和支持DeepSeek，并重磅推出 “DeepSeek 应用方案一体机”系列产品"}, {"ID": "3452", "Name": "昇腾一体机", "Reason": "软通动力推出 “DeepSeek 应用方案一体机”，其提供了昇腾、NVIDIA 等不同算力配置"}], "prod_name": "软通动力", "HotNum": 1642}, {"StockID": "300494", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "互动易称公司目前已有相关合作，正在进行对接使用deepseek模型"}], "prod_name": "盛天网络", "HotNum": 748}, {"StockID": "300364", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "中文在线目前已在部分内部 AI 网文创作流程中部署 DeepSeek-R1，旨在通过调用其能力提升创作效率。"}], "prod_name": "中文在线", "HotNum": 2335}, {"StockID": "688590", "Tag": [{"ID": "3346", "Name": "端侧应用商", "Reason": "DeepSeek模型已经成功接入新致软件新知平台保险、司法、汽车等重点行业的智能机器人系统中"}, {"ID": "3368", "Name": "一体机", "Reason": "新致软件联合中科海光，正式发布新致信创一体机——以海光K100 GPU服务器为算力基石，深度融合新致新知人工智能平台与DeepSeek系列大模型"}], "prod_name": "新致软件", "HotNum": 1582}, {"StockID": "300657", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "弘信电子集团于2023年12月20日点亮的全国首个国产燧原大规模集群庆阳智算中心已适配DeepSeek的全量模型"}, {"ID": "3368", "Name": "一体机", "Reason": "燧弘华创的国产算力一体机以全栈自主可控为核心，深度融合国产GPU硬件与深度优化软件栈，产品支持从1.5B到千亿级模型的DeepSeek全版本覆盖"}], "prod_name": "弘信电子", "HotNum": 1674}, {"StockID": "600941", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "移动云全面上线DeepSeek，实现全版本覆盖、全尺寸适配、全功能畅用"}, {"ID": "3368", "Name": "一体机", "Reason": "公司推出智算一体机-DeepSeek版"}], "prod_name": "中国移动", "HotNum": 640}, {"StockID": "688343", "Tag": [{"ID": "3346", "Name": "端侧应用商", "Reason": "云天励飞芯片团队完成 DeepEdge10 “算力积木”芯片平台与DeepSeek的多模型适配"}, {"ID": "3368", "Name": "一体机", "Reason": "公司与华为联合推出的云天天书大模型训推一体机成功适配DeepSeek，已经在深圳市龙岗区、南山区实现双区部署"}], "prod_name": "云天励飞", "HotNum": 826}, {"StockID": "600050", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "联通云发布与星罗平台融合的 DeepSeek-R1 系列模型"}], "prod_name": "XD中国联", "HotNum": 964}, {"StockID": "688561", "Tag": [{"ID": "3376", "Name": "信息安全", "Reason": "奇安信宣布完成与 DeepSeek 全面深度接入，安全专业问答性能分数提升约 16%"}], "prod_name": "奇安信  ", "HotNum": 652}, {"StockID": "600186", "Tag": [{"ID": "3375", "Name": "智能体", "Reason": "官微称，莲花紫星已在自有算力上成功部署DeepSeek R1大模型，并全面支持智能体调用"}], "prod_name": "莲花控股", "HotNum": 2113}, {"StockID": "300352", "Tag": [{"ID": "3376", "Name": "信息安全", "Reason": "微信公众号发布，公司密信AI能力平台已成功对接DeepSeek"}], "prod_name": "北信源", "HotNum": 1223}, {"StockID": "688365", "Tag": [{"ID": "3346", "Name": "端侧应用商", "Reason": "官微称，绫智（快麦小智推出的基于大模型智能体产品）的多项业务场景和功能已经接入并在支持DeepSeek各个版本"}], "prod_name": "光云科技", "HotNum": 530}, {"StockID": "688058", "Tag": [{"ID": "3375", "Name": "智能体", "Reason": "公司相关产品目前已接入DeepSeek，助力行业用户打造面向垂直领域的AI Agent应用"}], "prod_name": "宝兰德  ", "HotNum": 412}, {"StockID": "300170", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2024年年中汉得就已经完成了和Deepseek-V2的对接，在2025年1月份完成了和R1版本的对接"}], "prod_name": "汉得信息", "HotNum": 1916}, {"StockID": "300634", "Tag": [{"ID": "3377", "Name": "AIGC", "Reason": "公司Rich AIBox已正式接入DeepSeek-V3、DeepSeek-R1大模型，进一步提升了垂直领域大模型能力，实现逻辑推理、内容生成、图片理解等多方面的优化。"}], "prod_name": "彩讯股份", "HotNum": 799}, {"StockID": "300846", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "首都在线云平台在DeepSeek-R1推理能力的基础上，通过蒸馏技术使得模型更小、推理更快、资源消耗更少，方便部署在移动设备或边缘设备上"}, {"ID": "3345", "Name": "知识蒸馏技术", "Reason": "首都在线云平台在DeepSeek-R1推理能力的基础上，通过蒸馏技术使得模型更小、推理更快、资源消耗更少，方便部署在移动设备或边缘设备上"}, {"ID": "3368", "Name": "一体机", "Reason": "首都在线DeepSeek一体机深度融合软硬件技术，搭载自研的DeepSeek R1/V3系列大模型，覆盖从边缘端到云端的多样化场景需求"}], "prod_name": "首都在线", "HotNum": 2470}, {"StockID": "300925", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "官微称，法本信息AI产品矩阵全面融入DeepSeek模型，自2023年12月起便率先接入DeepSeek模型"}], "prod_name": "法本信息", "HotNum": 3245}, {"StockID": "603636", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "公司推出的深海大模型和白泽政务大模型均已接入DeepSeek 相关版本并进行私有化部署，并利用模型蒸馏技术优化模型性能"}, {"ID": "3345", "Name": "知识蒸馏技术", "Reason": "公司推出的深海大模型和白泽政务大模型均已接入DeepSeek相关版本并进行私有化部署，并通过深度融合DeepSeek-R1和DeepSeek-V3等先进模型，利用模型蒸馏技术优化模型性能"}, {"ID": "3368", "Name": "一体机", "Reason": "2025年2月28日官微：发布通用DeepSeek＋智能体一体机系列产品，助力AI大模型价值释放"}], "prod_name": "南威软件", "HotNum": 1539}, {"StockID": "839493", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "并行科技智算云平台已部署DeepSeek模型，用户可根据使用习惯随意选择。"}], "prod_name": "并行科技", "HotNum": 877}, {"StockID": "000977", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "浪潮云率先发布671B DeepSeek大模型一体机解决方案"}], "prod_name": "浪潮信息", "HotNum": 2838}, {"StockID": "600276", "Tag": [{"ID": "3372", "Name": "AI医疗", "Reason": "恒瑞医药向界面新闻回应企业文件《关于在公司内部全面开展DeepSeek应用的通知》消息属实"}], "prod_name": "恒瑞医药", "HotNum": 1151}, {"StockID": "000066", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "长城擎天GF7280 V5 AI训推一体机已全面适配DeepSeek R1系列模型"}], "prod_name": "中国长城", "HotNum": 2954}, {"StockID": "688227", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "品高AI大模型融合平台--AISTACK在2024年便开始支持DeepSeek的混合专家模型(MoE)，可用AISTACK本地部署DeepSeek R1，并为其推理服务实现了加速"}], "prod_name": "品高股份", "HotNum": 489}, {"StockID": "600271", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "公司联营企业推出航天联志DeepSeek一体机"}], "prod_name": "航天信息", "HotNum": 700}, {"StockID": "300017", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "爱捷云算力云平台已上架最新版DeepSeek-R1 660B 推理模型"}], "prod_name": "网宿科技", "HotNum": 746}, {"StockID": "002261", "Tag": [{"ID": "3343", "Name": "部署适配商", "Reason": "兆瀚AI算力产品基于鲲鹏+昇腾“处理器已完成DeepSeek多版本适配，为行业用户提供从训练到推理、从云端到边缘的完整部署能力。"}, {"ID": "3452", "Name": "昇腾一体机", "Reason": "公司与整数智能联合，共同推出业内首款搭载DeepSeek全系列模型的智能数据标注一体机，该一体机产品基于“昇腾+鲲鹏”打造的“兆瀚”AI算力硬件"}], "prod_name": "拓维信息", "HotNum": 4208}, {"StockID": "600909", "Tag": [{"ID": "3371", "Name": "券商", "Reason": "公司已完成 DeepSeek-R1大模型的本地化部署及场景适配"}], "prod_name": "华安证券", "HotNum": 1168}, {"StockID": "600109", "Tag": [{"ID": "3371", "Name": "券商", "Reason": "公司已完成DeepSeek大模型的本地化部署测试"}], "prod_name": "国金证券", "HotNum": 1240}, {"StockID": "000728", "Tag": [{"ID": "3371", "Name": "券商", "Reason": "公司已完成deepseek在金融场景的本地化部署及适配性测试，计划将其深度整合至自主研发的智能服务平台“燎元智能助手”中"}], "prod_name": "国元证券", "HotNum": 499}, {"StockID": "601377", "Tag": [{"ID": "3371", "Name": "券商", "Reason": "完成了DeepSeek V3和R1两款大模型产品接入中台大模型矩阵，为业务场景赋能"}], "prod_name": "兴业证券", "HotNum": 669}, {"StockID": "601788", "Tag": [{"ID": "3371", "Name": "券商", "Reason": "光大证券AI中台新增DeepSeek大模型本地化部署和多场景应用测试，并基于华为NPU算力平台实现国产化适配"}], "prod_name": "光大证券", "HotNum": 1788}, {"StockID": "601211", "Tag": [{"ID": "3371", "Name": "券商", "Reason": "公司已完成DeepSeek-R1模型的本地化部署，目前已在场景应用测试中，将赋能和拓展“君弘灵犀”大模型"}], "prod_name": "国泰海通", "HotNum": 2706}, {"StockID": "000776", "Tag": [{"ID": "3371", "Name": "券商", "Reason": "已完成DeepSeek-V3和R1的接入，并上线基于DeepSeek的微信小程序"}], "prod_name": "广发证券", "HotNum": 2517}, {"StockID": "600918", "Tag": [{"ID": "3371", "Name": "券商", "Reason": "已完成DeepSeek-R1的本地化部署，计划应用于财富管理、投行业务等领域"}], "prod_name": "中泰证券", "HotNum": 435}, {"StockID": "002670", "Tag": [{"ID": "3371", "Name": "券商", "Reason": "据中国基金报，国盛证券已完成 DeepSeek-R1大模型的本地化部署及场景适配，未来将基于DeepSeek实现智能客户问答、智能系统运维等场景"}], "prod_name": "国盛金控", "HotNum": 34375}, {"StockID": "000938", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "子公司自研一站式大模型服务平台软件-灵犀使能平台（LinSeer Hub）已经实现对DeepSeek V3/R1模型的纳管和上架"}, {"ID": "3368", "Name": "一体机", "Reason": "互动易称核心子公司新华三将根据DeepSeek在行业侧的应用情况，推出训推一体机解决方案"}], "prod_name": "紫光股份", "HotNum": 1660}, {"StockID": "002736", "Tag": [{"ID": "3371", "Name": "券商", "Reason": "公司已完成DeepSeek模型本地部署、后续计划将更广泛应用于金太阳APP、等核心的证券业务领域"}], "prod_name": "国信证券", "HotNum": 774}, {"StockID": "300168", "Tag": [{"ID": "3375", "Name": "智能体", "Reason": "互动易称公司目前正在将DeepSeek集成到公司数字智脑智能体、大模型服务支撑平台等产品中"}, {"ID": "3378", "Name": "大模型融合", "Reason": "互动易称公司目前正在将DeepSeek集成到公司数字智脑智能体、大模型服务支撑平台等产品中"}], "prod_name": "万达信息", "HotNum": 556}, {"StockID": "603171", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "互动易称，目前deepseek已经应用于公司亿企赢SaaS平台坐席咨询服务、数智化运维等场景"}], "prod_name": "税友股份", "HotNum": 1324}, {"StockID": "688078", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "龙软科技已相继实现与DeepSeek-V2、DeepSeek-R1大模型的对接，顺利完成大模型在龙软智能管控平台、龙软智图等核心产品的接入"}], "prod_name": "龙软科技", "HotNum": 435}, {"StockID": "601995", "Tag": [{"ID": "3371", "Name": "券商", "Reason": "子公司中金财富宣布完成与深度求索（DeepSeek）研发的DeepSeek-R1大模型深度融合。"}], "prod_name": "中金公司", "HotNum": 1614}, {"StockID": "300166", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "东方国信自主研发的幕僚智数、大模型训推平台等多款核心产品成功完成与DeepSeek-R1系列大模型的深度集成"}, {"ID": "3374", "Name": "其他", "Reason": "2025年2月8日公众号：公司已完成DeepSeek与幕僚智数产品的全面接入"}], "prod_name": "东方国信", "HotNum": 1874}, {"StockID": "000063", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "公司推出AiCube一体机，已全面适配DeepSeek V3 &amp; R1，仅需数小时即可完成DeepSeek R1全系列蒸馏模型的适配。"}], "prod_name": "中兴通讯", "HotNum": 2783}, {"StockID": "000032", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "公司所属的中国电子云推出了为DeepSeek优化的推理一体机"}], "prod_name": "深桑达Ａ", "HotNum": 1275}, {"StockID": "300451", "Tag": [{"ID": "3372", "Name": "AI医疗", "Reason": "创业慧康已将DeepSeek接入创业慧康大模型服务平台BsoftGPT，利用其模型特点，更好地赋能各项AI+医疗业务场景"}], "prod_name": "创业慧康", "HotNum": 1498}, {"StockID": "002335", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "公司携手壁仞科技顺利部署DeepSeek-R1-Distil蒸馏模型，并已上线Gitee AI，依然面向全体开发者免费使用"}, {"ID": "3368", "Name": "一体机", "Reason": "科华数据联合希姆计算深耕政务场景，打造DeepSeek加持的政务智能体一体机"}], "prod_name": "科华数据", "HotNum": 2713}, {"StockID": "301070", "Tag": [{"ID": "3372", "Name": "AI医疗", "Reason": "豫资开勒旗下的医疗AI平台“灵曦助手”完成DeepSeek大模型的升级部署"}], "prod_name": "开勒股份", "HotNum": 1075}, {"StockID": "688777", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "中控技术依托DeepSeek打造“工业BA超级智能系统”"}], "prod_name": "中控技术", "HotNum": 400}, {"StockID": "301248", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "旗下常青云高性能AI超融合平台完成与国产高性能推理模型DeepSeek系列大模型的深度适配优化，平台支持DeepSeekV3、推理模型R1、多模态模型Janus Pro等三大模型"}], "prod_name": "杰创智能", "HotNum": 966}, {"StockID": "600410", "Tag": [{"ID": "3375", "Name": "智能体", "Reason": "2025年2月9日公众号：2月6日，公司智能客服系列产品正式完成与DeepSeek模型各版本适配"}, {"ID": "3452", "Name": "昇腾一体机", "Reason": "官微称，将推出全栈国产智能客服解决方案一体机，采用昇腾算力+华为AICC软件+DeepSeek大模型+华胜天成智能客服应用软件"}], "prod_name": "华胜天成", "HotNum": 4908}, {"StockID": "000625", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "DeepSeek接入长安天枢大模型，首发搭载启源E07"}], "prod_name": "长安汽车", "HotNum": 1248}, {"StockID": "300377", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2025年2月6日官微：赢时胜大模型技术栈与DeepSeek主力模型全面对接"}, {"ID": "3452", "Name": "昇腾一体机", "Reason": "官微称，赢时胜AI一体机获昇腾认证，是DeepSeek与华为合作主推的硬件平台"}], "prod_name": "赢时胜", "HotNum": 2640}, {"StockID": "600986", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月6日官微，春节前浙文互联完成本地化部署的DeepSeek-R1推理大模型"}], "prod_name": "浙文互联", "HotNum": 1607}, {"StockID": "300687", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2月7日公司官微发布：赛意善谋GPT、知识库等产品也已全面接入DeepSeek-R1模型"}], "prod_name": "赛意信息", "HotNum": 1027}, {"StockID": "600570", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2025年2月10日公众号：恒生大模型系列应用全面接入DeepSeek主流模型(DeepSeek-V3/DeepSeek-R1)"}], "prod_name": "恒生电子", "HotNum": 7157}, {"StockID": "002575", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "旗下图灵引擎全面启动DeepSeek大模型云平台部署"}], "prod_name": "群兴玩具", "HotNum": 1900}, {"StockID": "300603", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "立昂领算云平台已上架最新版DeepSeek-R1660B推理模型，同时支持 DeepSeek-V3和DeepSeek-R1-Distill的多个版本模型预训练镜像"}], "prod_name": "立昂技术", "HotNum": 883}, {"StockID": "600588", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月9日公众号：春节期间用友BIP就完成了DeepSeek-V3 和 DeepSeek-R1的适配"}], "prod_name": "用友网络", "HotNum": 2008}, {"StockID": "300895", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "铜牛信息国资云平台正式上线DeepSeek-R1系列模型并提供私有化部署方案"}], "prod_name": "铜牛信息", "HotNum": 1270}, {"StockID": "300378", "Tag": [{"ID": "3375", "Name": "智能体", "Reason": "2025年2月9日公众号：DeepSeek大模型已全面集成至鼎捷IndepthAl智能体平台及鼎捷全线智能应用"}], "prod_name": "鼎捷数智", "HotNum": 1062}, {"StockID": "002439", "Tag": [{"ID": "3376", "Name": "信息安全", "Reason": "2025年2月10日公众号：全面接入DeepSeek，启明星辰的云安全能力迎来AI大模型驱动的重大升级"}], "prod_name": "启明星辰", "HotNum": 1135}, {"StockID": "300937", "Tag": [{"ID": "3372", "Name": "AI医疗", "Reason": "信阳新闻网：公司全面本地化部署国内顶尖AI大模型DeepSeek"}], "prod_name": "药易购", "HotNum": 476}, {"StockID": "688246", "Tag": [{"ID": "3372", "Name": "AI医疗", "Reason": "公司已经已经接入DeepSeek，正在进行专业领域内的训练，后续将基于DeepSeek模型升级目前已有的医疗AI应用"}], "prod_name": "嘉和美康", "HotNum": 500}, {"StockID": "300079", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "公司已基于DeepSeek-R1开源大模型完成接入及本地化部署"}], "prod_name": "数码视讯", "HotNum": 758}, {"StockID": "603766", "Tag": [{"ID": "3375", "Name": "智能体", "Reason": "隆鑫通用旗下无极机车“极屿OS”全场景生态AI车机系统（骑行助理）与DeepSeek大模型实现深度融合"}], "prod_name": "隆鑫通用", "HotNum": 553}, {"StockID": "002110", "Tag": [{"ID": "3375", "Name": "智能体", "Reason": "互动平台表示，公司将根据实际情况，积极学习运用DeepSeek等相关技术，强化推进数字化、智能化应用，推动公司数字化转型。"}], "prod_name": "三钢闽光", "HotNum": 486}, {"StockID": "002841", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "公司旗下教育品牌希沃全系列产品自2月8日起有序接入DeepSeek大模型"}], "prod_name": "视源股份", "HotNum": 425}, {"StockID": "688609", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "互动易称：公司为进一步提升产品智能化水平，已有产品接入DeepSeek应用"}], "prod_name": "九联科技", "HotNum": 591}, {"StockID": "300674", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月10日公众号：公司正式宣布全系产品接入DeepSeek大模型"}], "prod_name": "宇信科技", "HotNum": 3120}, {"StockID": "603956", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月10日互动易回复，威派格研究院自2024年就开始对DeepSeek进行研究。目前，公司研发的“河图 AI” 平台产品已接入包括DeepSeek、智谱GLM等多款大模型"}], "prod_name": "威派格  ", "HotNum": 2925}, {"StockID": "300383", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月11日公众号：全面适配DeepSeek本地私有化部署和R1模型服务"}], "prod_name": "光环新网", "HotNum": 1935}, {"StockID": "300638", "Tag": [{"ID": "3346", "Name": "端侧应用商", "Reason": "广和通AI玩具解决方案通过火山引擎接入DeepSeek开源模型，满足AI玩具场景在多模态交互、自然语言处理、情感分析、教育功能等方面的需求"}], "prod_name": "广和通", "HotNum": 2451}, {"StockID": "301095", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2025年2月10日公众号：旗下SemiMind半导体大模型平台正式接入DeepSeek-R1大模型，深度协同，让SemiMind更“懂”半导体"}], "prod_name": "广立微", "HotNum": 792}, {"StockID": "002929", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "2025年2月10日公众号：DeepSeek加持！润建股份&amp;希姆计算打造全国产政务算力一体机"}], "prod_name": "润建股份", "HotNum": 2669}, {"StockID": "002212", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "2025年2月11日公众号：发布DeepSeek安全智算一体机"}], "prod_name": "天融信", "HotNum": 966}, {"StockID": "300047", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月7日公众号：旗下的商品治理、价格监测、智能物资管治一体化平台、智能辅助评标、智能采购商城等产品全面接入DeepSeek大模型"}], "prod_name": "天源迪科", "HotNum": 2558}, {"StockID": "600666", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "2025年2月6日公众号：丝路新云平台正式上线DeepSeek-R1系列模型"}], "prod_name": "奥瑞德  ", "HotNum": 14542}, {"StockID": "002063", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月7日公众号：远光软件已全面集成DeepSeek大模型"}], "prod_name": "远光软件", "HotNum": 469}, {"StockID": "002413", "Tag": [{"ID": "3346", "Name": "端侧应用商", "Reason": "子公司理工雷科智算技术团队成功完成DeepSeek R1模型与旗下“山海”边缘智算模组、“雁门”智算服务器等产品的适配工作"}], "prod_name": "雷科防务", "HotNum": 2955}, {"StockID": "002599", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月11日互动易：公司旗下的中鸣机器人已正式接入国产开源大模型DeepSeek接口"}], "prod_name": "盛通股份", "HotNum": 2034}, {"StockID": "600602", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月7日子公司上海南洋万邦软件技术有限公司微信公众号：南洋万邦星宫 AI 平台全面接入 Deepseek 系列模型"}], "prod_name": "云赛智联", "HotNum": 2133}, {"StockID": "002657", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月7日公众号：中科金财与海光联合推出软硬一体解决方案，深度适配DeepSeek模型"}], "prod_name": "中科金财", "HotNum": 9955}, {"StockID": "300795", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2月11日互动易：公司已完成DeepSeek的部署和测试"}], "prod_name": "米奥会展", "HotNum": 548}, {"StockID": "600498", "Tag": [{"ID": "3343", "Name": "部署适配商", "Reason": "2025年02月07日官微：长江计算自主研发的G440K V2服务器，现已实现DeepSeek系列模型的推理适配和优化"}], "prod_name": "烽火通信", "HotNum": 1297}, {"StockID": "600104", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2025年2月11日公众号：上汽通用汽车成为首家将推理大模型深度融入智舱的合资车企DeepSeek-R1"}], "prod_name": "上汽集团", "HotNum": 842}, {"StockID": "000785", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月11日视频号：2月10日，居然智家全面接入DeepSeek大模型"}], "prod_name": "居然智家", "HotNum": 1832}, {"StockID": "300113", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "2025年2月11日公众号：顺网算力云全面拥抱DeepSeek"}], "prod_name": "顺网科技", "HotNum": 781}, {"StockID": "603613", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月10日互动易：公司国联云业务在2024年就已经接入了DeepSeek大模型"}], "prod_name": "国联股份", "HotNum": 470}, {"StockID": "000034", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "神州数码将DeepSeek集成到其自主研发的神州问学平台中，仅需3分钟部署DeepSeek模型"}, {"ID": "3452", "Name": "昇腾一体机", "Reason": "神州鲲泰问学一体机DeepSeek版在第三届北京人工智能产业创新发展大会上正式发布，并提供基于昇腾的智算服务"}], "prod_name": "神州数码", "HotNum": 2049}, {"StockID": "300369", "Tag": [{"ID": "3376", "Name": "信息安全", "Reason": "2025年2月11日公众号：绿盟科技已经成功完成了对DeepSeek-R1的接入，并实现绿盟风云卫AI安全能力平台与DeepSeek-R1双模型共同研判分析"}], "prod_name": "绿盟科技", "HotNum": 470}, {"StockID": "300872", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2025年2月10日公众号：旗下产融大模型正式集成国产大模型DeepSeek，完成基于DeepSeek R1的大模型服务平台升级，推出DeepSeek版包含产融分析和拓客智能体的产融大模型产品"}], "prod_name": "天阳科技", "HotNum": 5772}, {"StockID": "002368", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月8日公众号：旗下慧点科技宣布，其自主研发的CUBE智能应用支撑平台全面接入国产大模型DeepSeek"}], "prod_name": "太极股份", "HotNum": 636}, {"StockID": "300571", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月11日公众号：本地化部署DeepSeek，开启智能制造新篇章！"}], "prod_name": "平治信息", "HotNum": 846}, {"StockID": "002301", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月8日公众号：数字化运营平台顺利接入DeepSeek大模型"}], "prod_name": "齐心集团", "HotNum": 755}, {"StockID": "688367", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2025年2月11日公众号：工大高科盛视F1.0工业视觉大模型是面向工业场景的AI创新平台，目前已完成对接DeepSeek-R1、DeepSeek-V3等系列大模型"}], "prod_name": "工大高科", "HotNum": 331}, {"StockID": "002398", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月12日公众号：垒知AI接入DeepSeek，推进工程建设行业智能革命"}], "prod_name": "垒知集团", "HotNum": 1362}, {"StockID": "600100", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月12号公众号：知网华知大模型 + DeepSeek，知识服务“超强大脑”呼之欲出"}], "prod_name": "同方股份", "HotNum": 995}, {"StockID": "000710", "Tag": [{"ID": "3372", "Name": "AI医疗", "Reason": "公司目前已接入 Deepseek等多种开源模型，为公司临床客户提供更优质的服务"}], "prod_name": "贝瑞基因", "HotNum": 2493}, {"StockID": "601108", "Tag": [{"ID": "3371", "Name": "券商", "Reason": "2025年2月12日互动易：目前公司自主研发的“财小智”已完成与deepseek模型的对接，可通过使用deepseek的模型能力"}], "prod_name": "财通证券", "HotNum": 2346}, {"StockID": "002862", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年02月08日灵优智学官微：灵优智学全面接入DeepSeek，为AI玩具提供“最优解”"}], "prod_name": "实丰文化", "HotNum": 2332}, {"StockID": "002354", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2025年2月11日公众号：天娱数自研基座大模型天星大模型现已接入DeepSeek"}], "prod_name": "天娱数科", "HotNum": 3190}, {"StockID": "600839", "Tag": [{"ID": "3346", "Name": "端侧应用商", "Reason": "长虹官宣其AI TV正式接入DeepSeek，并支持在“深度思考（满血R1）”和“快速响应”两个版本间自由切换"}, {"ID": "3375", "Name": "智能体", "Reason": "2025年2月13日，长虹官宣其AITV正式接入DeepSeek，并支持在“深度思考(满血R1)”和“快速响应”两个版本间自由切换，这意味着行业首个能深度思考的 AITV智能体已全面上线"}], "prod_name": "四川长虹", "HotNum": 3315}, {"StockID": "300465", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "官微称，公司对接DeepSeek平台，助力金融服务智能化升级"}], "prod_name": "高伟达", "HotNum": 2114}, {"StockID": "000156", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "旗下诗画浙江·文旅惠民卡已经接入DeepSeek"}], "prod_name": "华数传媒", "HotNum": 727}, {"StockID": "300071", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月6日公司子公司迪思官微：自2月6日，福石控股旗下迪思传媒宣布其AI营销平台“迪思AI智链”、“FlinkAi”平台均已接入DeepSeek大模型。"}], "prod_name": "福石控股", "HotNum": 1277}, {"StockID": "600536", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "麒麟软件微信公众号2025年2月10日发布，DeepSeek全面接入银河麒麟操作系统。"}], "prod_name": "中国软件", "HotNum": 1474}, {"StockID": "003040", "Tag": [{"ID": "3375", "Name": "智能体", "Reason": "2025年2月12日微信公众号发布，公司将DeepSeek-V3、DeepSeek-R1在线模型接入公司咨询问答数智人、辅助办事机器人及业务经办数字员工等AI智能体平台"}], "prod_name": "楚天龙", "HotNum": 21503}, {"StockID": "301085", "Tag": [{"ID": "3452", "Name": "昇腾一体机", "Reason": "公司新推出的搭载昇腾芯片和DeepSeek系列模型的桌面级智能一体机D-BOXPro，该产品目前正处于市场推广初期，尚未产生相关收入"}], "prod_name": "亚康股份", "HotNum": 909}, {"StockID": "300287", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "公司已经将DeepSeek作为基础大模型能力之一适配到了“利智方”产品中"}], "prod_name": "飞利信", "HotNum": 1927}, {"StockID": "300921", "Tag": [{"ID": "3376", "Name": "信息安全", "Reason": "2025年2月11日互动易：公司目前已接入DeepSeek大模型，赋能SASE服务，提升公司云智网安一体化服务能力"}], "prod_name": "南凌科技", "HotNum": 2058}, {"StockID": "001339", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "2025年2月11日公众号：智微智能DeepSeek算力一体机，释放AI算力巅峰，加速智能未来"}], "prod_name": "智微智能", "HotNum": 2382}, {"StockID": "688168", "Tag": [{"ID": "3376", "Name": "信息安全", "Reason": "2025年2月7日官微消息,安博通下一代AI防火墙与人工智能大模型强强联合,实现了全新升级,搭载DeepSeek-R1-Distill-Qwen-32B模型"}], "prod_name": "安博通  ", "HotNum": 525}, {"StockID": "300348", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月11日互动易回复，自DeepSeekV3版本开源发布以来，我们对此保持了密切关注，并在春节前已经在部分公司内部应用场景接入该模型进行使用"}], "prod_name": "长亮科技", "HotNum": 7101}, {"StockID": "300290", "Tag": [{"ID": "3372", "Name": "AI医疗", "Reason": "2025年2月13日互动易：目前公司研发中心已经完成DeepSeek-R1-Distill-Qwen-14B 模型在自有环境荣科数据中心的本地化部署"}], "prod_name": "荣科科技", "HotNum": 949}, {"StockID": "000158", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "据公司官网：公司算力服务器上已经部署DeepSeek模型并进行研究"}, {"ID": "3352", "Name": "云平台", "Reason": "常山云数据中心成功完成DeepSeek-R1模型本地化部署并开放使用，更好地服务于政务、金融、医疗、教育等各行业领域，推动产业转型升级，赋能数字经济高质量发展"}], "prod_name": "常山北明", "HotNum": 2844}, {"StockID": "300645", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月11日互动易：目前公司部分产品已经与通义千问、智谱和DeepSeek在内的大模型进行了适配与本地化部署"}], "prod_name": "正元智慧", "HotNum": 3005}, {"StockID": "002065", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "2025年02月07日官微：东华云联合GPU知名独角兽企业燧原科技加速推进DeepSeek全量模型高效适配"}], "prod_name": "东华软件", "HotNum": 2454}, {"StockID": "002777", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2025年2月6日互动易：公司通过对DeepSeek-R1的接入和融合其相关能力，实现了公司银海“闻语”大模型训练质效的大幅度提升"}], "prod_name": "久远银海", "HotNum": 1142}, {"StockID": "603019", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "2025年2月14日公众号：曙光云发布全国产DeepSeek大模型超融合一体机"}], "prod_name": "中科曙光", "HotNum": 3176}, {"StockID": "300271", "Tag": [{"ID": "3376", "Name": "信息安全", "Reason": "2025年2月13日公众号：满血法律版DeepSeek-R1——元典问达2.0，正式上线"}], "prod_name": "华宇软件", "HotNum": 1130}, {"StockID": "689009", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月14日，公司宣布将深度融合DeepSeek，开启智能出行新篇章"}], "prod_name": "九号公司", "HotNum": 467}, {"StockID": "002313", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "孙公司芯讯通2025年2月12日官微：目前，芯讯通高算力AI模组SIM9650L已实测跑通DeepSeek R1模型"}], "prod_name": "日海智能", "HotNum": 2212}, {"StockID": "002908", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月12日，公司正式全面接入DeepSeek大模型，赋能就业、就医、政务等民生场景应用"}], "prod_name": "德生科技", "HotNum": 957}, {"StockID": "002229", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "2025年2月11日全资子公司英博云官微：英博云智算服务平台凭借前沿技术与创新理念，成功实现DeepSeek V3 &amp; R1 的深度部署与推理优化"}], "prod_name": "鸿博股份", "HotNum": 4639}, {"StockID": "300788", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月17日互动易：公司数智出版平台已接入包括DeepSeek在内的多个大模型，以提升出版效率、优化精准营销，持续降本增效，并逐步应用于其他数字内容和学习产品"}], "prod_name": "中信出版", "HotNum": 506}, {"StockID": "603990", "Tag": [{"ID": "3372", "Name": "AI医疗", "Reason": "2025年2月18日公告，DeepSeek 系开源模型，公司已进行了该模型的接入。"}], "prod_name": "麦迪科技", "HotNum": 2187}, {"StockID": "002117", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月13日互动易：我公司关注DeepSeek的发展，已开始进行实施DeepSeek的本地化部署，并与公司现有大模型进行适配。"}], "prod_name": "东港股份", "HotNum": 2121}, {"StockID": "300525", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2025年2月10日微信公众号，博思软件旗下博智星、数字凭证基座、阳光公采大模型等系列产品完成与“国运级AI”DeepSeek大模型的接入测试，共同开启数据智能新阶段，持续升级AI智能体产品，融合AI提升政企服务价值"}], "prod_name": "博思软件", "HotNum": 624}, {"StockID": "600678", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "参股子公司开物信息旗下矿拉拉数字平台2025年2月11日公众号：开物信息紧跟技术发展趋势，已实现公司AI管理中台与DeepSeek等多个主流大模型的融合"}], "prod_name": "四川金顶", "HotNum": 3901}, {"StockID": "000818", "Tag": [{"ID": "3343", "Name": "部署适配商", "Reason": "超擎数智以自主研发的AI Engine人工智能开发平台、NVAIE及定制化AI软件产品，全面加速用户人工智能应用的开发和部署，帮助企业和个人快速完成DeepSeek私有化部署落地"}], "prod_name": "航锦科技", "HotNum": 3931}, {"StockID": "003007", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "公司官网：直真运维大模型接入DeepSeek，深度探索通信运维新范式"}], "prod_name": "直真科技", "HotNum": 905}, {"StockID": "603209", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月16日官网新闻，公司成功接入DeepSeek API，逐步构建智能化应用场景，以提升人力资源调配、知识管理和船舶调度的智能化水平。"}], "prod_name": "兴通股份", "HotNum": 4625}, {"StockID": "600353", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "2025年2月19日四川省上市公司协会公众号：旭光电子发布DeepSeek边缘一体机， 强势赋能国产智能边算"}], "prod_name": "旭光电子", "HotNum": 2281}, {"StockID": "603859", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月7日官微：DeepSeek为工业AI注入新动力，能科持续深化大模型融合"}, {"ID": "3343", "Name": "部署适配商", "Reason": "能科科技在互动平台表示，公司构建的Al Agent能与DeepSeek进行适配。公司构建的Al Agent能够适配不同的开源和闭源大模型，形成多个行业的AI Agent。"}], "prod_name": "能科科技", "HotNum": 841}, {"StockID": "600734", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月13日官微：公司算力在线平台通过整合行业领先的算力资源与技术，并已成功实现 DeepSeek-R1 的全参数模型部署"}], "prod_name": "实达集团", "HotNum": 4026}, {"StockID": "300275", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2025年2月17日投资者互动平台：公司已完成DeepSeek-R1本地化部署，并将DeepSeek与公司自研SPG矿山安全大模型深度集成，实现公司全线软件产品接入DeepSeek"}], "prod_name": "梅安森", "HotNum": 539}, {"StockID": "301336", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月6日官微： 趣睡科技携手DeepSeek：AI赋能睡眠，开拓创新应用场景"}], "prod_name": "趣睡科技", "HotNum": 731}, {"StockID": "002642", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月11日官微：目前，荣联知识库管理与智能应答平台已完成了DeepSeek-R1模型的接入，并正在积极推动应用。"}], "prod_name": "荣联科技", "HotNum": 917}, {"StockID": "601929", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年02月16日官微：吉视传媒股份有限公司宣布成功完成DeepSeek大模型的本地化部署，成为吉林省首家实现大模型本地化部署的国有企业"}], "prod_name": "吉视传媒", "HotNum": 1034}, {"StockID": "300253", "Tag": [{"ID": "3372", "Name": "AI医疗", "Reason": "2025年2月11日互动易：公司的医护智能助手WiNEX Copilot产品已经适配DeepSeek，未来可以给医院用户更多选择"}], "prod_name": "卫宁健康", "HotNum": 4632}, {"StockID": "600845", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2025年2月14日官微：近期，天行IDE又快速与本地部署的DeepSeek大模型实现了深度集成。"}], "prod_name": "宝信软件", "HotNum": 652}, {"StockID": "002290", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "子公司海曦技术2025年2月10日官微：公司成功实现了基于中科方德操作系统的 DeepSeek R1模型接入，为国产化人工智能基础设施建设再添新成果。"}, {"ID": "3368", "Name": "一体机", "Reason": "子公司海曦技术2025年2月28日官微：单机也可跑DeepSeek 671B满血大模型--海曦大模型一体机推新品"}], "prod_name": "禾盛新材", "HotNum": 1705}, {"StockID": "002918", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月21日南海西樵官微：蒙娜丽莎基于DeepSeek本地化部署近日成功搭建蒙娜丽莎营销M-AI平台"}], "prod_name": "蒙娜丽莎", "HotNum": 656}, {"StockID": "002373", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年02月21日官微：千方科技引入DeepSeek赋能城市交通综合治理"}], "prod_name": "千方科技", "HotNum": 619}, {"StockID": "301302", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "华如科技基于军事“仿真+AI”技术积累，结合DeepSeek等开源大模型，推出XSimVerse®军事大模型"}], "prod_name": "华如科技", "HotNum": 1968}, {"StockID": "600756", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月9日官微：灵犀有言人工智能平台已实现DeepSeek-R1/V3模型适配，支持用户在互联网、局域网等各类环境下调用AI能力"}], "prod_name": "浪潮软件", "HotNum": 834}, {"StockID": "002230", "Tag": [{"ID": "3452", "Name": "昇腾一体机", "Reason": "科大讯飞发布“星火+DeepSeek双引擎一体机”，多款搭载昇腾卡"}], "prod_name": "科大讯飞", "HotNum": 1239}, {"StockID": "300857", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "协创数据旗下FCloud推出DeepSeek满血版一体机"}], "prod_name": "协创数据", "HotNum": 1273}, {"StockID": "688152", "Tag": [{"ID": "3452", "Name": "昇腾一体机", "Reason": "公司推出麒麟信安全国产化智算一体机，该一体机产品采用国产鲲鹏920架构服务器+华为昇腾AI卡，搭载麒麟信安安全操作系统，内置DeepSeek大模型"}], "prod_name": "麒麟信安", "HotNum": 677}, {"StockID": "002583", "Tag": [{"ID": "3352", "Name": "云平台", "Reason": "公司“情指行一体化实战平台”深度融合国产大模型DeepSeek全面开启智能化新时代，为公安实战指挥体系注入新质战斗力。"}, {"ID": "3374", "Name": "其他", "Reason": "2025年2月24日官微：DeepSeek遇见海能达，“AI警察”增持智能情指行，助力公安大部制改革提质增效"}], "prod_name": "海能达", "HotNum": 5080}, {"StockID": "688047", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "龙芯中科成功发布基于DeepSeek大模型的软硬全栈推理一体机"}], "prod_name": "龙芯中科", "HotNum": 26613}, {"StockID": "603017", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2024年半年报：公司持有园测信息科技股份有限公司21%股份。据园测信息2025年2月13日官微：当前槿墨AI已全面接入De­e­p­S­e­ek主流模型，包括De­e­p­S­e­ek-V3和De­e­p­S­e­ek-R1，将充分发挥De­e­p­S­e­ek “低成本+高性能+高开放度”的应用优势，实现大模型系列应用能力的全面跃升，为地理信息和社会治理领域提供更智能、更高效、更低成本的智能解决方案。"}], "prod_name": "中衡设计", "HotNum": 5569}, {"StockID": "600640", "Tag": [{"ID": "3377", "Name": "AIGC", "Reason": "025年2月11日互动易：公司的“国脉文化AIGC+生态合作平台”产品已接入DeepSeek等大模型。该平台是一款集AI企业治理、AIGC行业应用场景及算力资源调度的综合服务平台，面向多个行业场景，平台由三大模块组成：国脉云治、国脉云生和国脉云擎，平台自发布以来已为多家客户提供一站式的AIGC应用技术支撑、算力训练、推理等服务。"}], "prod_name": "国脉文化", "HotNum": 952}, {"StockID": "002264", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月17日互动易：公司已完成DeepSeek R1模型的本地化部署，并应用于营销、广告投放、供应链、客服等条线"}], "prod_name": "新华都", "HotNum": 696}, {"StockID": "603496", "Tag": [{"ID": "3452", "Name": "昇腾一体机", "Reason": "公司推出的昇腾Deepseek一体机分为训推一体机与推理一体机两种类型"}], "prod_name": "恒为科技", "HotNum": 3113}, {"StockID": "002811", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "子公司目前“IDEAFUSION兆材云库数字化平台”部分功能已经接入DeepSeek模型，包括物料顾问检索推理场景、黑洞文本重写场景 、知识库顾问场景。"}], "prod_name": "郑中设计", "HotNum": 3507}, {"StockID": "000988", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "2025年2月28日官微：DeepSeek+共工大模型，双擎驱动水利水电智能运维效率革命"}], "prod_name": "华工科技", "HotNum": 1847}, {"StockID": "603025", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "2025年2月26日公司控股子公司兴汉网际正式推出DeepSeeK AI智算一体机全平台系列产品"}], "prod_name": "大豪科技", "HotNum": 570}, {"StockID": "000016", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年02月14日康佳电视官微：2月14日，康佳电视宣布将DeepSeek先进的AI技术融入康佳智能电视系统当中，标志着电视行业正式迈入AI深度思考的新纪元"}], "prod_name": "深康佳Ａ", "HotNum": 4689}, {"StockID": "300380", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月13日官微：早在2024年5月，安硕即启动了千寻金融垂直领域大模型与DeepSeek的对接工作"}], "prod_name": "安硕信息", "HotNum": 2286}, {"StockID": "300746", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月24日官微：控股子公司伏泰科技全面接入DeepSeek，技术共振助力城市精管善治。"}], "prod_name": "汉嘉设计", "HotNum": 1165}, {"StockID": "603660", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "公司具备为客户提供基于DeepSeek不同参数的一体机产品和行业定制需要"}], "prod_name": "苏州科达", "HotNum": 1316}, {"StockID": "688228", "Tag": [{"ID": "3452", "Name": "昇腾一体机", "Reason": "官微称，开普云推出升级版AI一体机，昇腾算力加持并深度适配DeepSeek开普云"}], "prod_name": "开普云  ", "HotNum": 645}, {"StockID": "002219", "Tag": [{"ID": "3372", "Name": "AI医疗", "Reason": "公司已经完成了DeepSeek-R1(深度求索智能医疗系统)的本地化部署。"}], "prod_name": "新里程", "HotNum": 714}, {"StockID": "600728", "Tag": [{"ID": "3378", "Name": "大模型融合", "Reason": "佳都知行交通大模型与DeepSeek优化融合，加速交通数智化升级"}], "prod_name": "佳都科技", "HotNum": 795}, {"StockID": "603220", "Tag": [{"ID": "3343", "Name": "部署适配商", "Reason": "中贝通信合肥智算中心已完成DeepSeek全系列版本部署自主研发的DeepSeek-ZB训推一体机正加速推向全国市场。"}, {"ID": "3368", "Name": "一体机", "Reason": "2025年3月5日官微：中贝通信已推出多款面向政府、企业用户，搭载DeepSeek R1/V3满血版模型和蒸馏版模型的DeepSeek-ZB训推一体机"}], "prod_name": "中贝通信", "HotNum": 1121}, {"StockID": "300468", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月17日互动易：公司技术团队已完成了deepseek R1模型的本地化部署工作"}], "prod_name": "四方精创", "HotNum": 20427}, {"StockID": "603236", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月12日官微：公司基于边缘计算模组SG885G，已成功实现DeepSeek模型的稳定运行，并完成了针对性微调。"}], "prod_name": "移远通信", "HotNum": 3002}, {"StockID": "603888", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "根据新华妙笔小程序：新华妙笔工具支持小智A集成Deepseek满血版"}], "prod_name": "新华网  ", "HotNum": 632}, {"StockID": "002195", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月14日互动易：公司持股比例1.42%的墨芯人工智能已完成DeepSeek R1全系列蒸馏模型的推理部署"}], "prod_name": "岩山科技", "HotNum": 2138}, {"StockID": "836263", "Tag": [{"ID": "3375", "Name": "智能体", "Reason": "同步接入的DeepSeek“满血版”与“星曜之眼”合力打造智能体应用赋能集团日常业务，覆盖了工艺专家咨询、合同审查、造价评估、标书编撰、AI报表等业务场景"}], "prod_name": "中航泰达", "HotNum": 361}, {"StockID": "300459", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "公司研发的第一代产品——汤姆猫 AI 语音情感陪伴机器人接入了DeepSeek等模型的部分能力"}], "prod_name": "汤姆猫", "HotNum": 1037}, {"StockID": "002649", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年2月17日公司官网新闻：红麦聚信通过整合DeepSeek的技术优势，实现 AI 技术在核心业务板块的深度嵌入与全面赋能"}], "prod_name": "博彦科技", "HotNum": 850}, {"StockID": "603300", "Tag": [{"ID": "3368", "Name": "一体机", "Reason": "公众号：华铁DeepSeek大模型一体机——海南华铁联合新华三面向企业级市场的人工智能基础设施解决方案，近日成功落地。"}], "prod_name": "海南华铁", "HotNum": 11814}, {"StockID": "002050", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年03月05日官微：三花已成功搭建DeepSeek - R1模型私有化部署架构并开启内测，专属 “数字助手” 雏形初现"}], "prod_name": "三花智控", "HotNum": 2254}, {"StockID": "002465", "Tag": [{"ID": "3374", "Name": "其他", "Reason": "2025年3月18日互动易回复，目前已经私有化部署了deepseek，将逐步构建起一个高效强大的AI系统"}], "prod_name": "海格通信", "HotNum": 2892}, {"StockID": "002236", "Tag": [{"ID": "3452", "Name": "昇腾一体机", "Reason": "根据子公司华启智慧2025年3月22日官微：此次发布的DeepSeek一体机基于鲲鹏+昇腾处理器，可满足DeepSeek大模型从7B到671B不同参数规模版本的灵活部署"}], "prod_name": "大华股份", "HotNum": 1381}], "Power": 0, "Subscribe": 0, "ZT": {"600666": ["1", "9.97", "1751007778"], "688047": ["0", "13.56", "1751007773"]}, "IsGood": 0, "GoodNum": 5341, "ComNum": 3562, "errcode": "0", "t": 0.017358000000000012}