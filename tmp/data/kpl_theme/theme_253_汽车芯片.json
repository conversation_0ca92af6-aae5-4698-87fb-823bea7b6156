{"ID": "253", "Name": "汽车芯片", "BriefIntro": "汽车芯片即车规级芯片，是搭载在汽车上的集成电路，几乎存在于汽车的所有部位。汽车芯片是汽车产业竞争中的关键所在，可分为控制类（MCU和AI芯片）、功率类、模拟芯片、传感器和其他（如存储器）。", "ClassLayer": "2", "Desc": "", "PlateSwitch": "1", "StkSwitch": "2", "Introduction": "<p>相关题材介绍<br/></p><p><br/></p><p>一、分类</p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">汽车芯片即车规级芯片，是搭载在汽车上的集成电路，几乎存在于汽车的所有部位。汽车芯片是汽车产业竞争中的关键所在，可分为控制类（MCU和AI芯片）、功率类、模拟芯片、传感器和其他（如存储器）。</span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><br/></span></p><p><span style=\"background-color: rgb(255, 255, 255); color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify;\">二、规模占比</span><br/></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><br/></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">从我国汽车芯片市场结构来看，我国汽车芯片主要分为控制类（MCU和AI芯片）、功率类、模拟芯片、传感器。其中，控制类芯片、传感器芯片规模占比较高，分别为27.1%、23.5%。其次，功率半导体在汽车芯片占比为12.3%。</span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><br/></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">三、市场规模与预测</span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><br/></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"letter-spacing: 1.5px;\">近年来，我国汽车产销持续增长，对汽车芯片的需求日益旺盛，汽车芯片市场规模增长显著。中商产业研究院发布的《2018-2023年中国汽车半导体行业市场前景及投资策略研究报告》显示，2022年中国汽车芯片市场规模达794.6亿元，同比增长7.5%。中商产业研究院分析师预测，2023年市场规模将达850亿元，2024年将达905.4亿元。</span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"letter-spacing: 1.5px;\"><br/></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"letter-spacing: 1.5px;\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">MCU芯片：下游应用极为广泛，主要覆盖汽车电子、工业控制、消费电子、计算与存储、网络通信六大市场。目前，MCU芯片下游市场中汽车电子占比最高。中商产业研究院发布的《2023-2029中国MCU芯片市场现状研究分析与发展前景预测报告》显示，2022年中国MCU市场规模达493.2亿元，较上年增长13.67%。中商产业研究院分析师预测，2023年中国MCU市场规模将达到531.2亿元，2024年将达619.5亿元。</span></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"letter-spacing: 1.5px;\"><br/></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">IGBT：被称为电力电子行业里的“CPU”，广泛应用于电机节能、轨道交通、新能源汽车等领域。中商产业研究院发布的《2023-2028年中国IGBT市场调查与行业前景预测专题研究报告》显示，2022年中国新能源车IGBT市场规模约127亿元，同比增长111.67%。中商产业研究院分析师预测，2023年市场规模将达161亿元，2024年将达178亿元。</span></span></span><br/></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><br/></span></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">四、世界格局</span></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><br/></span></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">整体市场集中度较低，目前全球汽车芯片市场前五厂商占比接近50%。其中英飞凌占比最多，在英飞凌收购了Cypress之后，2020年凭借13.2%的市场份额占据第一。其次分别为恩智浦、瑞萨、TI及意法半导体，占比分别为10.9%、8.5%、8.3%及7.5%。</span></span></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><br/></span></span></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">五、发展前景</span></span></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">如今自主可控成为当务之急，政府<span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">高度重视半导体行业的发展，推出多项相关产业政策和发展规划，对推动芯片产业发展和自主研发能力，产业结构化升级起到了至关重要的作用，<span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">如《关于加强产融合作推动工业绿色发展的指导意见》《新能源汽车产业发展规划（2021-2035年）》。随着芯片技术不断进步，新能源利好政策的推动，汽车芯片市场需求不断攀升，未来国内各大汽车厂商有望大规模使用汽车芯片。</span></span></span></span></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><br/></span></span></span></span></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">在电动化、智能化的市场趋势下，中国厂商通过收购国外厂商，积极布局汽车半导体新能源、智能汽车领域，国内本土品牌具有渠道优势和较高的性价比，逐渐打开市场。未来，我国在高端供应链中不断突破并掌握核心技术，使中国制造业向高端供应链攀爬，加速进口替代，从而促进行业进一步发展。</span></span></span></span></span></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><br/></span></span></span></span></span></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"></span></span></span></span></span></span></span><span style=\"background-color: rgb(255, 255, 255); color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify;\">从市场层面来看，下游市场带来强劲需求。新能源汽车的推广势在必行，未来也将继续大力发展。<span style=\"color: rgb(35, 35, 34); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\">叠加无人驾驶的政策推进，更是引来新需求。</span>与庞大的机动车保有量相比，新能源汽车占比仍有很大的发展空间，随着新购、更换等需求推动，新能源汽车的保有量也将进一步提升，这将为汽车芯片带来带来巨大的市场需求，推动行业发展。叠加无人驾驶的政策推进，更是引来新需求。</span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><br/></span></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><span style=\"color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 1.5px; text-align: justify; background-color: rgb(255, 255, 255);\"><br/></span></span></p>", "CreateTime": "1721358722", "UpdateTime": "0", "Table": [{"Level1": {"ID": "2908", "Name": "控制类芯片", "ZSCode": "0", "Stocks": [{"StockID": "002906", "IsZz": "2", "IsHot": "0", "Reason": "公司正在研发基于高通8775芯片方案的舱驾一体及中央计算单元等跨域融合产品。", "prod_name": "华阳集团", "Hot": 7934}, {"StockID": "300077", "IsZz": "2", "IsHot": "0", "Reason": "公司车载、车规MCU芯片产品当前已批量用于汽车后装行业", "prod_name": "国民技术", "Hot": 2378}, {"StockID": "002049", "IsZz": "2", "IsHot": "0", "Reason": "车载控制器芯片为公司汽车电子主要产品之一，也是公司未来重要的业务方向之一", "prod_name": "紫光国微", "Hot": 1914}, {"StockID": "600841", "IsZz": "2", "IsHot": "0", "Reason": "2023年年报，上汽红岩汽车有限公司为公司全资子公司。在新能源重卡研发方面，子公司红岩汽车有限公司具有纯电动重卡、燃料电池重卡和系统的开发能力，拥有集成智能化电池温控管理、AutoSAR 高标准底层架构、 ASIL D 控制器芯片等技术。", "prod_name": "动力新科", "Hot": 1644}, {"StockID": "301117", "IsZz": "2", "IsHot": "0", "Reason": "公司的芯片产品主要用于车载无线通信系统，可以用于无人驾驶定位场景。", "prod_name": "佳缘科技", "Hot": 1317}, {"StockID": "300458", "IsZz": "2", "IsHot": "0", "Reason": "公司车规级芯片已经实现大规模量产并在多个客户产品方案上落地，可为包括智能中控、智能后视镜、液晶仪表盘等各类智能驾驶舱应用提供支持。", "prod_name": "全志科技", "Hot": 1288}, {"StockID": "688385", "IsZz": "2", "IsHot": "0", "Reason": "2021年底复旦微电子发布首款车用MCU产品", "prod_name": "复旦微电", "Hot": 1118}, {"StockID": "600699", "IsZz": "2", "IsHot": "0", "Reason": "公司正在和国内外多家整车厂商共同推进基于地平线、黑芝麻和高通等不同芯片平台的智能驾驶域控制器、驾舱融合域控制器等项目的研发及商业化落地。", "prod_name": "均胜电子", "Hot": 1109}, {"StockID": "301221", "IsZz": "2", "IsHot": "0", "Reason": "公司在智能座舱、智能驾驶以及新能源等方面已推出基于国内芯片厂商芯片的相关产品解决方案。", "prod_name": "光庭信息", "Hot": 1048}, {"StockID": "688135", "IsZz": "2", "IsHot": "0", "Reason": "公司目前涉及到的汽车电子芯片有MCU、多媒体主控芯片、传感器等领域", "prod_name": "利扬芯片", "Hot": 953}, {"StockID": "300496", "IsZz": "2", "IsHot": "0", "Reason": "公司和高通、英伟达等芯片厂商都展开密切的合作，已经推出自主研发的行泊一体智能驾驶域控制器 RazorDCX Pantanal 并已经完成初步实车验证。", "prod_name": "中科创达", "Hot": 888}, {"StockID": "300671", "IsZz": "2", "IsHot": "0", "Reason": "公司主要产品为 LED 屏控制及驱动、 MOSFET、 MCU、快充协议芯片，以及各类 ASIC 等类芯片，可应用于汽车电子领域。", "prod_name": "富满微", "Hot": 888}, {"StockID": "688262", "IsZz": "2", "IsHot": "0", "Reason": "公司目前产品线包括汽车车身和网关控制、动力总成控制、域控制、新能源电池管理、车联网安全等多种汽车芯片", "prod_name": "国芯科技", "Hot": 627}, {"StockID": "688515", "IsZz": "2", "IsHot": "0", "Reason": "公司车载以太网物理层芯片已进入到车路云的建设体系中，同时相关产品也已较多应用到智能座舱里。", "prod_name": "裕太微  ", "Hot": 396}, {"StockID": "688595", "IsZz": "2", "IsHot": "0", "Reason": "公司车规级信号链 MCU 芯片已顺利通过AEC-Q100 一系列车规级认证。", "prod_name": "芯海科技", "Hot": 395}, {"StockID": "688380", "IsZz": "2", "IsHot": "0", "Reason": "公司发布车规级MCU BAT32A2系列，主要应用于汽车及高端工业市场，目前已在客户端应用开发项目中全面量产", "prod_name": "中微半导", "Hot": 364}]}, "Level2": []}, {"Level1": {"ID": "2909", "Name": "功率半导体", "ZSCode": "0", "Stocks": [{"StockID": "600171", "IsZz": "2", "IsHot": "0", "Reason": "公司已有多款电源管理、功率器件（汽车点火IGBT和车载空调IGBT）汽车电子产品实现量产", "prod_name": "上海贝岭", "Hot": 1977}, {"StockID": "600460", "IsZz": "2", "IsHot": "0", "Reason": "公司应用于新能源汽车的IGBT模块（PIM）已在部分客户开始批量供货。", "prod_name": "士兰微  ", "Hot": 1394}, {"StockID": "600745", "IsZz": "2", "IsHot": "0", "Reason": "公司旗下 安世半导体是全球领先的分立与功率半导体IDM龙头厂商，是全球龙头的汽车半导体公司之一", "prod_name": "闻泰科技", "Hot": 811}, {"StockID": "605111", "IsZz": "2", "IsHot": "0", "Reason": "公司的主营业务为MOSFET、IGBT 等半导体芯片和功率器件的研发设计及销售。", "prod_name": "新洁能  ", "Hot": 594}, {"StockID": "603290", "IsZz": "2", "IsHot": "0", "Reason": "公司主营业务是以IGBT为主的功率半导体芯片和模块的设计研发和生产，并以IGBT模块形式对外实现销售。", "prod_name": "斯达半导", "Hot": 545}, {"StockID": "688187", "IsZz": "2", "IsHot": "0", "Reason": "公司形成了自主可控的高压 IGBT 技术体系，研制出 1700V-6500V 系列高压高电流密度 IGBT 产品，已大批量应用于轨道交通与电网领域。", "prod_name": "时代电气", "Hot": 453}, {"StockID": "688396", "IsZz": "2", "IsHot": "0", "Reason": "公司是国内最大的半导体芯片公司之一，生产的MOSFET，IGBT产品广泛应用于比亚迪等国内汽车。", "prod_name": "华润微  ", "Hot": 413}, {"StockID": "688711", "IsZz": "2", "IsHot": "0", "Reason": "全资子公司宏微科技主要为比亚迪海豹，海豚，元PLUS系列车型提供GV车规级IGBT，为比亚迪秦EV，比亚迪元EV系列车型提供GWB车规级IGBT", "prod_name": "宏微科技", "Hot": 345}]}, "Level2": []}, {"Level1": {"ID": "2910", "Name": "传感器芯片", "ZSCode": "0", "Stocks": [{"StockID": "603121", "IsZz": "2", "IsHot": "0", "Reason": "全资子公司盛美芯和参股中科阿尔法两家芯片设计公司，使得公司具备部分核心车规级芯片的自主设计，并提供提供车规级的核心敏感芯片", "prod_name": "XD华培动", "Hot": 1607}, {"StockID": "603501", "IsZz": "2", "IsHot": "0", "Reason": "通过与国际领先的汽车视觉技术公司开展方案合作，公司为后视摄像头（RVC）、 360 度环视系统（SVS）和电子后视镜提供了更高性价比的高质量图像解决方案，如OX08D10图像传感器等", "prod_name": "豪威集团", "Hot": 1394}, {"StockID": "300552", "IsZz": "2", "IsHot": "0", "Reason": "公司布局的全固态激光雷达芯片解决现有激光雷达产品存在的低可靠性、难集成、高成本等一系列难题，可用于车载雷达。", "prod_name": "万集科技", "Hot": 1264}, {"StockID": "002077", "IsZz": "2", "IsHot": "0", "Reason": "主要封装产品包括图像处理传感、生物识别传感、晶圆级 MEMS 和 5G 射频及电源等芯片，上述产品主要应用包含汽车等广泛领域", "prod_name": "大港股份", "Hot": 1235}, {"StockID": "600699", "IsZz": "2", "IsHot": "0", "Reason": "公司定位智能驾驶领域的 Tier1，开展从 L0 至 L4 级自动驾驶各级别技术研发，提供由智能驾驶域控制器、智能传感器、中间件到应用层算法的全栈解决方案", "prod_name": "均胜电子", "Hot": 1109}, {"StockID": "688052", "IsZz": "2", "IsHot": "0", "Reason": "公司推出了符合汽车电子应用的磁线性电流传感器，已大规模发货；车规级的磁开关、磁轮速传感器等方向研发进展顺利，客户端已完成送样测试", "prod_name": "纳芯微  ", "Hot": 521}]}, "Level2": []}, {"Level1": {"ID": "2911", "Name": "存储类芯片", "ZSCode": "0", "Stocks": [{"StockID": "300223", "IsZz": "2", "IsHot": "0", "Reason": "公司的存储芯片和模拟芯片均已在汽车领域量产销售，国际上主要的Tier1厂商如Continental、BOCSH、Valeo等均是公司客户。", "prod_name": "北京君正", "Hot": 2513}, {"StockID": "603986", "IsZz": "2", "IsHot": "0", "Reason": "公司 GD32A503 系列车规级 MCU 产品市场拓展稳步推进，并进一步推出GD32A490 系列高性能车规级 MCU 新品", "prod_name": "兆易创新", "Hot": 2433}]}, "Level2": []}, {"Level1": {"ID": "2912", "Name": "其他", "ZSCode": "0", "Stocks": [{"StockID": "003026", "IsZz": "2", "IsHot": "0", "Reason": "公司产品广泛运用于汽车电子，并正在进行车用半导体功率器件以及芯片的投产项目。", "prod_name": "中晶科技", "Hot": 5262}, {"StockID": "603005", "IsZz": "2", "IsHot": "0", "Reason": "公司是全球车规CIS芯片晶圆级TSV封装技术的开发者", "prod_name": "晶方科技", "Hot": 2234}, {"StockID": "301099", "IsZz": "2", "IsHot": "0", "Reason": "参股公司欧创芯（60%股权）是一家模拟芯片研发商，起产品主要应用场景之一为汽车车灯后装市场", "prod_name": "雅创电子", "Hot": 1194}, {"StockID": "301536", "IsZz": "2", "IsHot": "0", "Reason": "2024年4月15日互动易回复：公司车规影像SoC芯片，主要用于国内主流自主品牌和合资品牌车厂，已大量出货。", "prod_name": "星宸科技", "Hot": 1125}, {"StockID": "605358", "IsZz": "2", "IsHot": "0", "Reason": "公司于2016年通过国际一流汽车电子客户博世和大陆集团的体系认证，成为获得车载电源开关资格认证的肖特基二极管芯片供应商，目前实现大批量稳定供货。", "prod_name": "立昂微  ", "Hot": 820}, {"StockID": "603160", "IsZz": "2", "IsHot": "0", "Reason": "公司车规级触控芯片凭借高可靠性、优异 EMC 能力，以及从7 到 30+英寸屏幕的全覆盖品类， 2023 年出货量继续攀升", "prod_name": "汇顶科技", "Hot": 797}, {"StockID": "300613", "IsZz": "2", "IsHot": "0", "Reason": "公司具备专业车规ISP+模拟视频链路芯片+车载DVR芯片，及一系列车载视频产品及解决方案", "prod_name": "富瀚微", "Hot": 601}, {"StockID": "688536", "IsZz": "2", "IsHot": "0", "Reason": "公司发布 20 余款车规芯片，并相继顺利通过 AEC-Q100 认证，已陆续在客户端导入，公司可提供相对完整的模拟和电源管理元器件汽车级解决方案。", "prod_name": "思瑞浦  ", "Hot": 588}, {"StockID": "688486", "IsZz": "2", "IsHot": "0", "Reason": "2023年7月4日投资者活动记录表：公司在汽车电子领域有多款桥接产品进入量产，已有6颗芯片通过了车规级体系AEC-Q100认证，公司已拓展终端客户包括宝马、博世、长安、比亚迪、理想等。", "prod_name": "龙迅股份", "Hot": 481}, {"StockID": "688141", "IsZz": "2", "IsHot": "0", "Reason": "公司拟开展的新业务为汽车级芯片先进封测技术研发，汽车级芯片的先进封装生产及测试服务，同时可覆盖通讯级、工业级等高端封测业务。", "prod_name": "杰华特  ", "Hot": 480}]}, "Level2": []}], "Stocks": [], "StockList": [{"StockID": "300496", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司和高通、英伟达等芯片厂商都展开密切的合作，已经推出自主研发的行泊一体智能驾驶域控制器 RazorDCX Pantanal 并已经完成初步实车验证。"}], "prod_name": "中科创达", "HotNum": 888}, {"StockID": "002906", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司正在研发基于高通8775芯片方案的舱驾一体及中央计算单元等跨域融合产品。"}], "prod_name": "华阳集团", "HotNum": 7934}, {"StockID": "600699", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司正在和国内外多家整车厂商共同推进基于地平线、黑芝麻和高通等不同芯片平台的智能驾驶域控制器、驾舱融合域控制器等项目的研发及商业化落地。"}, {"ID": "2910", "Name": "传感器芯片", "Reason": "公司定位智能驾驶领域的 Tier1，开展从 L0 至 L4 级自动驾驶各级别技术研发，提供由智能驾驶域控制器、智能传感器、中间件到应用层算法的全栈解决方案"}], "prod_name": "均胜电子", "HotNum": 1109}, {"StockID": "301221", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司在智能座舱、智能驾驶以及新能源等方面已推出基于国内芯片厂商芯片的相关产品解决方案。"}], "prod_name": "光庭信息", "HotNum": 1048}, {"StockID": "688515", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司车载以太网物理层芯片已进入到车路云的建设体系中，同时相关产品也已较多应用到智能座舱里。"}], "prod_name": "裕太微  ", "HotNum": 396}, {"StockID": "301117", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司的芯片产品主要用于车载无线通信系统，可以用于无人驾驶定位场景。"}], "prod_name": "佳缘科技", "HotNum": 1317}, {"StockID": "300671", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司主要产品为 LED 屏控制及驱动、 MOSFET、 MCU、快充协议芯片，以及各类 ASIC 等类芯片，可应用于汽车电子领域。"}], "prod_name": "富满微", "HotNum": 888}, {"StockID": "603290", "Tag": [{"ID": "2909", "Name": "功率半导体", "Reason": "公司主营业务是以IGBT为主的功率半导体芯片和模块的设计研发和生产，并以IGBT模块形式对外实现销售。"}], "prod_name": "斯达半导", "HotNum": 545}, {"StockID": "605111", "Tag": [{"ID": "2909", "Name": "功率半导体", "Reason": "公司的主营业务为MOSFET、IGBT 等半导体芯片和功率器件的研发设计及销售。"}], "prod_name": "新洁能  ", "HotNum": 594}, {"StockID": "600460", "Tag": [{"ID": "2909", "Name": "功率半导体", "Reason": "公司应用于新能源汽车的IGBT模块（PIM）已在部分客户开始批量供货。"}], "prod_name": "士兰微  ", "HotNum": 1394}, {"StockID": "688187", "Tag": [{"ID": "2909", "Name": "功率半导体", "Reason": "公司形成了自主可控的高压 IGBT 技术体系，研制出 1700V-6500V 系列高压高电流密度 IGBT 产品，已大批量应用于轨道交通与电网领域。"}], "prod_name": "时代电气", "HotNum": 453}, {"StockID": "688711", "Tag": [{"ID": "2909", "Name": "功率半导体", "Reason": "全资子公司宏微科技主要为比亚迪海豹，海豚，元PLUS系列车型提供GV车规级IGBT，为比亚迪秦EV，比亚迪元EV系列车型提供GWB车规级IGBT"}], "prod_name": "宏微科技", "HotNum": 345}, {"StockID": "688396", "Tag": [{"ID": "2909", "Name": "功率半导体", "Reason": "公司是国内最大的半导体芯片公司之一，生产的MOSFET，IGBT产品广泛应用于比亚迪等国内汽车。"}], "prod_name": "华润微  ", "HotNum": 413}, {"StockID": "688052", "Tag": [{"ID": "2910", "Name": "传感器芯片", "Reason": "公司推出了符合汽车电子应用的磁线性电流传感器，已大规模发货；车规级的磁开关、磁轮速传感器等方向研发进展顺利，客户端已完成送样测试"}], "prod_name": "纳芯微  ", "HotNum": 521}, {"StockID": "688262", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司目前产品线包括汽车车身和网关控制、动力总成控制、域控制、新能源电池管理、车联网安全等多种汽车芯片"}], "prod_name": "国芯科技", "HotNum": 627}, {"StockID": "603121", "Tag": [{"ID": "2910", "Name": "传感器芯片", "Reason": "全资子公司盛美芯和参股中科阿尔法两家芯片设计公司，使得公司具备部分核心车规级芯片的自主设计，并提供提供车规级的核心敏感芯片"}], "prod_name": "XD华培动", "HotNum": 1607}, {"StockID": "603501", "Tag": [{"ID": "2910", "Name": "传感器芯片", "Reason": "通过与国际领先的汽车视觉技术公司开展方案合作，公司为后视摄像头（RVC）、 360 度环视系统（SVS）和电子后视镜提供了更高性价比的高质量图像解决方案，如OX08D10图像传感器等"}], "prod_name": "豪威集团", "HotNum": 1394}, {"StockID": "002077", "Tag": [{"ID": "2910", "Name": "传感器芯片", "Reason": "主要封装产品包括图像处理传感、生物识别传感、晶圆级 MEMS 和 5G 射频及电源等芯片，上述产品主要应用包含汽车等广泛领域"}], "prod_name": "大港股份", "HotNum": 1235}, {"StockID": "300223", "Tag": [{"ID": "2911", "Name": "存储类芯片", "Reason": "公司的存储芯片和模拟芯片均已在汽车领域量产销售，国际上主要的Tier1厂商如Continental、BOCSH、Valeo等均是公司客户。"}], "prod_name": "北京君正", "HotNum": 2513}, {"StockID": "603986", "Tag": [{"ID": "2911", "Name": "存储类芯片", "Reason": "公司 GD32A503 系列车规级 MCU 产品市场拓展稳步推进，并进一步推出GD32A490 系列高性能车规级 MCU 新品"}], "prod_name": "兆易创新", "HotNum": 2433}, {"StockID": "003026", "Tag": [{"ID": "2912", "Name": "其他", "Reason": "公司产品广泛运用于汽车电子，并正在进行车用半导体功率器件以及芯片的投产项目。"}], "prod_name": "中晶科技", "HotNum": 5262}, {"StockID": "603005", "Tag": [{"ID": "2912", "Name": "其他", "Reason": "公司是全球车规CIS芯片晶圆级TSV封装技术的开发者"}], "prod_name": "晶方科技", "HotNum": 2234}, {"StockID": "603160", "Tag": [{"ID": "2912", "Name": "其他", "Reason": "公司车规级触控芯片凭借高可靠性、优异 EMC 能力，以及从7 到 30+英寸屏幕的全覆盖品类， 2023 年出货量继续攀升"}], "prod_name": "汇顶科技", "HotNum": 797}, {"StockID": "301099", "Tag": [{"ID": "2912", "Name": "其他", "Reason": "参股公司欧创芯（60%股权）是一家模拟芯片研发商，起产品主要应用场景之一为汽车车灯后装市场"}], "prod_name": "雅创电子", "HotNum": 1194}, {"StockID": "688141", "Tag": [{"ID": "2912", "Name": "其他", "Reason": "公司拟开展的新业务为汽车级芯片先进封测技术研发，汽车级芯片的先进封装生产及测试服务，同时可覆盖通讯级、工业级等高端封测业务。"}], "prod_name": "杰华特  ", "HotNum": 480}, {"StockID": "688536", "Tag": [{"ID": "2912", "Name": "其他", "Reason": "公司发布 20 余款车规芯片，并相继顺利通过 AEC-Q100 认证，已陆续在客户端导入，公司可提供相对完整的模拟和电源管理元器件汽车级解决方案。"}], "prod_name": "思瑞浦  ", "HotNum": 588}, {"StockID": "605358", "Tag": [{"ID": "2912", "Name": "其他", "Reason": "公司于2016年通过国际一流汽车电子客户博世和大陆集团的体系认证，成为获得车载电源开关资格认证的肖特基二极管芯片供应商，目前实现大批量稳定供货。"}], "prod_name": "立昂微  ", "HotNum": 820}, {"StockID": "688135", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司目前涉及到的汽车电子芯片有MCU、多媒体主控芯片、传感器等领域"}], "prod_name": "利扬芯片", "HotNum": 953}, {"StockID": "300077", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司车载、车规MCU芯片产品当前已批量用于汽车后装行业"}], "prod_name": "国民技术", "HotNum": 2378}, {"StockID": "688380", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司发布车规级MCU BAT32A2系列，主要应用于汽车及高端工业市场，目前已在客户端应用开发项目中全面量产"}], "prod_name": "中微半导", "HotNum": 364}, {"StockID": "002049", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "车载控制器芯片为公司汽车电子主要产品之一，也是公司未来重要的业务方向之一"}], "prod_name": "紫光国微", "HotNum": 1914}, {"StockID": "600171", "Tag": [{"ID": "2909", "Name": "功率半导体", "Reason": "公司已有多款电源管理、功率器件（汽车点火IGBT和车载空调IGBT）汽车电子产品实现量产"}], "prod_name": "上海贝岭", "HotNum": 1977}, {"StockID": "600745", "Tag": [{"ID": "2909", "Name": "功率半导体", "Reason": "公司旗下 安世半导体是全球领先的分立与功率半导体IDM龙头厂商，是全球龙头的汽车半导体公司之一"}], "prod_name": "闻泰科技", "HotNum": 811}, {"StockID": "688595", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司车规级信号链 MCU 芯片已顺利通过AEC-Q100 一系列车规级认证。"}], "prod_name": "芯海科技", "HotNum": 395}, {"StockID": "300552", "Tag": [{"ID": "2910", "Name": "传感器芯片", "Reason": "公司布局的全固态激光雷达芯片解决现有激光雷达产品存在的低可靠性、难集成、高成本等一系列难题，可用于车载雷达。"}], "prod_name": "万集科技", "HotNum": 1264}, {"StockID": "688385", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "2021年底复旦微电子发布首款车用MCU产品"}], "prod_name": "复旦微电", "HotNum": 1118}, {"StockID": "600841", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "2023年年报，上汽红岩汽车有限公司为公司全资子公司。在新能源重卡研发方面，子公司红岩汽车有限公司具有纯电动重卡、燃料电池重卡和系统的开发能力，拥有集成智能化电池温控管理、AutoSAR 高标准底层架构、 ASIL D 控制器芯片等技术。"}], "prod_name": "动力新科", "HotNum": 1644}, {"StockID": "688486", "Tag": [{"ID": "2912", "Name": "其他", "Reason": "2023年7月4日投资者活动记录表：公司在汽车电子领域有多款桥接产品进入量产，已有6颗芯片通过了车规级体系AEC-Q100认证，公司已拓展终端客户包括宝马、博世、长安、比亚迪、理想等。"}], "prod_name": "龙迅股份", "HotNum": 481}, {"StockID": "300458", "Tag": [{"ID": "2908", "Name": "控制类芯片", "Reason": "公司车规级芯片已经实现大规模量产并在多个客户产品方案上落地，可为包括智能中控、智能后视镜、液晶仪表盘等各类智能驾驶舱应用提供支持。"}], "prod_name": "全志科技", "HotNum": 1288}, {"StockID": "301536", "Tag": [{"ID": "2912", "Name": "其他", "Reason": "2024年4月15日互动易回复：公司车规影像SoC芯片，主要用于国内主流自主品牌和合资品牌车厂，已大量出货。"}], "prod_name": "星宸科技", "HotNum": 1125}, {"StockID": "300613", "Tag": [{"ID": "2912", "Name": "其他", "Reason": "公司具备专业车规ISP+模拟视频链路芯片+车载DVR芯片，及一系列车载视频产品及解决方案"}], "prod_name": "富瀚微", "HotNum": 601}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 263, "ComNum": 582, "errcode": "0", "t": 0.011251000000000011}