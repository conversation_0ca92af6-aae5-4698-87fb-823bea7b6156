{"ID": "281", "Name": "智谱AI概念", "BriefIntro": "10月25日，在中国计算机大会（CNCC）上，北京大模型公司智谱研制的大模型智能交互智能体AutoGLM首次亮相并宣布开启内测，这个智能体不仅能动嘴皮子，还能真正帮人类做事——它可以通过语音指令理解用户意图并模拟人的操作，实现网页阅读、电商产品购买、点外卖、订酒店、评论和点赞朋友圈、发微信等行为。", "ClassLayer": "2", "Desc": "", "PlateSwitch": "2", "StkSwitch": "2", "Introduction": "<p>题材相关新闻</p><p><br/></p><p><strong style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;\"><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;\"><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\"></span><span style=\"color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255);\">10月25日，在中国计算机大会（CNCC）上，北京大模型公司智谱研制的大模型智能交互智能体AutoGLM首次亮相并宣布开启内测，这个智能体不仅能动嘴皮子，还能真正帮人类做事——它可以通过语音指令理解用户意图并模拟人的操作，实现网页阅读、电商产品购买、点外卖、订酒店、评论和点赞朋友圈、发微信等行为。</span><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\"></span></span></strong></p><p><br/></p><p><br/></p><p>题材相关介绍</p><p style=\"-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 8px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); line-height: 1.75em; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><strong style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;\">1、背景：</span></strong><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;\"><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\">今年年初，OpenAI被爆出将自研</span><span class=\"wx_search_keyword_wrap\" style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; color: var(--weui-LINK); cursor: default;\">AI Agent<em class=\"wx_search_keyword\" style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; display: inline-block; vertical-align: super; font-size: 10px; width: 1.2em; height: 1.2em; mask-position: 50% 50%; mask-repeat: no-repeat; mask-size: 100%; background-color: var(--weui-LINK); mask-image: url(&quot;data:image/svg+xml,%3csvg width=&#39;12&#39; height=&#39;12&#39; viewBox=&#39;0 0 12 12&#39; fill=&#39;none&#39; xmlns=&#39;http://www.w3.org/2000/svg&#39;%3e%3cpath fill-rule=&#39;evenodd&#39; clip-rule=&#39;evenodd&#39; d=&#39;M7.60772 8.29444C7.02144 8.73734 6.29139 9 5.5 9C3.567 9 2 7.433 2 5.5C2 3.567 3.567 2 5.5 2C7.433 2 9 3.567 9 5.5C9 6.28241 8.74327 7.00486 8.30946 7.5877C8.3183 7.59444 8.3268 7.60186 8.33488 7.60994L10.4331 9.70816L9.726 10.4153L7.62777 8.31704C7.62055 8.30983 7.61387 8.30228 7.60772 8.29444ZM8 5.5C8 6.88071 6.88071 8 5.5 8C4.11929 8 3 6.88071 3 5.5C3 4.11929 4.11929 3 5.5 3C6.88071 3 8 4.11929 8 5.5Z&#39; fill=&#39;%23576B95&#39;/%3e%3c/svg%3e&quot;);\"></em></span><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\">软件，它可替代人类，自动导航至任何网站并执行指定任务。10月25日，智谱AI推出相似产品——自主智能体AutoGLM，智谱将其称为是一个可模拟用户点击屏幕的手机操作助手，以及点击网页的浏览器助手。同时，智谱AI还推出了端到端情感语音模型GLM-4-Voice 。此外，苹果宣布Apple Intelligence的正式公开版本将于下周在iOS 18.1中上线。</span></span></p><p style=\"-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 8px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); line-height: 1.75em; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;\"><br style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\"/></span></p><p style=\"-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 8px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); line-height: 1.75em; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><strong style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px;\">2</span><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.034em;\">、AutoGLM基于OCR理解UI组件信息，通过链式思维训练理解组件功能。</span></strong><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.034em;\"></span><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.034em;\">根据清华大学发布的《AutoWebGLM: A Large Language Model-based Web Navigating Agent》，AutoWebGLM通过OCR模块解析页面截图并将其转化为易于LLM理解的HTML，帮助LLM理解UI功能及所处页面位置。</span><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.034em;\">通过GPT-4的链式思维推理，生成UI与执行动作对应意图的QA问答数据集，并对AutoGLM进行训练，使其可根据用户指令，确定用户意图所需UI组件，并自动完成相应操作。</span></p><p style=\"-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 8px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); line-height: 1.75em; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><br style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\"/></p><p style=\"-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 8px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); line-height: 1.75em; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><strong style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;\">3、自动驾驶仍需明确指令，个性化场景需与手机厂商合作，跨应用操作待改善。</span></strong><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;\"><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\">根据</span><span class=\"wx_search_keyword_wrap\" style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; color: var(--weui-LINK); cursor: default;\">数字生命卡兹克<em class=\"wx_search_keyword\" style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; display: inline-block; vertical-align: super; font-size: 10px; width: 1.2em; height: 1.2em; mask-position: 50% 50%; mask-repeat: no-repeat; mask-size: 100%; background-color: var(--weui-LINK); mask-image: url(&quot;data:image/svg+xml,%3csvg width=&#39;12&#39; height=&#39;12&#39; viewBox=&#39;0 0 12 12&#39; fill=&#39;none&#39; xmlns=&#39;http://www.w3.org/2000/svg&#39;%3e%3cpath fill-rule=&#39;evenodd&#39; clip-rule=&#39;evenodd&#39; d=&#39;M7.60772 8.29444C7.02144 8.73734 6.29139 9 5.5 9C3.567 9 2 7.433 2 5.5C2 3.567 3.567 2 5.5 2C7.433 2 9 3.567 9 5.5C9 6.28241 8.74327 7.00486 8.30946 7.5877C8.3183 7.59444 8.3268 7.60186 8.33488 7.60994L10.4331 9.70816L9.726 10.4153L7.62777 8.31704C7.62055 8.30983 7.61387 8.30228 7.60772 8.29444ZM8 5.5C8 6.88071 6.88071 8 5.5 8C4.11929 8 3 6.88071 3 5.5C3 4.11929 4.11929 3 5.5 3C6.88071 3 8 4.11929 8 5.5Z&#39; fill=&#39;%23576B95&#39;/%3e%3c/svg%3e&quot;);\"></em></span><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\">发布的实测视频，AutoGLM自动驾驶需要用户下达明确的指令信息，如预订酒店，需要用户给出时间、地点、预算及相应房型。我们认为AutoGLM执行如“帮我订回家的机票”等更个性化指令，仍需与手机厂商进行合作，获取用户个人信息使用权限方可实现。目前AutoGLM已可自动执行微信、淘宝、美团、小红书等App中用户常用操作，更多个性化操作尚未实现，并且未适配滴滴、京东、微信读书等App，未来需要更多第三方厂商以及更全面的UI训练数据集支持。</span></span></p><p style=\"-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 8px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); line-height: 1.75em; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><br style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\"/></p><p style=\"-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 8px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255); line-height: 1.75em; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><strong style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;\"><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;\">4、与荣耀等手机厂商深度合作，加速手机Agent落地，驱动AI手机渗透率提升。</span></strong><span style=\"-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;\">根据智谱，其在2024年9月与荣耀共同成立AI大模型技术联合实验室，与荣耀进行深度合作。2024年10月23日荣耀Magic 9.0发布会展示具备自动驾驶功能的AI Agent YOYO。具备自动驾驶功能的AI Agent可帮助用户摆脱繁琐的App操作，仅需语音指令即可满足用户需求，真正实现AI个人助理，有望驱动AI手机渗透率加速提升。</span></p><p><br/></p>", "CreateTime": "1730092217", "UpdateTime": "0", "Table": [{"Level1": {"ID": "3109", "Name": "参股", "ZSCode": "0", "Stocks": [{"StockID": "000917", "IsZz": "2", "IsHot": "0", "Reason": "公司旗下达晨创投于2021年初领投智谱AI A轮融资并多轮加注", "prod_name": "电广传媒", "Hot": 1355}, {"StockID": "002555", "IsZz": "2", "IsHot": "0", "Reason": "公司间接投资了智谱华章", "prod_name": "三七互娱", "Hot": 1030}, {"StockID": "688400", "IsZz": "2", "IsHot": "0", "Reason": "公司参股投资了智谱，截至目前公司持有智谱1.02%的股份", "prod_name": "凌云光  ", "Hot": 565}]}, "Level2": []}, {"Level1": {"ID": "3110", "Name": "合作", "ZSCode": "0", "Stocks": [{"StockID": "300925", "IsZz": "2", "IsHot": "0", "Reason": "公司与智谱AI建立了生态合作，建立起基础大模型、垂直大模型、Agent 行业应用的全栈能力", "prod_name": "法本信息", "Hot": 3245}, {"StockID": "603019", "IsZz": "2", "IsHot": "0", "Reason": "互动易：公司与智谱等 AI厂商合作，为人工智能产业化落地提供高性能算力资源保障", "prod_name": "中科曙光", "Hot": 3176}, {"StockID": "300674", "IsZz": "2", "IsHot": "0", "Reason": "2023年，公司与国产大模型公司北京智谱华章科技有限公司正式签署大模型合作协", "prod_name": "宇信科技", "Hot": 3120}, {"StockID": "000158", "IsZz": "2", "IsHot": "0", "Reason": "北明软件与智谱大模型有相关合作", "prod_name": "常山北明", "Hot": 2844}, {"StockID": "300688", "IsZz": "2", "IsHot": "0", "Reason": "公司与北京智谱华章科技有限公司签署《战略合作协议》，双方在政企大模型建设与落地、中小企业SaaS服务生态建设等反面开启紧密合作。", "prod_name": "创业黑马", "Hot": 2829}, {"StockID": "300047", "IsZz": "2", "IsHot": "0", "Reason": "公司已与智谱AI（清华大学）、北京大学等单位建立合作伙伴关系", "prod_name": "天源迪科", "Hot": 2558}, {"StockID": "300846", "IsZz": "2", "IsHot": "0", "Reason": "公司与智谱签订战略合作协议，合作涵盖算力集群建设、AI大模型商业化、海外市场拓展、大模型一体机研发、国产GPU适配及地方智算中心建设等", "prod_name": "首都在线", "Hot": 2470}, {"StockID": "605069", "IsZz": "2", "IsHot": "0", "Reason": "公司与智谱AI签署了战略合作协议，携手推进AI大模型在生态环保及水务领域的创新应用。", "prod_name": "正和生态", "Hot": 2410}, {"StockID": "000681", "IsZz": "2", "IsHot": "0", "Reason": "目前 AiPPT.cn-API 已经与联想、智谱、百度、Kimi 等各生态位的数百家客户输出 API 能力并达成生态合作", "prod_name": "视觉中国", "Hot": 2358}, {"StockID": "000034", "IsZz": "2", "IsHot": "0", "Reason": "公司获得智谱 AI 大模型合作伙伴计划-领航级合作伙伴认证、 智谱大模型合作伙伴联合解决方案DemoShow 现场方案评选第二名以及大模型合作伙伴联合解决方案-行业先锋奖", "prod_name": "神州数码", "Hot": 2049}, {"StockID": "601360", "IsZz": "2", "IsHot": "0", "Reason": "360 和智谱 AI 宣布达成战略合作，双方共同研发的千亿级大模型“360GLM”已具备新一代认知智能通用模型水准。", "prod_name": "三六零  ", "Hot": 1980}, {"StockID": "000032", "IsZz": "2", "IsHot": "0", "Reason": "公司与智谱 AI建立生态合作关系", "prod_name": "深桑达Ａ", "Hot": 1275}, {"StockID": "300010", "IsZz": "2", "IsHot": "0", "Reason": "公司与北京智谱华章《战略合作框架协议》  ， 成立合资公司专注于AI 教育产品的技术研发及销售", "prod_name": "豆神教育", "Hot": 1239}, {"StockID": "002712", "IsZz": "2", "IsHot": "0", "Reason": "公司与智谱华章签订战略合作协议", "prod_name": "思美传媒", "Hot": 962}, {"StockID": "300133", "IsZz": "2", "IsHot": "0", "Reason": "公司投资北京智谱华章科技有限公司并签署战略合作，将联合开发影视内容生成智能体以及影视内容审查垂直模型", "prod_name": "华策影视", "Hot": 930}, {"StockID": "300571", "IsZz": "2", "IsHot": "0", "Reason": "公司全资子公司与北京智谱华章科技有限公司签订战略合作协议，协议约定双方联合研究多模态具身大模型应用", "prod_name": "平治信息", "Hot": 846}, {"StockID": "300634", "IsZz": "2", "IsHot": "0", "Reason": "公司是清华智谱的战略合作伙伴", "prod_name": "彩讯股份", "Hot": 799}, {"StockID": "300825", "IsZz": "2", "IsHot": "0", "Reason": "公司融合英伟达、 北京智谱华章科技有限公司等各方优势，共同打造 AI 时代的整车研发数智平台", "prod_name": "阿尔特", "Hot": 527}, {"StockID": "601801", "IsZz": "2", "IsHot": "0", "Reason": "与智谱华章合作开发皖新阅读大模型", "prod_name": "皖新传媒", "Hot": 466}]}, "Level2": []}, {"Level1": {"ID": "3111", "Name": "接入", "ZSCode": "0", "Stocks": [{"StockID": "002657", "IsZz": "2", "IsHot": "0", "Reason": "公司已接入智谱ChatGLM中文对话大模型和语言模型", "prod_name": "中科金财", "Hot": 9955}, {"StockID": "837592", "IsZz": "2", "IsHot": "0", "Reason": "公司已采购智谱AI大模型", "prod_name": "DR华信永", "Hot": 2387}, {"StockID": "300229", "IsZz": "2", "IsHot": "0", "Reason": "公司可搭配智谱 AI 发布的 ChatGLM", "prod_name": "拓尔思", "Hot": 1376}, {"StockID": "300058", "IsZz": "2", "IsHot": "0", "Reason": "公司在与智谱等大模型厂商合作的基础上，构建了 AI 时代营销应用的数据底座，工作流底座和创作底座", "prod_name": "蓝色光标", "Hot": 1231}, {"StockID": "300830", "IsZz": "2", "IsHot": "0", "Reason": "公司团队已基于支持私有化部署的智谱华章ChatGLM4 大语言模型构建了低代码领域的专有大模型", "prod_name": "金现代", "Hot": 568}]}, "Level2": []}, {"Level1": {"ID": "3112", "Name": "服务", "ZSCode": "0", "Stocks": [{"StockID": "300657", "IsZz": "2", "IsHot": "0", "Reason": "公司为智谱华章提供必要的算力支持", "prod_name": "弘信电子", "Hot": 1674}, {"StockID": "603636", "IsZz": "2", "IsHot": "0", "Reason": "白泽政务大模型是南威在行业领域的重要积累和成果，该产品融合了智谱AI的底层技术，并在宝安区政府得到了实际应用", "prod_name": "南威软件", "Hot": 1539}, {"StockID": "688158", "IsZz": "2", "IsHot": "0", "Reason": "公司为智谱AI提供底层算力支持", "prod_name": "优刻得  ", "Hot": 1486}, {"StockID": "839493", "IsZz": "2", "IsHot": "0", "Reason": "智谱华章公司重要的主要大模型客户之一", "prod_name": "并行科技", "Hot": 877}, {"StockID": "002649", "IsZz": "2", "IsHot": "0", "Reason": "持续与智谱清言等国内外大模型厂商合作，提供一站式AI模型开发及推理服务", "prod_name": "博彦科技", "Hot": 850}]}, "Level2": []}], "Stocks": [], "StockList": [{"StockID": "300688", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司与北京智谱华章科技有限公司签署《战略合作协议》，双方在政企大模型建设与落地、中小企业SaaS服务生态建设等反面开启紧密合作。"}], "prod_name": "创业黑马", "HotNum": 2829}, {"StockID": "300047", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司已与智谱AI（清华大学）、北京大学等单位建立合作伙伴关系"}], "prod_name": "天源迪科", "HotNum": 2558}, {"StockID": "300846", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司与智谱签订战略合作协议，合作涵盖算力集群建设、AI大模型商业化、海外市场拓展、大模型一体机研发、国产GPU适配及地方智算中心建设等"}], "prod_name": "首都在线", "HotNum": 2470}, {"StockID": "300925", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司与智谱AI建立了生态合作，建立起基础大模型、垂直大模型、Agent 行业应用的全栈能力"}], "prod_name": "法本信息", "HotNum": 3245}, {"StockID": "300133", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司投资北京智谱华章科技有限公司并签署战略合作，将联合开发影视内容生成智能体以及影视内容审查垂直模型"}], "prod_name": "华策影视", "HotNum": 930}, {"StockID": "300571", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司全资子公司与北京智谱华章科技有限公司签订战略合作协议，协议约定双方联合研究多模态具身大模型应用"}], "prod_name": "平治信息", "HotNum": 846}, {"StockID": "002657", "Tag": [{"ID": "3111", "Name": "接入", "Reason": "公司已接入智谱ChatGLM中文对话大模型和语言模型"}], "prod_name": "中科金财", "HotNum": 9955}, {"StockID": "688158", "Tag": [{"ID": "3112", "Name": "服务", "Reason": "公司为智谱AI提供底层算力支持"}], "prod_name": "优刻得  ", "HotNum": 1486}, {"StockID": "688400", "Tag": [{"ID": "3109", "Name": "参股", "Reason": "公司参股投资了智谱，截至目前公司持有智谱1.02%的股份"}], "prod_name": "凌云光  ", "HotNum": 565}, {"StockID": "002712", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司与智谱华章签订战略合作协议"}], "prod_name": "思美传媒", "HotNum": 962}, {"StockID": "300634", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司是清华智谱的战略合作伙伴"}], "prod_name": "彩讯股份", "HotNum": 799}, {"StockID": "839493", "Tag": [{"ID": "3112", "Name": "服务", "Reason": "智谱华章公司重要的主要大模型客户之一"}], "prod_name": "并行科技", "HotNum": 877}, {"StockID": "605069", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司与智谱AI签署了战略合作协议，携手推进AI大模型在生态环保及水务领域的创新应用。"}], "prod_name": "正和生态", "HotNum": 2410}, {"StockID": "000917", "Tag": [{"ID": "3109", "Name": "参股", "Reason": "公司旗下达晨创投于2021年初领投智谱AI A轮融资并多轮加注"}], "prod_name": "电广传媒", "HotNum": 1355}, {"StockID": "837592", "Tag": [{"ID": "3111", "Name": "接入", "Reason": "公司已采购智谱AI大模型"}], "prod_name": "DR华信永", "HotNum": 2387}, {"StockID": "300229", "Tag": [{"ID": "3111", "Name": "接入", "Reason": "公司可搭配智谱 AI 发布的 ChatGLM"}], "prod_name": "拓尔思", "HotNum": 1376}, {"StockID": "300010", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司与北京智谱华章《战略合作框架协议》  ， 成立合资公司专注于AI 教育产品的技术研发及销售"}], "prod_name": "豆神教育", "HotNum": 1239}, {"StockID": "000034", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司获得智谱 AI 大模型合作伙伴计划-领航级合作伙伴认证、 智谱大模型合作伙伴联合解决方案DemoShow 现场方案评选第二名以及大模型合作伙伴联合解决方案-行业先锋奖"}], "prod_name": "神州数码", "HotNum": 2049}, {"StockID": "300825", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司融合英伟达、 北京智谱华章科技有限公司等各方优势，共同打造 AI 时代的整车研发数智平台"}], "prod_name": "阿尔特", "HotNum": 527}, {"StockID": "300830", "Tag": [{"ID": "3111", "Name": "接入", "Reason": "公司团队已基于支持私有化部署的智谱华章ChatGLM4 大语言模型构建了低代码领域的专有大模型"}], "prod_name": "金现代", "HotNum": 568}, {"StockID": "300058", "Tag": [{"ID": "3111", "Name": "接入", "Reason": "公司在与智谱等大模型厂商合作的基础上，构建了 AI 时代营销应用的数据底座，工作流底座和创作底座"}], "prod_name": "蓝色光标", "HotNum": 1231}, {"StockID": "002649", "Tag": [{"ID": "3112", "Name": "服务", "Reason": "持续与智谱清言等国内外大模型厂商合作，提供一站式AI模型开发及推理服务"}], "prod_name": "博彦科技", "HotNum": 850}, {"StockID": "000681", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "目前 AiPPT.cn-API 已经与联想、智谱、百度、Kimi 等各生态位的数百家客户输出 API 能力并达成生态合作"}], "prod_name": "视觉中国", "HotNum": 2358}, {"StockID": "601801", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "与智谱华章合作开发皖新阅读大模型"}], "prod_name": "皖新传媒", "HotNum": 466}, {"StockID": "000158", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "北明软件与智谱大模型有相关合作"}], "prod_name": "常山北明", "HotNum": 2844}, {"StockID": "000032", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "公司与智谱 AI建立生态合作关系"}], "prod_name": "深桑达Ａ", "HotNum": 1275}, {"StockID": "002555", "Tag": [{"ID": "3109", "Name": "参股", "Reason": "公司间接投资了智谱华章"}], "prod_name": "三七互娱", "HotNum": 1030}, {"StockID": "603019", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "互动易：公司与智谱等 AI厂商合作，为人工智能产业化落地提供高性能算力资源保障"}], "prod_name": "中科曙光", "HotNum": 3176}, {"StockID": "603636", "Tag": [{"ID": "3112", "Name": "服务", "Reason": "白泽政务大模型是南威在行业领域的重要积累和成果，该产品融合了智谱AI的底层技术，并在宝安区政府得到了实际应用"}], "prod_name": "南威软件", "HotNum": 1539}, {"StockID": "300674", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "2023年，公司与国产大模型公司北京智谱华章科技有限公司正式签署大模型合作协"}], "prod_name": "宇信科技", "HotNum": 3120}, {"StockID": "300657", "Tag": [{"ID": "3112", "Name": "服务", "Reason": "公司为智谱华章提供必要的算力支持"}], "prod_name": "弘信电子", "HotNum": 1674}, {"StockID": "601360", "Tag": [{"ID": "3110", "Name": "合作", "Reason": "360 和智谱 AI 宣布达成战略合作，双方共同研发的千亿级大模型“360GLM”已具备新一代认知智能通用模型水准。"}], "prod_name": "三六零  ", "HotNum": 1980}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 641, "ComNum": 594, "errcode": "0", "t": 0.009153000000000022}