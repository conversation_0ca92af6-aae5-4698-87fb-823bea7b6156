{"ID": "188", "Name": "文生视频概念", "BriefIntro": "2月16日凌晨，OpenAI在官网发布了创新性文生视频模型——Sora。", "ClassLayer": "1", "Desc": "", "PlateSwitch": "2", "StkSwitch": "2", "Introduction": "<p><strong>题材相关新闻：</strong></p><p><span style=\"color: rgb(35, 24, 21); font-family: 微软雅黑; text-align: justify; font-size: 18px;\">1、2月16日，OpenAI在其官网发布文生视频模型Sora。据介绍，该模型可以生成长达一分钟的视频，同时保持视觉品质并遵循用户提示。</span></p><p>2、<span style=\"background-color: rgb(255, 255, 255); color: rgb(34, 34, 34); font-family: arial; font-size: 18px;\">360创始人周鸿祎发布微博提到自己对Sora的看法，周鸿祎认为，Sora的诞生意味着AGI（通用人工智能）实现可能从10年缩短至一两年。</span></p><p><strong><span style=\"color:#222222;font-family:arial\"><span style=\"font-size: 18px; background-color: rgb(255, 255, 255);\">题材介绍：</span></span></strong></p><p>1、文生视频的定义：</p><p>1）文本生成视频/图像生成视频：输入几行文本或上传图像，就可以通过AI创建简短、高质量的视频；</p><p>2）视频-视频不同风格转换：将现有视频转换为不同的风格，包括不同的角色和对象，同时保持视频的结构；</p><p>3）扩展（Expand）：扩展视频的画布或宽高比，通过AI模型预测超出原始视频边界的内容，类似于Midjourney对于图片的“外画”功能；</p><p>4）实时编辑和修改：使用AI编辑视频内容，直接在视频中添加想要的素材，比如更换衣服、添加另一个角色、更改环境或添加道具；</p><p>5）延长（Extend）：使用AI扩展现有视频剪辑的长度。</p><p><span style=\"color:#222222;font-family:arial\"><span style=\"font-size: 18px; background-color: rgb(255, 255, 255);\">2、Sora模型展示出的亮点：</span></span><strong><span style=\"color:#222222;font-family:arial\"><span style=\"font-size: 18px; background-color: rgb(255, 255, 255);\"><br/></span></span></strong></p><p><span style=\"color:#222222;font-family:arial\"><span style=\"font-size: 18px; background-color: rgb(255, 255, 255);\">1）</span></span>视频生成长度可达1分钟，远超其他文生视频模型</p><p>作为对比，目前视频生成领域热门的跑道模型最长可生成18秒的视频，Pika1.0最长可生成15秒的视频。</p><p>2）3D 空间的一致性</p><p>Sora 可以生成具有动态镜头运动的视频，随着摄像机的移动和旋转，人物和场景元素在三维空间中保持连贯的运动。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708222289925893.png\" title=\"1708221998673146.png\" alt=\"image.png\"/></p><p>3）长期连续性和物体持久性</p><p>Sora 通常能够有效地对短期和长期依赖关系进行建模例如可以保留人、动物和物体，即使它们被遮挡或离开镜头。并且可以在单个样本中生成同一角色的多个镜头，在整个视频中保持外观。</p><p>Sora解决了一个具有挑战性的问题，即确保一个主题即使暂时离开视野也能保持不变且能够向前或向后扩展视频，以及连接视频，可以使用 Sora 在两个输入视频之间逐渐插值，在具有完全不同主题和场景构图的视频之间创建无缝过渡。</p><p><span style=\"background-color: rgb(255, 255, 255); color: rgb(34, 34, 34); font-family: arial; font-size: 18px;\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708222289430806.png\" title=\"1708222095305710.png\" alt=\"image.png\"/><br/></span>4）模拟数字世界</p><p>Sora能够模拟人工过程，例如视频游戏。Sora可以同时通过基本策略控制Minecraf游戏中的玩家，同时还可以高保真地渲染世界及其动态。</p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202402/1708222289536994.png\" title=\"1708222153796112.png\" alt=\"image.png\"/></p>", "CreateTime": "1708146033", "UpdateTime": "0", "Table": [], "Stocks": [{"StockID": "002657", "IsZz": "2", "IsHot": "0", "Reason": "公司与微软中国就Sora召开业务合作研讨会，前期在文生视频方向已测试国内外多种模型，探索商业模式，已有积累。", "prod_name": "中科金财", "Hot": 9955}, {"StockID": "002467", "IsZz": "2", "IsHot": "1", "Reason": "263云通信基于Web3.0理念构建核心视频技术，目前陆续在3D虚拟视频、MR视频合成、AI音视频等视频类产品功能里使用。", "prod_name": "二六三", "Hot": 7786}, {"StockID": "300251", "IsZz": "2", "IsHot": "0", "Reason": "公司希望能够在AI的潮流里成为一个先行者，现在已经把AI植入到了动画制作的一些流程里。", "prod_name": "光线传媒", "Hot": 4587}, {"StockID": "300291", "IsZz": "2", "IsHot": "0", "Reason": "公司积极探索AI技术在影视制作领域更多的应用场景。", "prod_name": "百纳千成", "Hot": 3109}, {"StockID": "300364", "IsZz": "2", "IsHot": "0", "Reason": "中文在线接受调研时表示，公司拥有海量的IP未来或可运用sora或类似应用生成短剧/短视频，通过公司现有成熟短剧或短视频营销等商业路径进行规模化、商业化的变现。", "prod_name": "中文在线", "Hot": 2335}, {"StockID": "600571", "IsZz": "2", "IsHot": "0", "Reason": "Pika开发团队创始人之一郭文景系公司实际控制人郭华强先生女儿。", "prod_name": "信雅达  ", "Hot": 2067}, {"StockID": "300418", "IsZz": "2", "IsHot": "0", "Reason": "公司天工大模型在腾讯优图实验室联合厦门大学此前开展的多模态大语言模型测评中，综合得分排名第一，旗下Opera在海外已经推出了短视频功能。", "prod_name": "昆仑万维", "Hot": 1857}, {"StockID": "300067", "IsZz": "2", "IsHot": "0", "Reason": "100%控股上海亘聪信息科技有限公司旗下意间AI支持输入文字生成一段动态视频的能力", "prod_name": "安诺其", "Hot": 1461}, {"StockID": "300229", "IsZz": "2", "IsHot": "0", "Reason": "公司持续跟踪文生图、文生视频相关技术进展，后续将根据实际情况适时推出基于Diffusion Transformer架构的文生图和文生视频功能", "prod_name": "拓尔思", "Hot": 1376}, {"StockID": "300578", "IsZz": "2", "IsHot": "0", "Reason": "公司提前三年储备和布局了相关人工智能和元宇宙技术，包括3D图形引擎，3D智能渲染，3D数据资产，3D虚拟数字人和智能云硬件等", "prod_name": "会畅通讯", "Hot": 1302}, {"StockID": "300781", "IsZz": "2", "IsHot": "0", "Reason": "公司表示，按计划，公司AIGC项目团队将在二日进行文生视频功能的开发，等待时机成熟后投入公测。", "prod_name": "因赛集团", "Hot": 1179}, {"StockID": "603189", "IsZz": "2", "IsHot": "0", "Reason": "互动平台称公司持续加大在融合媒体智能化生产及视频 AI 行业应用领域的研发投入", "prod_name": "网达软件", "Hot": 1104}, {"StockID": "300624", "IsZz": "2", "IsHot": "0", "Reason": "2023年10月30日，公司旗下AI视频创作软件Wondershare Filmora 13全球发布。", "prod_name": "万兴科技", "Hot": 1061}, {"StockID": "600640", "IsZz": "2", "IsHot": "0", "Reason": "公司在文生视频方面，已开发基于大模型的AIGC数字内容生成平台，利用多模态技术，使用自有的版权内容，生成文字、音频、图片、视频等影视素材，进行内容的创作生产。", "prod_name": "国脉文化", "Hot": 952}, {"StockID": "301171", "IsZz": "2", "IsHot": "0", "Reason": "公司AIGC产品KreadoAI包含了多模态模型的融合，包括文本生成、图生图、文本生成视频、语音生成、声纹克隆、数字人生成等，也包括了文字到广告创意图片及视频的生成能力。", "prod_name": "易点天下", "Hot": 843}, {"StockID": "601599", "IsZz": "2", "IsHot": "0", "Reason": "公司目前正加大行业调研，积极探索AI新技术的各项应用，包括:部分库存剧的AI换脸，盘活库存;新剧的后期制作中应用相关技术。", "prod_name": "浙文影业", "Hot": 783}, {"StockID": "300079", "IsZz": "2", "IsHot": "0", "Reason": "公司的AI技术可对于设定的识别元素实现快速的识别整理并用AIGC生产单个视频，智能媒资结合先进的AI识别技术等，构建全新视音频服务。此外，公司拥有支持超高清视频的内容生产系统。", "prod_name": "数码视讯", "Hot": 758}, {"StockID": "301052", "IsZz": "2", "IsHot": "0", "Reason": "公司储备了一部主投主控的动画大电影，以中华传统经典为内核、AI漫画大模型深度赋能创作，对标《长安三万里》。", "prod_name": "果麦文化", "Hot": 667}, {"StockID": "688039", "IsZz": "2", "IsHot": "0", "Reason": "公司提供通过AI技术进行短视频、长视频的生产/加工等解决方案。包括AI智能剪辑；用AIGC的方式自动生成对应短视频。", "prod_name": "当虹科技", "Hot": 652}, {"StockID": "301313", "IsZz": "2", "IsHot": "0", "Reason": "公司在文本、图像、虚拟人等场景中将会应用AIGC技术，AIGC技术的运用提高了制作效率", "prod_name": "凡拓数创", "Hot": 573}, {"StockID": "300556", "IsZz": "2", "IsHot": "0", "Reason": "公司有CG技术与AI相结合的多种类型的创新型应用及输出。", "prod_name": "丝路视觉", "Hot": 569}, {"StockID": "300770", "IsZz": "2", "IsHot": "0", "Reason": "公司联营公司寰宇信任与pika、华为、中影一起成立了G!Lab电影工业化实验室，标志着电影工业化3.0代正式到来。", "prod_name": "新媒股份", "Hot": 346}], "StockList": [{"StockID": "300624", "Tag": [], "prod_name": "万兴科技", "HotNum": 1061}, {"StockID": "300418", "Tag": [], "prod_name": "昆仑万维", "HotNum": 1857}, {"StockID": "300364", "Tag": [], "prod_name": "中文在线", "HotNum": 2335}, {"StockID": "301052", "Tag": [], "prod_name": "果麦文化", "HotNum": 667}, {"StockID": "688039", "Tag": [], "prod_name": "当虹科技", "HotNum": 652}, {"StockID": "603189", "Tag": [], "prod_name": "网达软件", "HotNum": 1104}, {"StockID": "300578", "Tag": [], "prod_name": "会畅通讯", "HotNum": 1302}, {"StockID": "301313", "Tag": [], "prod_name": "凡拓数创", "HotNum": 573}, {"StockID": "002467", "Tag": [], "prod_name": "二六三", "HotNum": 7786}, {"StockID": "300251", "Tag": [], "prod_name": "光线传媒", "HotNum": 4587}, {"StockID": "300291", "Tag": [], "prod_name": "百纳千成", "HotNum": 3109}, {"StockID": "601599", "Tag": [], "prod_name": "浙文影业", "HotNum": 783}, {"StockID": "300770", "Tag": [], "prod_name": "新媒股份", "HotNum": 346}, {"StockID": "600571", "Tag": [], "prod_name": "信雅达  ", "HotNum": 2067}, {"StockID": "300556", "Tag": [], "prod_name": "丝路视觉", "HotNum": 569}, {"StockID": "301171", "Tag": [], "prod_name": "易点天下", "HotNum": 843}, {"StockID": "300781", "Tag": [], "prod_name": "因赛集团", "HotNum": 1179}, {"StockID": "600640", "Tag": [], "prod_name": "国脉文化", "HotNum": 952}, {"StockID": "300079", "Tag": [], "prod_name": "数码视讯", "HotNum": 758}, {"StockID": "300067", "Tag": [], "prod_name": "安诺其", "HotNum": 1461}, {"StockID": "002657", "Tag": [], "prod_name": "中科金财", "HotNum": 9955}, {"StockID": "300229", "Tag": [], "prod_name": "拓尔思", "HotNum": 1376}], "Power": 0, "Subscribe": 0, "ZT": {"002467": ["1", "9.95", "1751007782"]}, "IsGood": 0, "GoodNum": 603, "ComNum": 970, "errcode": "0", "t": 0.008292999999999995}