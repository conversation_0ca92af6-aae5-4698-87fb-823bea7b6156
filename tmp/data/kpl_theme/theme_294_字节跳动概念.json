{"ID": "294", "Name": "字节跳动概念", "BriefIntro": "据21财经报道，2024年字节跳动在AI领域投资达到800亿元，与BAT（百度、阿里巴巴、腾讯）AI投资金额总和（约1000亿元）基本处于同一量级。伴随豆包大模型持续扩容，日均Tokens快速增长，机构认为字节将会显著增加AI相关投资，而进一步提升产品矩阵的能力，形成良性的循环。", "ClassLayer": "3", "Desc": "", "PlateSwitch": "1", "StkSwitch": "2", "Introduction": "<p>题材相关新闻</p><p><span style=\"color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255);\"></span></p><p>2024年12月10日，字节跳动视频生成模型Pi­x­e­l­D­a­n­ce已在豆包电脑版正式开启内测，部分用户已开通体验入口。内测页面显示，用户每日可免费生成十支视频。根据量子位数据显示，截至11月底，豆包APP在2024年的累计用户规模已成功超越 1.6 亿，每日平均新增用户下载量稳定维持在80万，成为全球排名第二，国内排名第一的AI app。11月份，豆包APP DAU接近900万，增长率超过15%，远超第二名Ki­mi DAU 300万。</p><p>2024年12月10日，字节跳动旗下TikTok在官网和官方账号发布声明，称已向美国哥伦比亚特区巡回上诉法院提交了紧急动议，以阻止TikTok封禁法律生效。此前美国联邦上诉法院裁定，出于国家安全考虑，维持美国总统拜登签署的强制要求字节跳动公司在美国出售TikTok的法令。</p><p><span style=\"color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; letter-spacing: 0.544px; text-align: justify; background-color: rgb(255, 255, 255);\"></span><br/></p><p><br/></p><p>题材相关介绍</p><p><br/></p><p>一、字节跳动AI优势解读</p><p><br/></p><p>1、字节AI应用的市场表现</p><p>海外市场表现强劲:在美国地区的应用下载榜里，字节的AI版小红书登顶第一名，下载量比ChatGPT还高，在东南亚菲律宾排名也非常高，虽然在美国主打的是纯应用如A教育、AI小红书等，不是直接以大模型形式推广。</p><p>国内市场占据优势:在国内，豆包位于所有应用下载的前列，十月份时已达1000万日活，在A应用下载榜里，除豆包外很难找到其他排名靠前的大模型AI产品，在国内AI应用中占据统治地位，用户量在国内排第一，甚至很多家长的小孩都在使用。</p><p>2、字节做C端应用的优势</p><p>广告、算力与亏损承受力:做C端应用的核心优势有三点，即有广告(来自TkTok和抖音的流量)、有算力(国内买卡最激进，海外也买得较多)、能亏钱(节是非上市公司，可大规投入)。</p><p>投入力度与效果:字节一年收入将近1万亿，去年利润率30%，今年25%，5个点的差异主要来自A!和部分电商，可见其在国内A!投入力度最大，从榜单排名也能看到投入后的效果。</p><p>3、字节豆包的用户渗透率高渗透率表现:腾讯跟进字节，腾讯管家默认上AI助手。豆包的用户接受度高，如小孩对豆包中的智能体接受度很高，甚至家长希望其他终端也能用豆包模型。用户规模领先:字节在国内和海外的用户数都遥遥领先，资本开支投入也相对最大。</p><p>4、关注字节豆包的原因及催化剂</p><p>近期事件引发关注:昨天AI版小红书在海外登顶，12月19日将举办字节的火山引擎大会(FORCE原力大会)，会上主要讨论大模型、应用开发等，可能会有化剂出现。</p><p><br/></p><p>二、近期热门豆包简介</p><p><br/></p><p style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0px; margin-bottom: 0.859em; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\">抖音豆包是由字节跳动公司基于云雀模型开发的AI工具，它提供聊天机器人、写作助手以及英语学习助手等功能，可以回答各种问题并进行对话，帮助人们获取信息。豆包支持网页Web平台，iOS以及安卓平台，用户可以通过手机号、抖音或者Apple ID登录。豆包的成功不仅归功于其个人魅力，也离不开抖音平台的推广和算法机制的支持。</p><p style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0px; margin-bottom: 0.859em; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\">豆包的主要功能包括：</p><ol style=\"box-sizing: inherit; padding-inline-start: 2em; margin-top: 0.859em; font-size: 14px; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; letter-spacing: 0.5px; background-color: rgb(255, 255, 255);\" class=\" list-paddingleft-2\"><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">智能推荐</span>：豆包可能具备基于用户历史行为和偏好进行智能推荐的能力，帮助用户更快地找到他们感兴趣的内容或服务。</p></li><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">自然语言处理</span>：豆包使用自然语言处理技术来理解和处理用户的输入，使得用户可以以更自然的方式与工具进行交互，而不需要特定的指令或格式。</p></li><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">多语言支持</span>：考虑到抖音在全球范围内的普及，豆包可能支持多种语言，以满足不同国家和地区用户的需求。</p></li></ol><p style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0.859em; margin-bottom: 0.859em; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\">此外，豆包还具有以下特点：</p><ul style=\"box-sizing: inherit; padding-inline-start: 2em; margin-top: 0.859em; font-size: 14px; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; letter-spacing: 0.5px; background-color: rgb(255, 255, 255);\" class=\" list-paddingleft-2\"><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">界面设计</span>：豆包拥有简单清爽的界面设计，无需学习，用户一打开就可以使用。</p></li><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">语音输入功能</span>：豆包支持语音输入，识别准确，还支持不同的方言，让沟通更简单、更高效。</p></li><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">多种音色</span>：豆包提供了多种音色，能用自然、亲切的声音回答用户。</p></li><li><p><span style=\"box-sizing: inherit; font-weight: 700;\">AI机器人定制</span>：用户可以定制属于自己的AI机器人，选择喜欢的说话风格、技能和背景。</p></li></ul><p class=\"last-node\" style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0.859em; margin-bottom: 0px; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\">豆包的命名背后也有一个小故事，它的名字简单、好读、好记，体现了产品的“拟人化”设计原则，让用户感觉到产品有类似人的温度<span class=\"docQuote___YIW6w\" data-testid=\"msh-ref-entrance\" style=\"box-sizing: inherit; margin: 0px 4px; position: relative; top: 3px; cursor: pointer; user-select: none; font-size: 16px; width: 18px; height: 18px; display: inline-flex; align-items: center; justify-content: center; border-radius: 50%; color: var(--msh-chat-segment-quoteIcon-color); background-color: var(--msh-chat-segment-quoteIcon-bg);\"><span role=\"img\" class=\"anticon MuiBox-root css-0\" style=\"box-sizing: inherit; display: inline-flex; color: inherit; line-height: 0; text-align: center; vertical-align: -0.125em; text-rendering: optimizelegibility; -webkit-font-smoothing: antialiased; align-items: center;\"><svg width=\"1em\" height=\"1em\" fill=\"currentColor\" aria-hidden=\"true\" focusable=\"false\" class=\"\"><use xlink:href=\"#mshd-seg-quote\"></use></svg></span></span>。豆包的名字也寓意着“都包了”，即工作生活学习的需求都包了，符合其作为通用助手的产品愿景。</p><p class=\"last-node\" style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0.859em; margin-bottom: 0px; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202412/1734341667522176.png\" title=\"1733890826711856.png\" alt=\"1733890809610.png\"/></p><p class=\"last-node\" style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0.859em; margin-bottom: 0px; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202412/1734341667126963.png\" title=\"1733890871113738.png\" alt=\"image.png\"/></p><p class=\"last-node\" style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0.859em; margin-bottom: 0px; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202412/1734341667700444.png\" title=\"1733890884744118.png\" alt=\"image.png\"/></p><p class=\"last-node\" style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0.859em; margin-bottom: 0px; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\"><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202412/1734341667907704.png\" title=\"1733890975444048.png\" alt=\"image.png\"/><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202412/1734341667288658.png\" title=\"1733890907930805.png\" alt=\"image.png\"/></p><p class=\"last-node\" style=\"box-sizing: inherit; font-size: 14px; line-height: 24px; margin-top: 0.859em; margin-bottom: 0px; letter-spacing: 0.25px; max-width: 100%; overflow: auto hidden; color: rgb(6, 6, 7); font-family: -apple-system, blinkmacsystemfont, &quot;Helvetica Neue&quot;, helvetica, &quot;segoe ui&quot;, arial, roboto, &quot;PingFang SC&quot;, miui, &quot;Hiragino Sans GB&quot;, &quot;Microsoft Yahei&quot;, sans-serif; background-color: rgb(255, 255, 255); white-space: pre-wrap !important; word-break: break-word !important;\"><br/></p><p><br/></p>", "CreateTime": "1733887125", "UpdateTime": "0", "Table": [{"Level1": {"ID": "3231", "Name": "算力相关", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "3232", "Name": "算力提供", "ZSCode": "0", "Stocks": [{"StockID": "000977", "IsZz": "2", "IsHot": "0", "Reason": "公司在国内人工智能计算领域的市场份额超过60%,为今日头条等国内知名AI相关企业提供计算力支撑", "prod_name": "浪潮信息", "Hot": 2838}, {"StockID": "603887", "IsZz": "2", "IsHot": "0", "Reason": "公司的互联网数据中心业务逐步加深与字节跳动等头部互联网企业合作", "prod_name": "城地香江", "Hot": 1842}, {"StockID": "301157", "IsZz": "2", "IsHot": "0", "Reason": "公司向字节跳动自建及租赁的数据中心提供BMS产品及服务", "prod_name": "华塑科技", "Hot": 1555}, {"StockID": "300442", "IsZz": "2", "IsHot": "0", "Reason": "公司超60%算力提供给字节跳动", "prod_name": "润泽科技", "Hot": 1499}, {"StockID": "301085", "IsZz": "2", "IsHot": "0", "Reason": "公司算力基础设施综合服务包括技术运维服务、售后维保服务和交付实施服务三大类，为字节跳动等客户的数据中心提供算力基础设施综合服务", "prod_name": "亚康股份", "Hot": 909}, {"StockID": "300603", "IsZz": "2", "IsHot": "0", "Reason": "公司与字节跳动在数据中心业务方面有合作", "prod_name": "立昂技术", "Hot": 883}]}, {"ID": "3233", "Name": "光模块", "ZSCode": "801206", "Stocks": [{"StockID": "002281", "IsZz": "2", "IsHot": "0", "Reason": "字节跳动为公司主要客户之一", "prod_name": "光迅科技", "Hot": 2556}]}, {"ID": "3235", "Name": "液冷", "ZSCode": "0", "Stocks": [{"StockID": "603019", "IsZz": "2", "IsHot": "0", "Reason": "公司液冷产品已在多家金融、互联网、IDC等行业用户得到应用，如建设银行、字节等。", "prod_name": "中科曙光", "Hot": 3176}, {"StockID": "002837", "IsZz": "2", "IsHot": "0", "Reason": "公司2017年启动的秦淮数据怀来新媒体大数据产业基地项目提供了数百套XFlex间接蒸发冷却机组，怀来项目共五期中其中一、二期专为字节跳动所定制", "prod_name": "英维克", "Hot": 1986}, {"StockID": "300499", "IsZz": "2", "IsHot": "0", "Reason": "子公司高澜创新为字节跳动公司提供的12U浸没液冷模组", "prod_name": "高澜股份", "Hot": 896}]}, {"ID": "3236", "Name": "交换机", "ZSCode": "801780", "Stocks": [{"StockID": "301165", "IsZz": "2", "IsHot": "0", "Reason": "公司是字节跳动等头部互联网企业的数据中心交换机主流供应商。", "prod_name": "锐捷网络", "Hot": 1921}]}, {"ID": "3238", "Name": "服务器", "ZSCode": "801720", "Stocks": [{"StockID": "603296", "IsZz": "2", "IsHot": "0", "Reason": "公司在数据业务领域字节形成了较为深厚的合作，公司一直是核心供应商，具有从板卡到模组到AI服务器整机的全栈式服务能力。", "prod_name": "华勤技术", "Hot": 703}]}, {"ID": "3241", "Name": "其他", "ZSCode": "0", "Stocks": [{"StockID": "300068", "IsZz": "2", "IsHot": "0", "Reason": "公司为字节跳动数据中心提供后备电源支撑", "prod_name": "南都电源", "Hot": 1917}, {"StockID": "300017", "IsZz": "2", "IsHot": "0", "Reason": "为字节供应CDN，国内最大的独立CDN厂商", "prod_name": "网宿科技", "Hot": 746}]}]}, {"Level1": {"ID": "3200", "Name": "豆包概念", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "3201", "Name": "AI玩具", "ZSCode": "0", "Stocks": [{"StockID": "603236", "IsZz": "2", "IsHot": "0", "Reason": "公司称尚未接到字节跳动或相关玩具厂商的订单，公司正在与玩具厂商进行方案评估和洽谈，但尚未正式出货", "prod_name": "移远通信", "Hot": 3002}, {"StockID": "300638", "IsZz": "2", "IsHot": "0", "Reason": "广和通推出AI玩具大模型解决方案，该方案深度融合豆包等AI大模型、内置广和通Cat.1模组，助力智能玩具实现AI化升级。", "prod_name": "广和通", "Hot": 2451}, {"StockID": "002862", "IsZz": "2", "IsHot": "0", "Reason": "公司目前全力打造第二代精品AI玩具，将豆包等大模型装进玩具里，赋予传统玩具以生命力。", "prod_name": "实丰文化", "Hot": 2332}, {"StockID": "300493", "IsZz": "2", "IsHot": "0", "Reason": "公司与火山引擎边缘大模型合作应用有智能玩具、具身智能，并通过一站式智能玩具解决方案，助力 AI玩具市场拓展", "prod_name": "润欣科技", "Hot": 1997}, {"StockID": "300459", "IsZz": "2", "IsHot": "0", "Reason": "在互动易给问及智能陪伴方面与豆包有什么区别时回复称，公司的AI产品也调用了豆包系列模型中的部分模型能力，为产品功能的完善提供支持", "prod_name": "汤姆猫", "Hot": 1037}, {"StockID": "688018", "IsZz": "2", "IsHot": "0", "Reason": "在字节跳动推出的AI玩具folotoy的文档里有esptool，esptool是乐鑫的", "prod_name": "乐鑫科技", "Hot": 683}]}, {"ID": "3202", "Name": "端测硬件", "ZSCode": "0", "Stocks": [{"StockID": "300496", "IsZz": "2", "IsHot": "0", "Reason": "公司与火山引擎达成深度合作,共建联合实验室赋能端侧智能", "prod_name": "中科创达", "Hot": 888}, {"StockID": "688332", "IsZz": "2", "IsHot": "0", "Reason": "公司讯龙三代BT895x芯片已被搭载于FIIL GS Links AI高音质开放式耳机，是市场上第二款支持豆包大模型AI的耳机产品", "prod_name": "中科蓝讯", "Hot": 508}]}, {"ID": "3218", "Name": "其他", "ZSCode": "0", "Stocks": [{"StockID": "002908", "IsZz": "2", "IsHot": "0", "Reason": "公司投入大量经费研发了民生行业大模型，并与字节跳动的豆包大语言模型进行合作", "prod_name": "德生科技", "Hot": 957}, {"StockID": "300494", "IsZz": "2", "IsHot": "0", "Reason": "公司旗下AI音乐社交产品给麦已经接入了豆包AI工具和MiniMax AI工具", "prod_name": "盛天网络", "Hot": 748}]}]}, {"Level1": {"ID": "3205", "Name": "服务商", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "3207", "Name": "广告营销", "ZSCode": "0", "Stocks": [{"StockID": "603598", "IsZz": "2", "IsHot": "0", "Reason": "公司重点围绕字节跳动等头部流量平台，为客户提供精准获客、高效增长的运营服务，提升广告投放效率，提高投入产出比，为客户带来投放价值提升", "prod_name": "引力传媒", "Hot": 2576}, {"StockID": "002115", "IsZz": "2", "IsHot": "0", "Reason": "子公司巨网科技连续取得字节跳动旗下巨量引擎等平台颁发的荣誉奖项，综合实力现已进入行业前五", "prod_name": "三维通信", "Hot": 2273}, {"StockID": "002803", "IsZz": "2", "IsHot": "0", "Reason": "公司与字节跳动合作模式为双向模式，一方面为字节跳动投放广告获取客户，另一方面也帮助其他广告主在字节跳动旗下的app如抖音上进行广告投放。", "prod_name": "吉宏股份", "Hot": 2191}, {"StockID": "002291", "IsZz": "2", "IsHot": "0", "Reason": "公司目前已获得抖音巨量千川服务商，抖音DP服务商等资质牌照", "prod_name": "遥望科技", "Hot": 2155}, {"StockID": "300063", "IsZz": "2", "IsHot": "0", "Reason": "公司为巨量引擎的综合代理商、巨量千川三星级服务商，代理巨量引擎旗下包括抖音客户端在内的多产品互联网广告营销业务", "prod_name": "天龙集团", "Hot": 1690}, {"StockID": "002400", "IsZz": "2", "IsHot": "0", "Reason": "公司为中国本土最优秀的大型综合性广告公司之一，与头条系保持着长期密切的合作关系，获得TikTok出海核心代理资质", "prod_name": "省广集团", "Hot": 1619}, {"StockID": "002279", "IsZz": "2", "IsHot": "0", "Reason": "公司为字节跳动提供出海营销服务，同时也是是公司数字传播业务合作的媒介平台", "prod_name": "久其软件", "Hot": 1439}, {"StockID": "300058", "IsZz": "2", "IsHot": "0", "Reason": "Blue AI在与字节跳动等主流Maas及云厂商合作的基础上，发展出广告投放分析助手等全球化业务AI营销工具矩阵", "prod_name": "蓝色光标", "Hot": 1231}, {"StockID": "002712", "IsZz": "2", "IsHot": "0", "Reason": "公司旗下八方腾泰等多家子公司是字节跳动旗下巨量引擎的综合代理商、巨量引擎的巨量千川服务商等", "prod_name": "思美传媒", "Hot": 962}, {"StockID": "301171", "IsZz": "2", "IsHot": "0", "Reason": "公司作为致力于推进我国企业国际化进程的互联网营销服务商，与Google、字节跳动等全球主流互联网媒体或其代理商建立了稳定良好的合作关系。", "prod_name": "易点天下", "Hot": 843}, {"StockID": "300242", "IsZz": "2", "IsHot": "0", "Reason": "公司为国内基于手机厂商广告商业化代理资质最全的供应商，字节跳动为公司客户之一", "prod_name": "佳云科技", "Hot": 788}, {"StockID": "002663", "IsZz": "2", "IsHot": "0", "Reason": "公司已与华为、京东、天音、腾讯、OPPO、阿里巴巴、字节跳动等优质产业客户达成合作，开辟了经营发展的新路径", "prod_name": "普邦股份", "Hot": 642}, {"StockID": "002591", "IsZz": "2", "IsHot": "0", "Reason": "公司全资子公司武汉飞游和长沙聚丰有为百度、腾讯、字节跳动等互联网公司提供了互联网产品营销服务。", "prod_name": "恒大高新", "Hot": 617}]}, {"ID": "3209", "Name": "数据提供", "ZSCode": "0", "Stocks": [{"StockID": "002122", "IsZz": "2", "IsHot": "0", "Reason": "旗下产品Enable为字节提供AI应用开发全生命周期的数据服务", "prod_name": "汇洲智能", "Hot": 4195}, {"StockID": "300766", "IsZz": "2", "IsHot": "0", "Reason": "公司增长服务以广点通、头条巨量引擎、芒果tv等流量为基础，新增接入多个第三方流量平台，同时依托公司精准大数据面向广告主提供全新的投放推广业务；", "prod_name": "每日互动", "Hot": 3647}, {"StockID": "603533", "IsZz": "2", "IsHot": "0", "Reason": "字节跳动与公司已开展用户画像、内容、广告、AI 创作数据合作", "prod_name": "掌阅科技", "Hot": 2767}, {"StockID": "301316", "IsZz": "2", "IsHot": "0", "Reason": "公司为豆包大模型做标注工作，通过专业的数据标注团队和流程，对大量的数据进行标注，为豆包大模型的训练提供了高质量、准确的数据基础", "prod_name": "慧博云通", "Hot": 1952}, {"StockID": "688787", "IsZz": "2", "IsHot": "0", "Reason": "字节跳动一直以来是公司的重要客户，公司为其提供智能语音、计算机视觉以及自然语言等各类数据产品或服务", "prod_name": "XD海天瑞", "Hot": 618}, {"StockID": "688615", "IsZz": "2", "IsHot": "0", "Reason": "字节为公司五大客户之一，为其提供互联网广告推广、商业大数据B端服务", "prod_name": "合合信息", "Hot": 422}]}, {"ID": "3215", "Name": "代理运营", "ZSCode": "0", "Stocks": [{"StockID": "002878", "IsZz": "2", "IsHot": "0", "Reason": "公司拥有巨量引擎平台综合代理商及星图代理商资质", "prod_name": "元隆雅图", "Hot": 9806}, {"StockID": "002799", "IsZz": "2", "IsHot": "0", "Reason": "子公司领凯科技为今日头条、腾讯、快手等的核心代理商，近年来主要为今日头条、抖音、淘宝、支付宝、网易、搜狐、当当网、高德地图、车主邦等知名APP提供互联网广告精准营销服务", "prod_name": "环球印务", "Hot": 5002}, {"StockID": "002131", "IsZz": "2", "IsHot": "0", "Reason": "公司媒体代理事业群，已与字节巨量引擎等多个头部媒体平台建立了深度的合作关系", "prod_name": "利欧股份", "Hot": 3983}, {"StockID": "002354", "IsZz": "2", "IsHot": "0", "Reason": "公司控股子公司山西鹏景科技有限公司已获得今日头条授权的巨量引擎全国地区除独代范围以外综合代理商、巨量千川电商营销服务商资质", "prod_name": "天娱数科", "Hot": 3190}, {"StockID": "301110", "IsZz": "2", "IsHot": "0", "Reason": "公司自2021年开始已经重点拓展了抖音渠道的代运营业务合作", "prod_name": "青木科技", "Hot": 2884}, {"StockID": "300921", "IsZz": "2", "IsHot": "0", "Reason": "南凌科技与火山引擎在多个领域开展合作，是火山引擎全线产品的代理商", "prod_name": "南凌科技", "Hot": 2058}, {"StockID": "603825", "IsZz": "2", "IsHot": "0", "Reason": "公司与字节跳动Byte Dance签署出海合作协议，成为Tik Tok Ads出海核心代理", "prod_name": "华扬联众", "Hot": 1897}, {"StockID": "000676", "IsZz": "2", "IsHot": "0", "Reason": "公司全资子公司智度亦复是行业领先的营销服务供应商，其中包括巨量引擎代理授权等，全面覆盖互联网主流优质媒体", "prod_name": "智度股份", "Hot": 1880}, {"StockID": "301236", "IsZz": "2", "IsHot": "0", "Reason": "公司长期为字节跳动等行业头部企业提供大量数字化运营服务", "prod_name": "软通动力", "Hot": 1642}, {"StockID": "600986", "IsZz": "2", "IsHot": "0", "Reason": "公司取得头条、 快手、 腾讯等头部互联网媒体的核心代理资格", "prod_name": "浙文互联", "Hot": 1607}, {"StockID": "002995", "IsZz": "2", "IsHot": "0", "Reason": "公司及旗下子公司-玄武时代是字节跳动旗下巨量引擎的综合代理商、巨量千川服务商和抖音星图服务商", "prod_name": "天地在线", "Hot": 1343}, {"StockID": "300781", "IsZz": "2", "IsHot": "0", "Reason": "公司已于巨量引擎签署了代理商数据推广商务合作协议，包括：头条、抖音等框架协议", "prod_name": "因赛集团", "Hot": 1179}, {"StockID": "603535", "IsZz": "2", "IsHot": "0", "Reason": "嘉诚国际物流股份有限公司近日收到字节跳动跨境电商的运营公司发送的《中标通知书》，公司中标甲方入库质检及分拨仓储作业运营服务", "prod_name": "嘉诚国际", "Hot": 968}, {"StockID": "605168", "IsZz": "2", "IsHot": "0", "Reason": "公司主要通过商务洽谈的方式对外采购互联网媒体资源，主要包括字节跳动等互联网媒体的多种形式广告资源", "prod_name": "三人行  ", "Hot": 694}, {"StockID": "300292", "IsZz": "2", "IsHot": "0", "Reason": "2019年公司推动广告在巨量引擎（今日头条、 抖音、西瓜、火山等平台广告资源的运营方）的授权代理商行列中规模及地位稳步提升", "prod_name": "吴通控股", "Hot": 541}, {"StockID": "300785", "IsZz": "2", "IsHot": "0", "Reason": "公司依托旗下子公司日晟星罗将代运营业务重点转向抖音平台。日晟星罗已持有巨量引擎综合代理商、巨量千川服务商、巨量星图代理商、营销科学服务商、FACTOR认证服务商等关键资质", "prod_name": "值得买", "Hot": 536}, {"StockID": "300612", "IsZz": "2", "IsHot": "0", "Reason": "公司控股子公司天津星言云汇网络科技有限公司，是巨量引擎广告代理综合代理商、巨量千川平台电商营销服务商。", "prod_name": "宣亚国际", "Hot": 532}]}, {"ID": "3216", "Name": "金融服务", "ZSCode": "0", "Stocks": [{"StockID": "300061", "IsZz": "2", "IsHot": "0", "Reason": "公司与中国银联等众多大中型银行和大型金融机构达成合作，并成为抖音等众多大型互联网平台的优质服务商", "prod_name": "旗天科技", "Hot": 2375}, {"StockID": "300465", "IsZz": "2", "IsHot": "0", "Reason": "公司为火山引擎生态合作伙伴，并于2023年底实现了在金融客户端的大数据领域的行业应用。", "prod_name": "高伟达", "Hot": 2114}]}, {"ID": "3213", "Name": "其他", "ZSCode": "0", "Stocks": [{"StockID": "003029", "IsZz": "2", "IsHot": "0", "Reason": "公司在互动平台称，公司为字节跳动公司全平台提供密码相关服务，协助字节跳动公司服务其全球用户", "prod_name": "吉大正元", "Hot": 41135}, {"StockID": "300925", "IsZz": "2", "IsHot": "0", "Reason": "字节跳动为公司客户", "prod_name": "法本信息", "Hot": 3245}, {"StockID": "301316", "IsZz": "2", "IsHot": "0", "Reason": "公司为字节跳动提供信息技术服务与 BPO 服务，包括软件开发与测试、数字化运营等", "prod_name": "慧博云通", "Hot": 1952}, {"StockID": "301165", "IsZz": "2", "IsHot": "0", "Reason": "2024年公司中标的字节跳动智算中心建设项目陆续供货交付。", "prod_name": "锐捷网络", "Hot": 1921}, {"StockID": "300959", "IsZz": "2", "IsHot": "0", "Reason": "公司移动信息服务和数字营销业务均与字节跳动相关公司存在业务往来。", "prod_name": "线上线下", "Hot": 1769}, {"StockID": "300657", "IsZz": "2", "IsHot": "0", "Reason": "2024年10月22日，公司在互动平台披露，其与字节跳动在的合作主要在消费电子领域展开", "prod_name": "弘信电子", "Hot": 1674}, {"StockID": "300475", "IsZz": "2", "IsHot": "0", "Reason": "公司代理的是应用于服务器级别的存储产品，下游客户主要是腾讯、阿里、字节等大型互联网企业和云厂商。", "prod_name": "香农芯创", "Hot": 1213}, {"StockID": "301001", "IsZz": "2", "IsHot": "0", "Reason": "公司发展至今，取得巨量引擎营销科学认证服务商等", "prod_name": "凯淳股份", "Hot": 1199}, {"StockID": "301382", "IsZz": "2", "IsHot": "0", "Reason": "公司为众多知名 APP 提供运营服务，包括运营商系、腾讯系、字节跳动系", "prod_name": "蜂助手", "Hot": 1160}, {"StockID": "300459", "IsZz": "2", "IsHot": "0", "Reason": "公司与字节跳动旗下穿山甲等全球多家知名广告营销服务商开展合作", "prod_name": "汤姆猫", "Hot": 1037}, {"StockID": "002587", "IsZz": "2", "IsHot": "0", "Reason": "公司控股子公司创想数维已于近期拿下抖音生活服务的服务商牌照。", "prod_name": "奥拓电子", "Hot": 916}, {"StockID": "002649", "IsZz": "2", "IsHot": "0", "Reason": "字节跳动是公司的重要客户，我们一直在多个业务领域进行紧密合作。", "prod_name": "博彦科技", "Hot": 850}, {"StockID": "300231", "IsZz": "2", "IsHot": "0", "Reason": "2019年1月3日在互动平台称：公司为今日头条和美团提供的都是系统集成业务服务", "prod_name": "银信科技", "Hot": 690}, {"StockID": "301380", "IsZz": "2", "IsHot": "0", "Reason": "公司为字节、阿里巴巴、京东旗下公司提供移动信息化服务。", "prod_name": "挖金客", "Hot": 668}, {"StockID": "300560", "IsZz": "2", "IsHot": "0", "Reason": "公司主要以抖音平台/今日头条等数字化营销服务平台的代理业务为基础，为客户提供数据营销服务。", "prod_name": "中富通", "Hot": 605}, {"StockID": "301270", "IsZz": "2", "IsHot": "0", "Reason": "部分字库软件授权业务的客户还将公司的字库软件用于嵌入式用途， 如字节跳动旗下全部 APP", "prod_name": "汉仪股份", "Hot": 551}, {"StockID": "300182", "IsZz": "2", "IsHot": "0", "Reason": "公司已与字节跳动建立了合作关系，是字节跳动的影视版权重要供应商之一。", "prod_name": "捷成股份", "Hot": 496}, {"StockID": "301291", "IsZz": "2", "IsHot": "0", "Reason": "中标互联网头部企业字节跳动的火山引擎数据中心项目，将提供电力模块等输配电产品。", "prod_name": "明阳电气", "Hot": 445}]}]}, {"Level1": {"ID": "3206", "Name": "合作商", "ZSCode": "0", "Stocks": []}, "Level2": [{"ID": "3221", "Name": "扣子", "ZSCode": "0", "Stocks": [{"StockID": "300785", "IsZz": "2", "IsHot": "0", "Reason": "在扣子专业版上显示值得买为合作伙伴", "prod_name": "值得买", "Hot": 536}]}, {"ID": "3210", "Name": "抖音", "ZSCode": "0", "Stocks": [{"StockID": "300071", "IsZz": "2", "IsHot": "0", "Reason": "公司存在与字节跳动、头条系公司的合作，主要是与抖音平台进行合作。", "prod_name": "福石控股", "Hot": 1277}, {"StockID": "600556", "IsZz": "2", "IsHot": "0", "Reason": "公司成为TikTok北美直营业务的首批合作伙伴之一", "prod_name": "天下秀  ", "Hot": 1257}, {"StockID": "300058", "IsZz": "2", "IsHot": "0", "Reason": "蓝色光标是2018年最早的抖音海外版Tiktok官方出海代理商之一", "prod_name": "蓝色光标", "Hot": 1231}, {"StockID": "603602", "IsZz": "2", "IsHot": "0", "Reason": "公司为tiktok美区的服务商，主要涉及达人营销以及跨境商品销售业务。", "prod_name": "纵横通信", "Hot": 600}, {"StockID": "601366", "IsZz": "2", "IsHot": "0", "Reason": "公司下属子公司与抖音有开展相关业务合作", "prod_name": "利群股份", "Hot": 339}]}, {"ID": "3211", "Name": "AI眼镜", "ZSCode": "801852", "Stocks": [{"StockID": "300622", "IsZz": "2", "IsHot": "0", "Reason": "公司在智能眼镜业务已与字节关联公司投资的李未可开展合作", "prod_name": "博士眼镜", "Hot": 5073}, {"StockID": "300975", "IsZz": "2", "IsHot": "0", "Reason": "公司为字节跳动子公司供应电子元器件产品，用于制造VR终端产品，互动易称公司AI眼镜仍在布局阶段", "prod_name": "商络电子", "Hot": 3516}, {"StockID": "301486", "IsZz": "2", "IsHot": "0", "Reason": "公司的精准定位控制器产品还应用于字节跳动 Pico 系列产品。", "prod_name": "致尚科技", "Hot": 2113}, {"StockID": "002899", "IsZz": "2", "IsHot": "0", "Reason": "公司与字节关联公司投资的李未可进行合作，为其现有产品线换代升级，包括Chat系列智能AI眼镜，S系列一体式AR智能眼镜", "prod_name": "英派斯", "Hot": 1681}, {"StockID": "300496", "IsZz": "2", "IsHot": "0", "Reason": "公司跟字节跳动主要在智能视觉方面进行合作。", "prod_name": "中科创达", "Hot": 888}]}, {"ID": "3212", "Name": "生态合作", "ZSCode": "0", "Stocks": [{"StockID": "000759", "IsZz": "2", "IsHot": "0", "Reason": "2023年11月29日互动易称公司与抖音本地生活服务合作", "prod_name": "中百集团", "Hot": 5238}, {"StockID": "000681", "IsZz": "2", "IsHot": "0", "Reason": "公司与字节跳动等互联网平台保持紧密合作，将图片、视频以及音乐等内容素材与更多的应用场景连接，为海量长尾用户赋能", "prod_name": "视觉中国", "Hot": 2358}, {"StockID": "603918", "IsZz": "2", "IsHot": "0", "Reason": "公司与抖音视界、清华大学等知名客户建立了深入的合作业务关系，并成功接入更多园区和校区系统。", "prod_name": "金桥信息", "Hot": 2349}, {"StockID": "300364", "IsZz": "2", "IsHot": "0", "Reason": "公司跟字节跳动旗下的番茄小说、番茄畅听均有合作", "prod_name": "中文在线", "Hot": 2335}, {"StockID": "300921", "IsZz": "2", "IsHot": "0", "Reason": "公司为字节火山引擎的优秀合作伙伴", "prod_name": "南凌科技", "Hot": 2058}, {"StockID": "300170", "IsZz": "2", "IsHot": "0", "Reason": "汉得联合火山引擎，邀请众多国际知名企业，共同举办了“AI 时代共话数字化征程”主题活动，并分享了基于豆包大模型的AI智能体应用案例", "prod_name": "汉得信息", "Hot": 1916}, {"StockID": "300781", "IsZz": "2", "IsHot": "0", "Reason": "公司自研多模态的营销AIGC应用大模型与豆包等知名大模型在AP I接口调用方面有合作", "prod_name": "因赛集团", "Hot": 1179}, {"StockID": "300634", "IsZz": "2", "IsHot": "0", "Reason": "公司与阿里、字节跳动分别在5G消息CSP管理平台及数据智能、企业直播、AR及视频内容制作等方面有合作", "prod_name": "彩讯股份", "Hot": 799}, {"StockID": "301551", "IsZz": "2", "IsHot": "0", "Reason": "公司搭建IPTV智能运营体系，与字节跳动合作开展IPTV智能推荐算法研究应用。", "prod_name": "无线传媒", "Hot": 573}, {"StockID": "300556", "IsZz": "2", "IsHot": "0", "Reason": "公司与字节跳动联合推出过《震撼吧！国乐》等一系列的优秀作品；", "prod_name": "丝路视觉", "Hot": 569}, {"StockID": "300413", "IsZz": "2", "IsHot": "0", "Reason": "芒果TV和抖音达成合作并联合发布“精品短剧扶持计划”，《大妈的世界贺岁篇》《小年兽与捉妖师》等短剧已在芒果TV、湖南卫视和抖音播出", "prod_name": "芒果超媒", "Hot": 399}, {"StockID": "688225", "IsZz": "2", "IsHot": "0", "Reason": "公司与字节的飞书正式签署合作伙伴框架协议，双方在产品层面完成深度融合以安全+数字办公形成了推动企业数字化升级的双翼动力。", "prod_name": "亚信安全", "Hot": 392}]}, {"ID": "3214", "Name": "其他", "ZSCode": "0", "Stocks": [{"StockID": "300921", "IsZz": "2", "IsHot": "0", "Reason": "公司为字节火山引擎的优秀合作伙伴", "prod_name": "南凌科技", "Hot": 2058}, {"StockID": "300299", "IsZz": "2", "IsHot": "0", "Reason": "公司游戏产品《仙境传说RO:新世代的诞生》和字节跳动相关企业进行合作、与其相关企业量子跃动共同增资盖姆艾尔、保持与其VR/AR 设备商务对接", "prod_name": "富春股份", "Hot": 1975}, {"StockID": "000785", "IsZz": "2", "IsHot": "0", "Reason": "2024年10月31投资者关系记录表，公司与火山引擎合作，基于Omniverse平台，推动数字资产格式互通、渲染效果优化及生成式AI能力的全面提升", "prod_name": "居然智家", "Hot": 1832}, {"StockID": "002148", "IsZz": "2", "IsHot": "0", "Reason": "公司在互动平台回复称，在游戏广告推广方面与今日头条存在合作", "prod_name": "北纬科技", "Hot": 1371}, {"StockID": "002919", "IsZz": "2", "IsHot": "0", "Reason": "公司收购喀什奥术网络科技有限公司100%股权，公告显示奥术与字节有业务合作", "prod_name": "名臣健康", "Hot": 701}, {"StockID": "301102", "IsZz": "2", "IsHot": "0", "Reason": "2023年11月22日互动易：今日头条是公司的合作客户", "prod_name": "兆讯传媒", "Hot": 367}]}]}], "Stocks": [], "StockList": [{"StockID": "603236", "Tag": [{"ID": "3201", "Name": "AI玩具", "Reason": "公司称尚未接到字节跳动或相关玩具厂商的订单，公司正在与玩具厂商进行方案评估和洽谈，但尚未正式出货"}], "prod_name": "移远通信", "HotNum": 3002}, {"StockID": "688018", "Tag": [{"ID": "3201", "Name": "AI玩具", "Reason": "在字节跳动推出的AI玩具folotoy的文档里有esptool，esptool是乐鑫的"}], "prod_name": "乐鑫科技", "HotNum": 683}, {"StockID": "002115", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "子公司巨网科技连续取得字节跳动旗下巨量引擎等平台颁发的荣誉奖项，综合实力现已进入行业前五"}], "prod_name": "三维通信", "HotNum": 2273}, {"StockID": "603598", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "公司重点围绕字节跳动等头部流量平台，为客户提供精准获客、高效增长的运营服务，提升广告投放效率，提高投入产出比，为客户带来投放价值提升"}], "prod_name": "引力传媒", "HotNum": 2576}, {"StockID": "002400", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "公司为中国本土最优秀的大型综合性广告公司之一，与头条系保持着长期密切的合作关系，获得TikTok出海核心代理资质"}], "prod_name": "省广集团", "HotNum": 1619}, {"StockID": "300058", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "Blue AI在与字节跳动等主流Maas及云厂商合作的基础上，发展出广告投放分析助手等全球化业务AI营销工具矩阵"}, {"ID": "3210", "Name": "抖音", "Reason": "蓝色光标是2018年最早的抖音海外版Tiktok官方出海代理商之一"}], "prod_name": "蓝色光标", "HotNum": 1231}, {"StockID": "688787", "Tag": [{"ID": "3209", "Name": "数据提供", "Reason": "字节跳动一直以来是公司的重要客户，公司为其提供智能语音、计算机视觉以及自然语言等各类数据产品或服务"}], "prod_name": "XD海天瑞", "HotNum": 618}, {"StockID": "000681", "Tag": [{"ID": "3212", "Name": "生态合作", "Reason": "公司与字节跳动等互联网平台保持紧密合作，将图片、视频以及音乐等内容素材与更多的应用场景连接，为海量长尾用户赋能"}], "prod_name": "视觉中国", "HotNum": 2358}, {"StockID": "603533", "Tag": [{"ID": "3209", "Name": "数据提供", "Reason": "字节跳动与公司已开展用户画像、内容、广告、AI 创作数据合作"}], "prod_name": "掌阅科技", "HotNum": 2767}, {"StockID": "300442", "Tag": [{"ID": "3232", "Name": "算力提供", "Reason": "公司超60%算力提供给字节跳动"}], "prod_name": "润泽科技", "HotNum": 1499}, {"StockID": "301085", "Tag": [{"ID": "3232", "Name": "算力提供", "Reason": "公司算力基础设施综合服务包括技术运维服务、售后维保服务和交付实施服务三大类，为字节跳动等客户的数据中心提供算力基础设施综合服务"}], "prod_name": "亚康股份", "HotNum": 909}, {"StockID": "688332", "Tag": [{"ID": "3202", "Name": "端测硬件", "Reason": "公司讯龙三代BT895x芯片已被搭载于FIIL GS Links AI高音质开放式耳机，是市场上第二款支持豆包大模型AI的耳机产品"}], "prod_name": "中科蓝讯", "HotNum": 508}, {"StockID": "300781", "Tag": [{"ID": "3212", "Name": "生态合作", "Reason": "公司自研多模态的营销AIGC应用大模型与豆包等知名大模型在AP I接口调用方面有合作"}, {"ID": "3215", "Name": "代理运营", "Reason": "公司已于巨量引擎签署了代理商数据推广商务合作协议，包括：头条、抖音等框架协议"}], "prod_name": "因赛集团", "HotNum": 1179}, {"StockID": "002354", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "公司控股子公司山西鹏景科技有限公司已获得今日头条授权的巨量引擎全国地区除独代范围以外综合代理商、巨量千川电商营销服务商资质"}], "prod_name": "天娱数科", "HotNum": 3190}, {"StockID": "002862", "Tag": [{"ID": "3201", "Name": "AI玩具", "Reason": "公司目前全力打造第二代精品AI玩具，将豆包等大模型装进玩具里，赋予传统玩具以生命力。"}], "prod_name": "实丰文化", "HotNum": 2332}, {"StockID": "300063", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "公司为巨量引擎的综合代理商、巨量千川三星级服务商，代理巨量引擎旗下包括抖音客户端在内的多产品互联网广告营销业务"}], "prod_name": "天龙集团", "HotNum": 1690}, {"StockID": "301157", "Tag": [{"ID": "3232", "Name": "算力提供", "Reason": "公司向字节跳动自建及租赁的数据中心提供BMS产品及服务"}], "prod_name": "华塑科技", "HotNum": 1555}, {"StockID": "300921", "Tag": [{"ID": "3212", "Name": "生态合作", "Reason": "公司为字节火山引擎的优秀合作伙伴"}, {"ID": "3215", "Name": "代理运营", "Reason": "南凌科技与火山引擎在多个领域开展合作，是火山引擎全线产品的代理商"}, {"ID": "3214", "Name": "其他", "Reason": "公司为字节火山引擎的优秀合作伙伴"}], "prod_name": "南凌科技", "HotNum": 2058}, {"StockID": "300242", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "公司为国内基于手机厂商广告商业化代理资质最全的供应商，字节跳动为公司客户之一"}], "prod_name": "佳云科技", "HotNum": 788}, {"StockID": "300496", "Tag": [{"ID": "3202", "Name": "端测硬件", "Reason": "公司与火山引擎达成深度合作,共建联合实验室赋能端侧智能"}, {"ID": "3211", "Name": "AI眼镜", "Reason": "公司跟字节跳动主要在智能视觉方面进行合作。"}], "prod_name": "中科创达", "HotNum": 888}, {"StockID": "301316", "Tag": [{"ID": "3209", "Name": "数据提供", "Reason": "公司为豆包大模型做标注工作，通过专业的数据标注团队和流程，对大量的数据进行标注，为豆包大模型的训练提供了高质量、准确的数据基础"}, {"ID": "3213", "Name": "其他", "Reason": "公司为字节跳动提供信息技术服务与 BPO 服务，包括软件开发与测试、数字化运营等"}], "prod_name": "慧博云通", "HotNum": 1952}, {"StockID": "300170", "Tag": [{"ID": "3212", "Name": "生态合作", "Reason": "汉得联合火山引擎，邀请众多国际知名企业，共同举办了“AI 时代共话数字化征程”主题活动，并分享了基于豆包大模型的AI智能体应用案例"}], "prod_name": "汉得信息", "HotNum": 1916}, {"StockID": "002291", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "公司目前已获得抖音巨量千川服务商，抖音DP服务商等资质牌照"}], "prod_name": "遥望科技", "HotNum": 2155}, {"StockID": "600556", "Tag": [{"ID": "3210", "Name": "抖音", "Reason": "公司成为TikTok北美直营业务的首批合作伙伴之一"}], "prod_name": "天下秀  ", "HotNum": 1257}, {"StockID": "002649", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "字节跳动是公司的重要客户，我们一直在多个业务领域进行紧密合作。"}], "prod_name": "博彦科技", "HotNum": 850}, {"StockID": "301001", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "公司发展至今，取得巨量引擎营销科学认证服务商等"}], "prod_name": "凯淳股份", "HotNum": 1199}, {"StockID": "002712", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "公司旗下八方腾泰等多家子公司是字节跳动旗下巨量引擎的综合代理商、巨量引擎的巨量千川服务商等"}], "prod_name": "思美传媒", "HotNum": 962}, {"StockID": "002663", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "公司已与华为、京东、天音、腾讯、OPPO、阿里巴巴、字节跳动等优质产业客户达成合作，开辟了经营发展的新路径"}], "prod_name": "普邦股份", "HotNum": 642}, {"StockID": "002995", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "公司及旗下子公司-玄武时代是字节跳动旗下巨量引擎的综合代理商、巨量千川服务商和抖音星图服务商"}], "prod_name": "天地在线", "HotNum": 1343}, {"StockID": "301551", "Tag": [{"ID": "3212", "Name": "生态合作", "Reason": "公司搭建IPTV智能运营体系，与字节跳动合作开展IPTV智能推荐算法研究应用。"}], "prod_name": "无线传媒", "HotNum": 573}, {"StockID": "600986", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "公司取得头条、 快手、 腾讯等头部互联网媒体的核心代理资格"}], "prod_name": "浙文互联", "HotNum": 1607}, {"StockID": "002919", "Tag": [{"ID": "3214", "Name": "其他", "Reason": "公司收购喀什奥术网络科技有限公司100%股权，公告显示奥术与字节有业务合作"}], "prod_name": "名臣健康", "HotNum": 701}, {"StockID": "301110", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "公司自2021年开始已经重点拓展了抖音渠道的代运营业务合作"}], "prod_name": "青木科技", "HotNum": 2884}, {"StockID": "002148", "Tag": [{"ID": "3214", "Name": "其他", "Reason": "公司在互动平台回复称，在游戏广告推广方面与今日头条存在合作"}], "prod_name": "北纬科技", "HotNum": 1371}, {"StockID": "601366", "Tag": [{"ID": "3210", "Name": "抖音", "Reason": "公司下属子公司与抖音有开展相关业务合作"}], "prod_name": "利群股份", "HotNum": 339}, {"StockID": "605168", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "公司主要通过商务洽谈的方式对外采购互联网媒体资源，主要包括字节跳动等互联网媒体的多种形式广告资源"}], "prod_name": "三人行  ", "HotNum": 694}, {"StockID": "002878", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "公司拥有巨量引擎平台综合代理商及星图代理商资质"}], "prod_name": "元隆雅图", "HotNum": 9806}, {"StockID": "300231", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "2019年1月3日在互动平台称：公司为今日头条和美团提供的都是系统集成业务服务"}], "prod_name": "银信科技", "HotNum": 690}, {"StockID": "300925", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "字节跳动为公司客户"}], "prod_name": "法本信息", "HotNum": 3245}, {"StockID": "301102", "Tag": [{"ID": "3214", "Name": "其他", "Reason": "2023年11月22日互动易：今日头条是公司的合作客户"}], "prod_name": "兆讯传媒", "HotNum": 367}, {"StockID": "300182", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "公司已与字节跳动建立了合作关系，是字节跳动的影视版权重要供应商之一。"}], "prod_name": "捷成股份", "HotNum": 496}, {"StockID": "300413", "Tag": [{"ID": "3212", "Name": "生态合作", "Reason": "芒果TV和抖音达成合作并联合发布“精品短剧扶持计划”，《大妈的世界贺岁篇》《小年兽与捉妖师》等短剧已在芒果TV、湖南卫视和抖音播出"}], "prod_name": "芒果超媒", "HotNum": 399}, {"StockID": "301486", "Tag": [{"ID": "3211", "Name": "AI眼镜", "Reason": "公司的精准定位控制器产品还应用于字节跳动 Pico 系列产品。"}], "prod_name": "致尚科技", "HotNum": 2113}, {"StockID": "300634", "Tag": [{"ID": "3212", "Name": "生态合作", "Reason": "公司与阿里、字节跳动分别在5G消息CSP管理平台及数据智能、企业直播、AR及视频内容制作等方面有合作"}], "prod_name": "彩讯股份", "HotNum": 799}, {"StockID": "300657", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "2024年10月22日，公司在互动平台披露，其与字节跳动在的合作主要在消费电子领域展开"}], "prod_name": "弘信电子", "HotNum": 1674}, {"StockID": "301270", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "部分字库软件授权业务的客户还将公司的字库软件用于嵌入式用途， 如字节跳动旗下全部 APP"}], "prod_name": "汉仪股份", "HotNum": 551}, {"StockID": "000977", "Tag": [{"ID": "3232", "Name": "算力提供", "Reason": "公司在国内人工智能计算领域的市场份额超过60%,为今日头条等国内知名AI相关企业提供计算力支撑"}], "prod_name": "浪潮信息", "HotNum": 2838}, {"StockID": "003029", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "公司在互动平台称，公司为字节跳动公司全平台提供密码相关服务，协助字节跳动公司服务其全球用户"}], "prod_name": "吉大正元", "HotNum": 41135}, {"StockID": "603602", "Tag": [{"ID": "3210", "Name": "抖音", "Reason": "公司为tiktok美区的服务商，主要涉及达人营销以及跨境商品销售业务。"}], "prod_name": "纵横通信", "HotNum": 600}, {"StockID": "300603", "Tag": [{"ID": "3232", "Name": "算力提供", "Reason": "公司与字节跳动在数据中心业务方面有合作"}], "prod_name": "立昂技术", "HotNum": 883}, {"StockID": "300622", "Tag": [{"ID": "3211", "Name": "AI眼镜", "Reason": "公司在智能眼镜业务已与字节关联公司投资的李未可开展合作"}], "prod_name": "博士眼镜", "HotNum": 5073}, {"StockID": "002899", "Tag": [{"ID": "3211", "Name": "AI眼镜", "Reason": "公司与字节关联公司投资的李未可进行合作，为其现有产品线换代升级，包括Chat系列智能AI眼镜，S系列一体式AR智能眼镜"}], "prod_name": "英派斯", "HotNum": 1681}, {"StockID": "002591", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "公司全资子公司武汉飞游和长沙聚丰有为百度、腾讯、字节跳动等互联网公司提供了互联网产品营销服务。"}], "prod_name": "恒大高新", "HotNum": 617}, {"StockID": "300975", "Tag": [{"ID": "3211", "Name": "AI眼镜", "Reason": "公司为字节跳动子公司供应电子元器件产品，用于制造VR终端产品，互动易称公司AI眼镜仍在布局阶段"}], "prod_name": "商络电子", "HotNum": 3516}, {"StockID": "300560", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "公司主要以抖音平台/今日头条等数字化营销服务平台的代理业务为基础，为客户提供数据营销服务。"}], "prod_name": "中富通", "HotNum": 605}, {"StockID": "300061", "Tag": [{"ID": "3216", "Name": "金融服务", "Reason": "公司与中国银联等众多大中型银行和大型金融机构达成合作，并成为抖音等众多大型互联网平台的优质服务商"}], "prod_name": "旗天科技", "HotNum": 2375}, {"StockID": "300465", "Tag": [{"ID": "3216", "Name": "金融服务", "Reason": "公司为火山引擎生态合作伙伴，并于2023年底实现了在金融客户端的大数据领域的行业应用。"}], "prod_name": "高伟达", "HotNum": 2114}, {"StockID": "300766", "Tag": [{"ID": "3209", "Name": "数据提供", "Reason": "公司增长服务以广点通、头条巨量引擎、芒果tv等流量为基础，新增接入多个第三方流量平台，同时依托公司精准大数据面向广告主提供全新的投放推广业务；"}], "prod_name": "每日互动", "HotNum": 3647}, {"StockID": "301236", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "公司长期为字节跳动等行业头部企业提供大量数字化运营服务"}], "prod_name": "软通动力", "HotNum": 1642}, {"StockID": "300612", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "公司控股子公司天津星言云汇网络科技有限公司，是巨量引擎广告代理综合代理商、巨量千川平台电商营销服务商。"}], "prod_name": "宣亚国际", "HotNum": 532}, {"StockID": "603918", "Tag": [{"ID": "3212", "Name": "生态合作", "Reason": "公司与抖音视界、清华大学等知名客户建立了深入的合作业务关系，并成功接入更多园区和校区系统。"}], "prod_name": "金桥信息", "HotNum": 2349}, {"StockID": "002908", "Tag": [{"ID": "3218", "Name": "其他", "Reason": "公司投入大量经费研发了民生行业大模型，并与字节跳动的豆包大语言模型进行合作"}], "prod_name": "德生科技", "HotNum": 957}, {"StockID": "603535", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "嘉诚国际物流股份有限公司近日收到字节跳动跨境电商的运营公司发送的《中标通知书》，公司中标甲方入库质检及分拨仓储作业运营服务"}], "prod_name": "嘉诚国际", "HotNum": 968}, {"StockID": "300475", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "公司代理的是应用于服务器级别的存储产品，下游客户主要是腾讯、阿里、字节等大型互联网企业和云厂商。"}], "prod_name": "香农芯创", "HotNum": 1213}, {"StockID": "603825", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "公司与字节跳动Byte Dance签署出海合作协议，成为Tik Tok Ads出海核心代理"}], "prod_name": "华扬联众", "HotNum": 1897}, {"StockID": "002803", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "公司与字节跳动合作模式为双向模式，一方面为字节跳动投放广告获取客户，另一方面也帮助其他广告主在字节跳动旗下的app如抖音上进行广告投放。"}], "prod_name": "吉宏股份", "HotNum": 2191}, {"StockID": "300785", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "公司依托旗下子公司日晟星罗将代运营业务重点转向抖音平台。日晟星罗已持有巨量引擎综合代理商、巨量千川服务商、巨量星图代理商、营销科学服务商、FACTOR认证服务商等关键资质"}, {"ID": "3221", "Name": "扣子", "Reason": "在扣子专业版上显示值得买为合作伙伴"}], "prod_name": "值得买", "HotNum": 536}, {"StockID": "002131", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "公司媒体代理事业群，已与字节巨量引擎等多个头部媒体平台建立了深度的合作关系"}], "prod_name": "利欧股份", "HotNum": 3983}, {"StockID": "002279", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "公司为字节跳动提供出海营销服务，同时也是是公司数字传播业务合作的媒介平台"}], "prod_name": "久其软件", "HotNum": 1439}, {"StockID": "300292", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "2019年公司推动广告在巨量引擎（今日头条、 抖音、西瓜、火山等平台广告资源的运营方）的授权代理商行列中规模及地位稳步提升"}], "prod_name": "吴通控股", "HotNum": 541}, {"StockID": "300556", "Tag": [{"ID": "3212", "Name": "生态合作", "Reason": "公司与字节跳动联合推出过《震撼吧！国乐》等一系列的优秀作品；"}], "prod_name": "丝路视觉", "HotNum": 569}, {"StockID": "300959", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "公司移动信息服务和数字营销业务均与字节跳动相关公司存在业务往来。"}], "prod_name": "线上线下", "HotNum": 1769}, {"StockID": "688615", "Tag": [{"ID": "3209", "Name": "数据提供", "Reason": "字节为公司五大客户之一，为其提供互联网广告推广、商业大数据B端服务"}], "prod_name": "合合信息", "HotNum": 422}, {"StockID": "301171", "Tag": [{"ID": "3207", "Name": "广告营销", "Reason": "公司作为致力于推进我国企业国际化进程的互联网营销服务商，与Google、字节跳动等全球主流互联网媒体或其代理商建立了稳定良好的合作关系。"}], "prod_name": "易点天下", "HotNum": 843}, {"StockID": "301380", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "公司为字节、阿里巴巴、京东旗下公司提供移动信息化服务。"}], "prod_name": "挖金客", "HotNum": 668}, {"StockID": "002587", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "公司控股子公司创想数维已于近期拿下抖音生活服务的服务商牌照。"}], "prod_name": "奥拓电子", "HotNum": 916}, {"StockID": "300364", "Tag": [{"ID": "3212", "Name": "生态合作", "Reason": "公司跟字节跳动旗下的番茄小说、番茄畅听均有合作"}], "prod_name": "中文在线", "HotNum": 2335}, {"StockID": "000676", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "公司全资子公司智度亦复是行业领先的营销服务供应商，其中包括巨量引擎代理授权等，全面覆盖互联网主流优质媒体"}], "prod_name": "智度股份", "HotNum": 1880}, {"StockID": "301382", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "公司为众多知名 APP 提供运营服务，包括运营商系、腾讯系、字节跳动系"}], "prod_name": "蜂助手", "HotNum": 1160}, {"StockID": "300459", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "公司与字节跳动旗下穿山甲等全球多家知名广告营销服务商开展合作"}, {"ID": "3201", "Name": "AI玩具", "Reason": "在互动易给问及智能陪伴方面与豆包有什么区别时回复称，公司的AI产品也调用了豆包系列模型中的部分模型能力，为产品功能的完善提供支持"}], "prod_name": "汤姆猫", "HotNum": 1037}, {"StockID": "300071", "Tag": [{"ID": "3210", "Name": "抖音", "Reason": "公司存在与字节跳动、头条系公司的合作，主要是与抖音平台进行合作。"}], "prod_name": "福石控股", "HotNum": 1277}, {"StockID": "300299", "Tag": [{"ID": "3214", "Name": "其他", "Reason": "公司游戏产品《仙境传说RO:新世代的诞生》和字节跳动相关企业进行合作、与其相关企业量子跃动共同增资盖姆艾尔、保持与其VR/AR 设备商务对接"}], "prod_name": "富春股份", "HotNum": 1975}, {"StockID": "002799", "Tag": [{"ID": "3215", "Name": "代理运营", "Reason": "子公司领凯科技为今日头条、腾讯、快手等的核心代理商，近年来主要为今日头条、抖音、淘宝、支付宝、网易、搜狐、当当网、高德地图、车主邦等知名APP提供互联网广告精准营销服务"}], "prod_name": "环球印务", "HotNum": 5002}, {"StockID": "300493", "Tag": [{"ID": "3201", "Name": "AI玩具", "Reason": "公司与火山引擎边缘大模型合作应用有智能玩具、具身智能，并通过一站式智能玩具解决方案，助力 AI玩具市场拓展"}], "prod_name": "润欣科技", "HotNum": 1997}, {"StockID": "688225", "Tag": [{"ID": "3212", "Name": "生态合作", "Reason": "公司与字节的飞书正式签署合作伙伴框架协议，双方在产品层面完成深度融合以安全+数字办公形成了推动企业数字化升级的双翼动力。"}], "prod_name": "亚信安全", "HotNum": 392}, {"StockID": "000759", "Tag": [{"ID": "3212", "Name": "生态合作", "Reason": "2023年11月29日互动易称公司与抖音本地生活服务合作"}], "prod_name": "中百集团", "HotNum": 5238}, {"StockID": "301165", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "2024年公司中标的字节跳动智算中心建设项目陆续供货交付。"}, {"ID": "3236", "Name": "交换机", "Reason": "公司是字节跳动等头部互联网企业的数据中心交换机主流供应商。"}], "prod_name": "锐捷网络", "HotNum": 1921}, {"StockID": "002122", "Tag": [{"ID": "3209", "Name": "数据提供", "Reason": "旗下产品Enable为字节提供AI应用开发全生命周期的数据服务"}], "prod_name": "汇洲智能", "HotNum": 4195}, {"StockID": "301291", "Tag": [{"ID": "3213", "Name": "其他", "Reason": "中标互联网头部企业字节跳动的火山引擎数据中心项目，将提供电力模块等输配电产品。"}], "prod_name": "明阳电气", "HotNum": 445}, {"StockID": "002281", "Tag": [{"ID": "3233", "Name": "光模块", "Reason": "字节跳动为公司主要客户之一"}], "prod_name": "光迅科技", "HotNum": 2556}, {"StockID": "300499", "Tag": [{"ID": "3235", "Name": "液冷", "Reason": "子公司高澜创新为字节跳动公司提供的12U浸没液冷模组"}], "prod_name": "高澜股份", "HotNum": 896}, {"StockID": "002837", "Tag": [{"ID": "3235", "Name": "液冷", "Reason": "公司2017年启动的秦淮数据怀来新媒体大数据产业基地项目提供了数百套XFlex间接蒸发冷却机组，怀来项目共五期中其中一、二期专为字节跳动所定制"}], "prod_name": "英维克", "HotNum": 1986}, {"StockID": "603019", "Tag": [{"ID": "3235", "Name": "液冷", "Reason": "公司液冷产品已在多家金融、互联网、IDC等行业用户得到应用，如建设银行、字节等。"}], "prod_name": "中科曙光", "HotNum": 3176}, {"StockID": "603887", "Tag": [{"ID": "3232", "Name": "算力提供", "Reason": "公司的互联网数据中心业务逐步加深与字节跳动等头部互联网企业合作"}], "prod_name": "城地香江", "HotNum": 1842}, {"StockID": "603296", "Tag": [{"ID": "3238", "Name": "服务器", "Reason": "公司在数据业务领域字节形成了较为深厚的合作，公司一直是核心供应商，具有从板卡到模组到AI服务器整机的全栈式服务能力。"}], "prod_name": "华勤技术", "HotNum": 703}, {"StockID": "300017", "Tag": [{"ID": "3241", "Name": "其他", "Reason": "为字节供应CDN，国内最大的独立CDN厂商"}], "prod_name": "网宿科技", "HotNum": 746}, {"StockID": "300068", "Tag": [{"ID": "3241", "Name": "其他", "Reason": "公司为字节跳动数据中心提供后备电源支撑"}], "prod_name": "南都电源", "HotNum": 1917}, {"StockID": "300638", "Tag": [{"ID": "3201", "Name": "AI玩具", "Reason": "广和通推出AI玩具大模型解决方案，该方案深度融合豆包等AI大模型、内置广和通Cat.1模组，助力智能玩具实现AI化升级。"}], "prod_name": "广和通", "HotNum": 2451}, {"StockID": "300494", "Tag": [{"ID": "3218", "Name": "其他", "Reason": "公司旗下AI音乐社交产品给麦已经接入了豆包AI工具和MiniMax AI工具"}], "prod_name": "盛天网络", "HotNum": 748}, {"StockID": "000785", "Tag": [{"ID": "3214", "Name": "其他", "Reason": "2024年10月31投资者关系记录表，公司与火山引擎合作，基于Omniverse平台，推动数字资产格式互通、渲染效果优化及生成式AI能力的全面提升"}], "prod_name": "居然智家", "HotNum": 1832}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 1489, "ComNum": 1125, "errcode": "0", "t": 0.007625999999999966}