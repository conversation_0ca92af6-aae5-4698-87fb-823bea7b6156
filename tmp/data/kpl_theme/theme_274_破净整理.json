{"ID": "274", "Name": "破净整理", "BriefIntro": "证监会将发布上市公司市值管理指引并征求意见。《指引》第八条和第九条就主要指数成份股公司和长期破净公司作出专门要求，特整理市值大于200亿的破净权重与局部活跃破净个股", "ClassLayer": "3", "Desc": "", "PlateSwitch": "2", "StkSwitch": "2", "Introduction": "<p>题材相关新闻</p><p><br/></p><p>财联社9月24日电，中国证监会就《上市公司监管指引第10号--市值管理(征求章见稿)》公开征求章见，《指引》第八条和第九条就主要指数成份股公司和长期破净公司作出专门要求&lt;一是主要指数成份股公司应当制定并公开披露市值管理制度》明确具体职责分工、内部考核评价等并在年度业绩说明会中就制度执行情况进行专项说明。上市公司可参照执行。二是长期破净公司应当披露估值提升计划包括目标、期限及具体措施，并在年度业绩说明会中就估值提升计划执行情况进行专项说明。</p><p><br/></p><p>长期破净公司应当制定并经董事会审议后披露上市公司估值提升计划，包括目标、期限及具体措施，相关内容和措施应当明确、具体、可执行，不得使用容易引起歧义或者误导投资者的表述。长期破净公司应当至少每年对估值提升计划的实施效果进行评估，并根据需要及时完善，经董事会审议后披露。</p><p><br/></p><p>题材相关介绍</p><p><br/></p><p>破净股是指股票的市场价格低于每股净资产的股票。这种现象通常在市场低迷时出现，破净股数量增多往往与市场底部靠拢有关。截至2024年9月24日，沪深两市破净股数量为738只，占比整个A股的13.79%，破净率已跃升至近年高位。这超过了历史上几次重要底部的破净股数量，如上证指数在2638点时的66只和1664点时的173只。</p><p>破净股主要集中在基础化工、房地产、建筑装饰行业，这些行业的破净股数量均在50只以上。银行、钢铁、房地产、煤炭等行业的破净率相对较高。例如，银行板块42只个股全部破净，钢铁行业破净率为60%。</p><p><br/></p>", "CreateTime": "1727189871", "UpdateTime": "0", "Table": [{"Level1": {"ID": "3145", "Name": "非银沪深300", "ZSCode": "0", "FirstShelveTime": "1731897977", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "000617", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.07元", "FirstShelveTime": "1731898158", "prod_name": "中油资本", "Hot": 2208}, {"StockID": "600219", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：4.33元", "FirstShelveTime": "1731898158", "prod_name": "南山铝业", "Hot": 867}, {"StockID": "600104", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：25.29元", "FirstShelveTime": "1731898043", "prod_name": "上汽集团", "Hot": 842}, {"StockID": "600741", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：19.07元", "FirstShelveTime": "1731898112", "prod_name": "华域汽车", "Hot": 754}, {"StockID": "601669", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：7.91元", "FirstShelveTime": "1731898071", "prod_name": "中国电建", "Hot": 677}, {"StockID": "601238", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：11.06元", "FirstShelveTime": "1731898112", "prod_name": "XD广汽集", "Hot": 651}, {"StockID": "000002", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：17.09元", "FirstShelveTime": "1731898158", "prod_name": "万科Ａ", "Hot": 643}, {"StockID": "601668", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：10.75元", "FirstShelveTime": "1731898071", "prod_name": "中国建筑", "Hot": 535}, {"StockID": "600585", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：35.61元", "FirstShelveTime": "1731898158", "prod_name": "XD海螺水", "Hot": 486}, {"StockID": "601800", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：17.35元", "FirstShelveTime": "1731898071", "prod_name": "中国交建", "Hot": 473}, {"StockID": "601390", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：12.30元", "FirstShelveTime": "1731898043", "prod_name": "中国中铁", "Hot": 465}, {"StockID": "601618", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：4.94元", "FirstShelveTime": "1731898021", "prod_name": "中国中冶", "Hot": 459}, {"StockID": "600019", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：9.05元", "FirstShelveTime": "1731898112", "prod_name": "宝钢股份", "Hot": 453}, {"StockID": "601699", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：15.68元", "FirstShelveTime": "1731898158", "prod_name": "潞安环能", "Hot": 450}, {"StockID": "601117", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：9.90元", "FirstShelveTime": "1731898071", "prod_name": "中国化学", "Hot": 384}]}, "Level2": []}, {"Level1": {"ID": "3056", "Name": "破净活跃个股", "ZSCode": "0", "FirstShelveTime": "1727186291", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "600643", "IsZz": "2", "IsHot": "1", "Reason": "每股净资产：7.72元", "FirstShelveTime": "1727188092", "prod_name": "爱建集团", "Hot": 31464}, {"StockID": "600676", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：5.02元", "FirstShelveTime": "1731921116", "prod_name": "交运股份", "Hot": 2614}, {"StockID": "600884", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：9.76元", "FirstShelveTime": "1731921194", "prod_name": "杉杉股份", "Hot": 2316}, {"StockID": "000627", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：4.14元", "FirstShelveTime": "1727188742", "prod_name": "天茂集团", "Hot": 1542}, {"StockID": "600658", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.11元", "FirstShelveTime": "1731921223", "prod_name": "电子城  ", "Hot": 1542}, {"StockID": "600787", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.30元", "FirstShelveTime": "1731921140", "prod_name": "中储股份", "Hot": 1458}, {"StockID": "002146", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：5.11元", "FirstShelveTime": "1731921235", "prod_name": "荣盛发展", "Hot": 1312}, {"StockID": "600104", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：25.29元", "FirstShelveTime": "1731921091", "prod_name": "上汽集团", "Hot": 842}, {"StockID": "000717", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：3.57元", "FirstShelveTime": "1727188092", "prod_name": "中南股份", "Hot": 799}, {"StockID": "002344", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.54元", "FirstShelveTime": "1731921130", "prod_name": "海宁皮城", "Hot": 605}, {"StockID": "601828", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：11.09元", "FirstShelveTime": "1727344230", "prod_name": "美凯龙  ", "Hot": 579}, {"StockID": "000402", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：11.37元", "FirstShelveTime": "1727591553", "prod_name": "金融街", "Hot": 546}, {"StockID": "603185", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：19.83元", "FirstShelveTime": "1727188092", "prod_name": "弘元绿能", "Hot": 529}, {"StockID": "002110", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.21元", "FirstShelveTime": "1727188092", "prod_name": "三钢闽光", "Hot": 486}, {"StockID": "000906", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.66元", "FirstShelveTime": "1727188092", "prod_name": "浙商中拓", "Hot": 414}, {"StockID": "600231", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：2.41元", "FirstShelveTime": "1727188092", "prod_name": "凌钢股份", "Hot": 409}, {"StockID": "600508", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：17.72元", "FirstShelveTime": "1731921214", "prod_name": "上海能源", "Hot": 368}]}, "Level2": []}, {"Level1": {"ID": "3147", "Name": "A50破净", "ZSCode": "0", "FirstShelveTime": "1731899789", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "601398", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：10.23元", "FirstShelveTime": "1731899899", "prod_name": "工商银行", "Hot": 1864}, {"StockID": "601988", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.18元", "FirstShelveTime": "1731899899", "prod_name": "中国银行", "Hot": 1326}, {"StockID": "601288", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：7.40元", "FirstShelveTime": "1731899899", "prod_name": "农业银行", "Hot": 1187}, {"StockID": "601328", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：13.06元", "FirstShelveTime": "1731899899", "prod_name": "交通银行", "Hot": 901}, {"StockID": "600104", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：25.29元", "FirstShelveTime": "1731899822", "prod_name": "上汽集团", "Hot": 842}, {"StockID": "601658", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.37元", "FirstShelveTime": "1731899899", "prod_name": "邮储银行", "Hot": 749}, {"StockID": "601166", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：36.88元", "FirstShelveTime": "1731899899", "prod_name": "兴业银行", "Hot": 747}, {"StockID": "601669", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：7.91元", "FirstShelveTime": "1731899899", "prod_name": "中国电建", "Hot": 677}, {"StockID": "601668", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：10.75元", "FirstShelveTime": "1731899899", "prod_name": "中国建筑", "Hot": 535}, {"StockID": "600048", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：16.66元", "FirstShelveTime": "1731899815", "prod_name": "保利发展", "Hot": 501}, {"StockID": "600028", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.76元", "FirstShelveTime": "1731899810", "prod_name": "中国石化", "Hot": 471}, {"StockID": "601390", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：12.30元", "FirstShelveTime": "1731899899", "prod_name": "中国中铁", "Hot": 465}]}, "Level2": []}, {"Level1": {"ID": "3055", "Name": "破净权重", "ZSCode": "0", "FirstShelveTime": "1727186291", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": []}, "Level2": [{"ID": "3058", "Name": "券商", "ZSCode": "0", "FirstShelveTime": "1727186291", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "601211", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：16.64元", "FirstShelveTime": "1727187200", "prod_name": "国泰海通", "Hot": 2706}, {"StockID": "601688", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：17.10元", "FirstShelveTime": "1727187200", "prod_name": "华泰证券", "Hot": 1851}, {"StockID": "600109", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.74元", "FirstShelveTime": "1727187200", "prod_name": "国金证券", "Hot": 1240}, {"StockID": "601555", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.15元", "FirstShelveTime": "1727187200", "prod_name": "东吴证券", "Hot": 746}, {"StockID": "000783", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：5.94元", "FirstShelveTime": "1727187200", "prod_name": "长江证券", "Hot": 716}, {"StockID": "601377", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.15元", "FirstShelveTime": "1727187200", "prod_name": "兴业证券", "Hot": 669}, {"StockID": "000728", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.17元", "FirstShelveTime": "1727187200", "prod_name": "国元证券", "Hot": 499}]}, {"ID": "3057", "Name": "银行", "ZSCode": "801027", "FirstShelveTime": "1727186291", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "601398", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：10.23元", "FirstShelveTime": "1727186953", "prod_name": "工商银行", "Hot": 1864}, {"StockID": "600000", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：21.65元", "FirstShelveTime": "1727186953", "prod_name": "浦发银行", "Hot": 1619}, {"StockID": "600926", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：17.19元", "FirstShelveTime": "1727186953", "prod_name": "杭州银行", "Hot": 1327}, {"StockID": "601988", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.18元", "FirstShelveTime": "1727186953", "prod_name": "中国银行", "Hot": 1326}, {"StockID": "601288", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：7.40元", "FirstShelveTime": "1727186953", "prod_name": "农业银行", "Hot": 1187}, {"StockID": "000001", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：21.23元", "FirstShelveTime": "1727186953", "prod_name": "平安银行", "Hot": 1091}, {"StockID": "600919", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：12.15元", "FirstShelveTime": "1727186953", "prod_name": "江苏银行", "Hot": 1073}, {"StockID": "002966", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：11.25元", "FirstShelveTime": "1727186953", "prod_name": "苏州银行", "Hot": 957}, {"StockID": "601328", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：13.06元", "FirstShelveTime": "1727186953", "prod_name": "交通银行", "Hot": 901}, {"StockID": "601825", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：12.28元", "FirstShelveTime": "1727186953", "prod_name": "XD沪农商", "Hot": 803}, {"StockID": "601939", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：12.14元", "FirstShelveTime": "1727186953", "prod_name": "建设银行", "Hot": 774}, {"StockID": "601939", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：12.14元", "FirstShelveTime": "1727186953", "prod_name": "建设银行", "Hot": 774}, {"StockID": "601658", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.37元", "FirstShelveTime": "1727186953", "prod_name": "邮储银行", "Hot": 749}, {"StockID": "601166", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：36.88元", "FirstShelveTime": "1727186953", "prod_name": "兴业银行", "Hot": 747}, {"StockID": "600016", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：12.36元", "FirstShelveTime": "1727186953", "prod_name": "民生银行", "Hot": 712}, {"StockID": "601963", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：15.07元", "FirstShelveTime": "1727186953", "prod_name": "重庆银行", "Hot": 688}, {"StockID": "002142", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：29.37元", "FirstShelveTime": "1727186953", "prod_name": "宁波银行", "Hot": 681}, {"StockID": "601998", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：12.22元", "FirstShelveTime": "1727186953", "prod_name": "中信银行", "Hot": 677}, {"StockID": "601838", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：17.87元", "FirstShelveTime": "1727186953", "prod_name": "成都银行", "Hot": 675}, {"StockID": "601009", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：14.23元", "FirstShelveTime": "1727186953", "prod_name": "南京银行", "Hot": 656}, {"StockID": "601665", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：7.42元", "FirstShelveTime": "1727186953", "prod_name": "齐鲁银行", "Hot": 579}, {"StockID": "601818", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：7.84元", "FirstShelveTime": "1727186953", "prod_name": "光大银行", "Hot": 552}, {"StockID": "601169", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：12.29元", "FirstShelveTime": "1727186953", "prod_name": "北京银行", "Hot": 550}, {"StockID": "601077", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：10.69元", "FirstShelveTime": "1727186953", "prod_name": "XD渝农商", "Hot": 523}, {"StockID": "600015", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：17.95元", "FirstShelveTime": "1727186953", "prod_name": "华夏银行", "Hot": 514}, {"StockID": "601577", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：15.65元", "FirstShelveTime": "1727186953", "prod_name": "长沙银行", "Hot": 507}, {"StockID": "601128", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.76元", "FirstShelveTime": "1727186953", "prod_name": "常熟银行", "Hot": 486}, {"StockID": "601229", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：15.89元", "FirstShelveTime": "1727186953", "prod_name": "上海银行", "Hot": 482}]}, {"ID": "3059", "Name": "其他金融", "ZSCode": "0", "FirstShelveTime": "1727186291", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "000617", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.07元", "FirstShelveTime": "1727187280", "prod_name": "中油资本", "Hot": 2208}, {"StockID": "600390", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：9.50元", "FirstShelveTime": "1727187280", "prod_name": "五矿资本", "Hot": 2064}, {"StockID": "600061", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：7.90元", "FirstShelveTime": "1727187280", "prod_name": "国投资本", "Hot": 568}]}, {"ID": "3060", "Name": "地产链", "ZSCode": "801676", "FirstShelveTime": "1727186291", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "000002", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：17.09元", "FirstShelveTime": "1727187404", "prod_name": "万科Ａ", "Hot": 643}, {"StockID": "601155", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：27.23元", "FirstShelveTime": "1727187404", "prod_name": "新城控股", "Hot": 536}, {"StockID": "600606", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：5.69元", "FirstShelveTime": "1727187404", "prod_name": "绿地控股", "Hot": 516}, {"StockID": "600048", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：16.66元", "FirstShelveTime": "1727187404", "prod_name": "保利发展", "Hot": 501}, {"StockID": "600585", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：35.61元", "FirstShelveTime": "1727187404", "prod_name": "XD海螺水", "Hot": 486}, {"StockID": "001979", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：10.68元", "FirstShelveTime": "1727187404", "prod_name": "招商蛇口", "Hot": 457}, {"StockID": "000877", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：9.23元", "FirstShelveTime": "1727187404", "prod_name": "天山股份", "Hot": 388}]}, {"ID": "3066", "Name": "基建类", "ZSCode": "0", "FirstShelveTime": "1727187519", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "601669", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：7.91元", "FirstShelveTime": "1727187458", "prod_name": "中国电建", "Hot": 677}, {"StockID": "601668", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：10.75元", "FirstShelveTime": "1727187404", "prod_name": "中国建筑", "Hot": 535}, {"StockID": "601868", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：2.48元", "FirstShelveTime": "1727187404", "prod_name": "中国能建", "Hot": 497}, {"StockID": "601800", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：17.35元", "FirstShelveTime": "1727187458", "prod_name": "中国交建", "Hot": 473}, {"StockID": "601390", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：12.30元", "FirstShelveTime": "1727187458", "prod_name": "中国中铁", "Hot": 465}, {"StockID": "601618", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：4.94元", "FirstShelveTime": "1727187620", "prod_name": "中国中冶", "Hot": 459}, {"StockID": "601186", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：18.85元", "FirstShelveTime": "1727187458", "prod_name": "中国铁建", "Hot": 420}]}, {"ID": "3061", "Name": "资源类", "ZSCode": "0", "FirstShelveTime": "1727186291", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "600219", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：4.33元", "FirstShelveTime": "1727187724", "prod_name": "南山铝业", "Hot": 867}, {"StockID": "601857", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.14元", "FirstShelveTime": "1727187724", "prod_name": "中国石油", "Hot": 812}, {"StockID": "600348", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：7.45元", "FirstShelveTime": "1727187767", "prod_name": "华阳股份", "Hot": 534}, {"StockID": "600583", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：5.73元", "FirstShelveTime": "1727187724", "prod_name": "海油工程", "Hot": 488}, {"StockID": "000937", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.08元", "FirstShelveTime": "1727187767", "prod_name": "冀中能源", "Hot": 461}, {"StockID": "601699", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：15.68元", "FirstShelveTime": "1727187767", "prod_name": "潞安环能", "Hot": 450}, {"StockID": "601666", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：9.58", "FirstShelveTime": "1727187767", "prod_name": "平煤股份", "Hot": 376}, {"StockID": "000703", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.58元", "FirstShelveTime": "1727187724", "prod_name": "恒逸石化", "Hot": 346}]}, {"ID": "3062", "Name": "钢铁", "ZSCode": "801012", "FirstShelveTime": "1727186291", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "000932", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：7.66元", "FirstShelveTime": "1727187852", "prod_name": "华菱钢铁", "Hot": 479}, {"StockID": "600019", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：9.05元", "FirstShelveTime": "1727187852", "prod_name": "宝钢股份", "Hot": 453}, {"StockID": "000959", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.39元", "FirstShelveTime": "1727187852", "prod_name": "首钢股份", "Hot": 382}]}, {"ID": "3063", "Name": "汽车", "ZSCode": "0", "FirstShelveTime": "1727186291", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "600104", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：25.29元", "FirstShelveTime": "1727187889", "prod_name": "上汽集团", "Hot": 842}, {"StockID": "600741", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：19.07元", "FirstShelveTime": "1727187889", "prod_name": "华域汽车", "Hot": 754}, {"StockID": "601238", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：11.06元", "FirstShelveTime": "1727187889", "prod_name": "XD广汽集", "Hot": 651}]}, {"ID": "3064", "Name": "交通运输", "ZSCode": "0", "FirstShelveTime": "1727186291", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "601018", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：3.88元", "FirstShelveTime": "1727188788", "prod_name": "宁波港  ", "Hot": 541}, {"StockID": "601880", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：1.65元", "FirstShelveTime": "1727188788", "prod_name": "辽港股份", "Hot": 492}, {"StockID": "601598", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：5.29元", "FirstShelveTime": "1727188788", "prod_name": "中国外运", "Hot": 470}, {"StockID": "601333", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：3.80元", "FirstShelveTime": "1727188788", "prod_name": "广深铁路", "Hot": 408}, {"StockID": "601006", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.33元", "FirstShelveTime": "1727188788", "prod_name": "大秦铁路", "Hot": 397}, {"StockID": "600704", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.90元", "FirstShelveTime": "1727188788", "prod_name": "物产中大", "Hot": 378}, {"StockID": "001872", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：23.96元", "FirstShelveTime": "1727188788", "prod_name": "招商港口", "Hot": 372}, {"StockID": "600153", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：18.12元", "FirstShelveTime": "1727188788", "prod_name": "建发股份", "Hot": 370}]}, {"ID": "3068", "Name": "公用事业", "ZSCode": "0", "FirstShelveTime": "1727188869", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "600157", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：2.12元", "FirstShelveTime": "1727188920", "prod_name": "永泰能源", "Hot": 660}, {"StockID": "002608", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.21元", "FirstShelveTime": "1727188920", "prod_name": "江苏国信", "Hot": 477}, {"StockID": "000883", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：5.10元", "FirstShelveTime": "1727188920", "prod_name": "湖北能源", "Hot": 362}]}, {"ID": "3065", "Name": "其他", "ZSCode": "0", "FirstShelveTime": "1727186291", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "002129", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：9.27元", "FirstShelveTime": "1727189089", "prod_name": "TCL中环", "Hot": 846}, {"StockID": "600637", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.78元", "FirstShelveTime": "1727189089", "prod_name": "东方明珠", "Hot": 442}, {"StockID": "600655", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：9.42元", "FirstShelveTime": "1727188712", "prod_name": "豫园股份", "Hot": 437}, {"StockID": "600177", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.38元", "FirstShelveTime": "1727189089", "prod_name": "雅戈尔  ", "Hot": 403}, {"StockID": "601117", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：9.90元", "FirstShelveTime": "1727187634", "prod_name": "中国化学", "Hot": 384}, {"StockID": "600098", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：7.41元", "FirstShelveTime": "1727188687", "prod_name": "XD广州发", "Hot": 357}]}]}, {"Level1": {"ID": "3067", "Name": "破净2元股", "ZSCode": "0", "FirstShelveTime": "1727188006", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "000826", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：2.82元", "FirstShelveTime": "1727188585", "prod_name": "启迪环境", "Hot": 3248}, {"StockID": "002146", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：5.11元", "FirstShelveTime": "1727188585", "prod_name": "荣盛发展", "Hot": 1312}, {"StockID": "600337", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：2.25元", "FirstShelveTime": "1727188310", "prod_name": "美克家居", "Hot": 821}, {"StockID": "002936", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：4.88元", "FirstShelveTime": "1727188585", "prod_name": "郑州银行", "Hot": 676}, {"StockID": "600157", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：2.12元", "FirstShelveTime": "1727188585", "prod_name": "永泰能源", "Hot": 660}, {"StockID": "600567", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：2.95元", "FirstShelveTime": "1727188310", "prod_name": "山鹰国际", "Hot": 592}, {"StockID": "600162", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：1.80元", "FirstShelveTime": "1727188585", "prod_name": "香江控股", "Hot": 526}, {"StockID": "600606", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：5.69元", "FirstShelveTime": "1727188585", "prod_name": "绿地控股", "Hot": 516}, {"StockID": "601880", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：1.65元", "FirstShelveTime": "1727188585", "prod_name": "辽港股份", "Hot": 492}, {"StockID": "600307", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：1.47元", "FirstShelveTime": "1727188310", "prod_name": "酒钢宏兴", "Hot": 482}, {"StockID": "601588", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：3.50元", "FirstShelveTime": "1727188585", "prod_name": "北辰实业", "Hot": 436}, {"StockID": "600022", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：1.88元", "FirstShelveTime": "1727188310", "prod_name": "山东钢铁", "Hot": 430}, {"StockID": "601992", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：4.11元", "FirstShelveTime": "1727188310", "prod_name": "金隅集团", "Hot": 414}, {"StockID": "600231", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：2.41元", "FirstShelveTime": "1727188310", "prod_name": "凌钢股份", "Hot": 409}, {"StockID": "603077", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：2.15元", "FirstShelveTime": "1727188585", "prod_name": "和邦生物", "Hot": 400}]}, "Level2": []}, {"Level1": {"ID": "3146", "Name": "科创创业板破净", "ZSCode": "0", "FirstShelveTime": "1731899148", "UpdateCacheTime": "0", "IsNew": 0, "Stocks": [{"StockID": "300057", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：5.94元", "FirstShelveTime": "1731899241", "prod_name": "万顺新材", "Hot": 2214}, {"StockID": "300158", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：5.09元", "FirstShelveTime": "1731899241", "prod_name": "振东制药", "Hot": 592}, {"StockID": "300055", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.13元", "FirstShelveTime": "1731899177", "prod_name": "万邦达", "Hot": 559}, {"StockID": "300639", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.94元", "FirstShelveTime": "1731899241", "prod_name": "凯普生物", "Hot": 471}, {"StockID": "300305", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.40元", "FirstShelveTime": "1731899241", "prod_name": "裕兴股份", "Hot": 427}, {"StockID": "300616", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：15.66元", "FirstShelveTime": "1731899241", "prod_name": "尚品宅配", "Hot": 426}, {"StockID": "300732", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：8.10元", "FirstShelveTime": "1731899241", "prod_name": "设研院", "Hot": 402}, {"StockID": "688033", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：6.59元", "FirstShelveTime": "1731899322", "prod_name": "天宜新材", "Hot": 379}, {"StockID": "688298", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：34.47元", "FirstShelveTime": "1731899322", "prod_name": "东方生物", "Hot": 378}, {"StockID": "300190", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：4.51元", "FirstShelveTime": "1731899241", "prod_name": "维尔利", "Hot": 367}, {"StockID": "300070", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：7.49元", "FirstShelveTime": "1731899241", "prod_name": "碧水源", "Hot": 366}, {"StockID": "300664", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：5.61元", "FirstShelveTime": "1731899241", "prod_name": "鹏鹞环保", "Hot": 348}, {"StockID": "688317", "IsZz": "2", "IsHot": "0", "Reason": "每股净资产：18.93元", "FirstShelveTime": "1731899322", "prod_name": "之江生物", "Hot": 334}]}, "Level2": []}], "Stocks": [], "StockList": [{"StockID": "601128", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：8.76元"}], "prod_name": "常熟银行", "HotNum": 486}, {"StockID": "601825", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：12.28元"}], "prod_name": "XD沪农商", "HotNum": 803}, {"StockID": "601077", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：10.69元"}], "prod_name": "XD渝农商", "HotNum": 523}, {"StockID": "601998", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：12.22元"}], "prod_name": "中信银行", "HotNum": 677}, {"StockID": "601166", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：36.88元"}, {"ID": "3147", "Name": "A50破净", "Reason": "每股净资产：36.88元"}], "prod_name": "兴业银行", "HotNum": 747}, {"StockID": "600016", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：12.36元"}], "prod_name": "民生银行", "HotNum": 712}, {"StockID": "601818", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：7.84元"}], "prod_name": "光大银行", "HotNum": 552}, {"StockID": "600015", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：17.95元"}], "prod_name": "华夏银行", "HotNum": 514}, {"StockID": "600000", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：21.65元"}], "prod_name": "浦发银行", "HotNum": 1619}, {"StockID": "000001", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：21.23元"}], "prod_name": "平安银行", "HotNum": 1091}, {"StockID": "601398", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：10.23元"}, {"ID": "3147", "Name": "A50破净", "Reason": "每股净资产：10.23元"}], "prod_name": "工商银行", "HotNum": 1864}, {"StockID": "601328", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：13.06元"}, {"ID": "3147", "Name": "A50破净", "Reason": "每股净资产：13.06元"}], "prod_name": "交通银行", "HotNum": 901}, {"StockID": "601939", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：12.14元"}, {"ID": "3057", "Name": "银行", "Reason": "每股净资产：12.14元"}], "prod_name": "建设银行", "HotNum": 774}, {"StockID": "601288", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：7.40元"}, {"ID": "3147", "Name": "A50破净", "Reason": "每股净资产：7.40元"}], "prod_name": "农业银行", "HotNum": 1187}, {"StockID": "601988", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：8.18元"}, {"ID": "3147", "Name": "A50破净", "Reason": "每股净资产：8.18元"}], "prod_name": "中国银行", "HotNum": 1326}, {"StockID": "601658", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：8.37元"}, {"ID": "3147", "Name": "A50破净", "Reason": "每股净资产：8.37元"}], "prod_name": "邮储银行", "HotNum": 749}, {"StockID": "601169", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：12.29元"}], "prod_name": "北京银行", "HotNum": 550}, {"StockID": "002966", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：11.25元"}], "prod_name": "苏州银行", "HotNum": 957}, {"StockID": "600919", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：12.15元"}], "prod_name": "江苏银行", "HotNum": 1073}, {"StockID": "601009", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：14.23元"}], "prod_name": "南京银行", "HotNum": 656}, {"StockID": "601963", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：15.07元"}], "prod_name": "重庆银行", "HotNum": 688}, {"StockID": "002142", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：29.37元"}], "prod_name": "宁波银行", "HotNum": 681}, {"StockID": "601229", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：15.89元"}], "prod_name": "上海银行", "HotNum": 482}, {"StockID": "600926", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：17.19元"}], "prod_name": "杭州银行", "HotNum": 1327}, {"StockID": "601838", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：17.87元"}], "prod_name": "成都银行", "HotNum": 675}, {"StockID": "601577", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：15.65元"}], "prod_name": "长沙银行", "HotNum": 507}, {"StockID": "601665", "Tag": [{"ID": "3057", "Name": "银行", "Reason": "每股净资产：7.42元"}], "prod_name": "齐鲁银行", "HotNum": 579}, {"StockID": "600109", "Tag": [{"ID": "3058", "Name": "券商", "Reason": "每股净资产：8.74元"}], "prod_name": "国金证券", "HotNum": 1240}, {"StockID": "601688", "Tag": [{"ID": "3058", "Name": "券商", "Reason": "每股净资产：17.10元"}], "prod_name": "华泰证券", "HotNum": 1851}, {"StockID": "601377", "Tag": [{"ID": "3058", "Name": "券商", "Reason": "每股净资产：8.15元"}], "prod_name": "兴业证券", "HotNum": 669}, {"StockID": "601555", "Tag": [{"ID": "3058", "Name": "券商", "Reason": "每股净资产：8.15元"}], "prod_name": "东吴证券", "HotNum": 746}, {"StockID": "601211", "Tag": [{"ID": "3058", "Name": "券商", "Reason": "每股净资产：16.64元"}], "prod_name": "国泰海通", "HotNum": 2706}, {"StockID": "000728", "Tag": [{"ID": "3058", "Name": "券商", "Reason": "每股净资产：8.17元"}], "prod_name": "国元证券", "HotNum": 499}, {"StockID": "000783", "Tag": [{"ID": "3058", "Name": "券商", "Reason": "每股净资产：5.94元"}], "prod_name": "长江证券", "HotNum": 716}, {"StockID": "600061", "Tag": [{"ID": "3059", "Name": "其他金融", "Reason": "每股净资产：7.90元"}], "prod_name": "国投资本", "HotNum": 568}, {"StockID": "000617", "Tag": [{"ID": "3059", "Name": "其他金融", "Reason": "每股净资产：8.07元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：8.07元"}], "prod_name": "中油资本", "HotNum": 2208}, {"StockID": "600390", "Tag": [{"ID": "3059", "Name": "其他金融", "Reason": "每股净资产：9.50元"}], "prod_name": "五矿资本", "HotNum": 2064}, {"StockID": "001979", "Tag": [{"ID": "3060", "Name": "地产链", "Reason": "每股净资产：10.68元"}], "prod_name": "招商蛇口", "HotNum": 457}, {"StockID": "000002", "Tag": [{"ID": "3060", "Name": "地产链", "Reason": "每股净资产：17.09元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：17.09元"}], "prod_name": "万科Ａ", "HotNum": 643}, {"StockID": "600606", "Tag": [{"ID": "3060", "Name": "地产链", "Reason": "每股净资产：5.69元"}, {"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：5.69元"}], "prod_name": "绿地控股", "HotNum": 516}, {"StockID": "600048", "Tag": [{"ID": "3060", "Name": "地产链", "Reason": "每股净资产：16.66元"}, {"ID": "3147", "Name": "A50破净", "Reason": "每股净资产：16.66元"}], "prod_name": "保利发展", "HotNum": 501}, {"StockID": "601155", "Tag": [{"ID": "3060", "Name": "地产链", "Reason": "每股净资产：27.23元"}], "prod_name": "新城控股", "HotNum": 536}, {"StockID": "000877", "Tag": [{"ID": "3060", "Name": "地产链", "Reason": "每股净资产：9.23元"}], "prod_name": "天山股份", "HotNum": 388}, {"StockID": "600585", "Tag": [{"ID": "3060", "Name": "地产链", "Reason": "每股净资产：35.61元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：35.61元"}], "prod_name": "XD海螺水", "HotNum": 486}, {"StockID": "601668", "Tag": [{"ID": "3066", "Name": "基建类", "Reason": "每股净资产：10.75元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：10.75元"}, {"ID": "3147", "Name": "A50破净", "Reason": "每股净资产：10.75元"}], "prod_name": "中国建筑", "HotNum": 535}, {"StockID": "601868", "Tag": [{"ID": "3066", "Name": "基建类", "Reason": "每股净资产：2.48元"}], "prod_name": "中国能建", "HotNum": 497}, {"StockID": "601390", "Tag": [{"ID": "3066", "Name": "基建类", "Reason": "每股净资产：12.30元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：12.30元"}, {"ID": "3147", "Name": "A50破净", "Reason": "每股净资产：12.30元"}], "prod_name": "中国中铁", "HotNum": 465}, {"StockID": "601669", "Tag": [{"ID": "3066", "Name": "基建类", "Reason": "每股净资产：7.91元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：7.91元"}, {"ID": "3147", "Name": "A50破净", "Reason": "每股净资产：7.91元"}], "prod_name": "中国电建", "HotNum": 677}, {"StockID": "601800", "Tag": [{"ID": "3066", "Name": "基建类", "Reason": "每股净资产：17.35元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：17.35元"}], "prod_name": "中国交建", "HotNum": 473}, {"StockID": "601186", "Tag": [{"ID": "3066", "Name": "基建类", "Reason": "每股净资产：18.85元"}], "prod_name": "中国铁建", "HotNum": 420}, {"StockID": "601618", "Tag": [{"ID": "3066", "Name": "基建类", "Reason": "每股净资产：4.94元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：4.94元"}], "prod_name": "中国中冶", "HotNum": 459}, {"StockID": "601117", "Tag": [{"ID": "3065", "Name": "其他", "Reason": "每股净资产：9.90元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：9.90元"}], "prod_name": "中国化学", "HotNum": 384}, {"StockID": "600219", "Tag": [{"ID": "3061", "Name": "资源类", "Reason": "每股净资产：4.33元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：4.33元"}], "prod_name": "南山铝业", "HotNum": 867}, {"StockID": "600583", "Tag": [{"ID": "3061", "Name": "资源类", "Reason": "每股净资产：5.73元"}], "prod_name": "海油工程", "HotNum": 488}, {"StockID": "601857", "Tag": [{"ID": "3061", "Name": "资源类", "Reason": "每股净资产：8.14元"}], "prod_name": "中国石油", "HotNum": 812}, {"StockID": "000703", "Tag": [{"ID": "3061", "Name": "资源类", "Reason": "每股净资产：6.58元"}], "prod_name": "恒逸石化", "HotNum": 346}, {"StockID": "000937", "Tag": [{"ID": "3061", "Name": "资源类", "Reason": "每股净资产：6.08元"}], "prod_name": "冀中能源", "HotNum": 461}, {"StockID": "601666", "Tag": [{"ID": "3061", "Name": "资源类", "Reason": "每股净资产：9.58"}], "prod_name": "平煤股份", "HotNum": 376}, {"StockID": "600348", "Tag": [{"ID": "3061", "Name": "资源类", "Reason": "每股净资产：7.45元"}], "prod_name": "华阳股份", "HotNum": 534}, {"StockID": "601699", "Tag": [{"ID": "3061", "Name": "资源类", "Reason": "每股净资产：15.68元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：15.68元"}], "prod_name": "潞安环能", "HotNum": 450}, {"StockID": "000932", "Tag": [{"ID": "3062", "Name": "钢铁", "Reason": "每股净资产：7.66元"}], "prod_name": "华菱钢铁", "HotNum": 479}, {"StockID": "600019", "Tag": [{"ID": "3062", "Name": "钢铁", "Reason": "每股净资产：9.05元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：9.05元"}], "prod_name": "宝钢股份", "HotNum": 453}, {"StockID": "000959", "Tag": [{"ID": "3062", "Name": "钢铁", "Reason": "每股净资产：6.39元"}], "prod_name": "首钢股份", "HotNum": 382}, {"StockID": "600104", "Tag": [{"ID": "3063", "Name": "汽车", "Reason": "每股净资产：25.29元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：25.29元"}, {"ID": "3147", "Name": "A50破净", "Reason": "每股净资产：25.29元"}, {"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：25.29元"}], "prod_name": "上汽集团", "HotNum": 842}, {"StockID": "601238", "Tag": [{"ID": "3063", "Name": "汽车", "Reason": "每股净资产：11.06元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：11.06元"}], "prod_name": "XD广汽集", "HotNum": 651}, {"StockID": "600741", "Tag": [{"ID": "3063", "Name": "汽车", "Reason": "每股净资产：19.07元"}, {"ID": "3145", "Name": "非银沪深300", "Reason": "每股净资产：19.07元"}], "prod_name": "华域汽车", "HotNum": 754}, {"StockID": "000906", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：6.66元"}], "prod_name": "浙商中拓", "HotNum": 414}, {"StockID": "600231", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：2.41元"}, {"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：2.41元"}], "prod_name": "凌钢股份", "HotNum": 409}, {"StockID": "000717", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：3.57元"}], "prod_name": "中南股份", "HotNum": 799}, {"StockID": "002110", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：8.21元"}], "prod_name": "三钢闽光", "HotNum": 486}, {"StockID": "600643", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：7.72元"}], "prod_name": "爱建集团", "HotNum": 31464}, {"StockID": "603185", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：19.83元"}], "prod_name": "弘元绿能", "HotNum": 529}, {"StockID": "600337", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：2.25元"}], "prod_name": "美克家居", "HotNum": 821}, {"StockID": "601992", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：4.11元"}], "prod_name": "金隅集团", "HotNum": 414}, {"StockID": "600022", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：1.88元"}], "prod_name": "山东钢铁", "HotNum": 430}, {"StockID": "600307", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：1.47元"}], "prod_name": "酒钢宏兴", "HotNum": 482}, {"StockID": "600567", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：2.95元"}], "prod_name": "山鹰国际", "HotNum": 592}, {"StockID": "002146", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：5.11元"}, {"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：5.11元"}], "prod_name": "荣盛发展", "HotNum": 1312}, {"StockID": "000826", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：2.82元"}], "prod_name": "启迪环境", "HotNum": 3248}, {"StockID": "600157", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：2.12元"}, {"ID": "3068", "Name": "公用事业", "Reason": "每股净资产：2.12元"}], "prod_name": "永泰能源", "HotNum": 660}, {"StockID": "603077", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：2.15元"}], "prod_name": "和邦生物", "HotNum": 400}, {"StockID": "002936", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：4.88元"}], "prod_name": "郑州银行", "HotNum": 676}, {"StockID": "600162", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：1.80元"}], "prod_name": "香江控股", "HotNum": 526}, {"StockID": "601588", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：3.50元"}], "prod_name": "北辰实业", "HotNum": 436}, {"StockID": "601880", "Tag": [{"ID": "3067", "Name": "破净2元股", "Reason": "每股净资产：1.65元"}, {"ID": "3064", "Name": "交通运输", "Reason": "每股净资产：1.65元"}], "prod_name": "辽港股份", "HotNum": 492}, {"StockID": "600098", "Tag": [{"ID": "3065", "Name": "其他", "Reason": "每股净资产：7.41元"}], "prod_name": "XD广州发", "HotNum": 357}, {"StockID": "600655", "Tag": [{"ID": "3065", "Name": "其他", "Reason": "每股净资产：9.42元"}], "prod_name": "豫园股份", "HotNum": 437}, {"StockID": "000627", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：4.14元"}], "prod_name": "天茂集团", "HotNum": 1542}, {"StockID": "600153", "Tag": [{"ID": "3064", "Name": "交通运输", "Reason": "每股净资产：18.12元"}], "prod_name": "建发股份", "HotNum": 370}, {"StockID": "600704", "Tag": [{"ID": "3064", "Name": "交通运输", "Reason": "每股净资产：6.90元"}], "prod_name": "物产中大", "HotNum": 378}, {"StockID": "601598", "Tag": [{"ID": "3064", "Name": "交通运输", "Reason": "每股净资产：5.29元"}], "prod_name": "中国外运", "HotNum": 470}, {"StockID": "601006", "Tag": [{"ID": "3064", "Name": "交通运输", "Reason": "每股净资产：8.33元"}], "prod_name": "大秦铁路", "HotNum": 397}, {"StockID": "601333", "Tag": [{"ID": "3064", "Name": "交通运输", "Reason": "每股净资产：3.80元"}], "prod_name": "广深铁路", "HotNum": 408}, {"StockID": "601018", "Tag": [{"ID": "3064", "Name": "交通运输", "Reason": "每股净资产：3.88元"}], "prod_name": "宁波港  ", "HotNum": 541}, {"StockID": "001872", "Tag": [{"ID": "3064", "Name": "交通运输", "Reason": "每股净资产：23.96元"}], "prod_name": "招商港口", "HotNum": 372}, {"StockID": "002608", "Tag": [{"ID": "3068", "Name": "公用事业", "Reason": "每股净资产：8.21元"}], "prod_name": "江苏国信", "HotNum": 477}, {"StockID": "000883", "Tag": [{"ID": "3068", "Name": "公用事业", "Reason": "每股净资产：5.10元"}], "prod_name": "湖北能源", "HotNum": 362}, {"StockID": "600637", "Tag": [{"ID": "3065", "Name": "其他", "Reason": "每股净资产：8.78元"}], "prod_name": "东方明珠", "HotNum": 442}, {"StockID": "002129", "Tag": [{"ID": "3065", "Name": "其他", "Reason": "每股净资产：9.27元"}], "prod_name": "TCL中环", "HotNum": 846}, {"StockID": "600177", "Tag": [{"ID": "3065", "Name": "其他", "Reason": "每股净资产：8.38元"}], "prod_name": "雅戈尔  ", "HotNum": 403}, {"StockID": "601828", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：11.09元"}], "prod_name": "美凯龙  ", "HotNum": 579}, {"StockID": "000402", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：11.37元"}], "prod_name": "金融街", "HotNum": 546}, {"StockID": "300055", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：6.13元"}], "prod_name": "万邦达", "HotNum": 559}, {"StockID": "300057", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：5.94元"}], "prod_name": "万顺新材", "HotNum": 2214}, {"StockID": "300070", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：7.49元"}], "prod_name": "碧水源", "HotNum": 366}, {"StockID": "300158", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：5.09元"}], "prod_name": "振东制药", "HotNum": 592}, {"StockID": "300190", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：4.51元"}], "prod_name": "维尔利", "HotNum": 367}, {"StockID": "300305", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：6.40元"}], "prod_name": "裕兴股份", "HotNum": 427}, {"StockID": "300616", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：15.66元"}], "prod_name": "尚品宅配", "HotNum": 426}, {"StockID": "300639", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：6.94元"}], "prod_name": "凯普生物", "HotNum": 471}, {"StockID": "300664", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：5.61元"}], "prod_name": "鹏鹞环保", "HotNum": 348}, {"StockID": "300732", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：8.10元"}], "prod_name": "设研院", "HotNum": 402}, {"StockID": "688033", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：6.59元"}], "prod_name": "天宜新材", "HotNum": 379}, {"StockID": "688298", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：34.47元"}], "prod_name": "东方生物", "HotNum": 378}, {"StockID": "688317", "Tag": [{"ID": "3146", "Name": "科创创业板破净", "Reason": "每股净资产：18.93元"}], "prod_name": "之江生物", "HotNum": 334}, {"StockID": "600028", "Tag": [{"ID": "3147", "Name": "A50破净", "Reason": "每股净资产：6.76元"}], "prod_name": "中国石化", "HotNum": 471}, {"StockID": "600676", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：5.02元"}], "prod_name": "交运股份", "HotNum": 2614}, {"StockID": "002344", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：6.54元"}], "prod_name": "海宁皮城", "HotNum": 605}, {"StockID": "600787", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：6.30元"}], "prod_name": "中储股份", "HotNum": 1458}, {"StockID": "600884", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：9.76元"}], "prod_name": "杉杉股份", "HotNum": 2316}, {"StockID": "600508", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：17.72元"}], "prod_name": "上海能源", "HotNum": 368}, {"StockID": "600658", "Tag": [{"ID": "3056", "Name": "破净活跃个股", "Reason": "每股净资产：6.11元"}], "prod_name": "电子城  ", "HotNum": 1542}], "Power": 0, "Subscribe": 0, "ZT": {"600643": ["1", "10.02", "1751007779"]}, "IsGood": 0, "GoodNum": 1671, "ComNum": 1608, "errcode": "0", "t": 0.014206999999999997}