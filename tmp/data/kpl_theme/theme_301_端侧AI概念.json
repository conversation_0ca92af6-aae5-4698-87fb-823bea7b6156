{"ID": "301", "Name": "端侧AI概念", "BriefIntro": "2025年1月20日，豆包正式推出实时语音大模型，其为一款语音理解和生成一体化的模型，实现了端到端语音对话。相比传统级联模式，在语音表现力、控制力、情绪承接方面表现惊艳，并具备低时延、对话中可随时打断等特性，引发市场对端测AI的关注", "ClassLayer": "2", "Desc": "", "PlateSwitch": "1", "StkSwitch": "2", "Introduction": "<p>题材相关新闻</p><p><br/></p><p>2025年1月20日：豆包正式推出实时语音大模型，其为一款语音理解和生成一体化的模型，实现了端到端语音对话。相比传统级联模式，在语音表现力、控制力、情绪承接方面表现惊艳，并具备低时延、对话中可随时打断等特性。</p><p><br/></p><p>题材相关介绍</p><p><br/></p><p>端测AI（End-device AI）是指在终端设备上直接运行和处理人工智能算法，无需将数据发送到云端或服务器进行处理的技术。</p><h3 class=\"\" style=\"margin: 1.14em 0px 0px; padding: 0px; border: 0px; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-stretch: inherit; font-size: 14px; line-height: 24px; font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, &quot;Segoe UI&quot;, Arial, Roboto, &quot;PingFang SC&quot;, MIUI, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, sans-serif; vertical-align: baseline; color: rgb(6, 6, 7); letter-spacing: 0.5px; background-color: rgb(255, 255, 255);\">定义</h3><p>端测AI是人工智能的一种实现方式，侧重于将AI的能力下沉到设备端，减少对云服务的依赖，从而实现更快的响应速度、更好的隐私保护和更低的网络需求。</p><h3 class=\"\" style=\"margin: 1.14em 0px 0px; padding: 0px; border: 0px; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-stretch: inherit; font-size: 14px; line-height: 24px; font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, &quot;Segoe UI&quot;, Arial, Roboto, &quot;PingFang SC&quot;, MIUI, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, sans-serif; vertical-align: baseline; color: rgb(6, 6, 7); letter-spacing: 0.5px; background-color: rgb(255, 255, 255);\">优势</h3><ol start=\"1\" style=\"margin-top: 0.859em; padding: 0px; border: 0px; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-stretch: inherit; font-size: 14px; line-height: inherit; font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, &quot;Segoe UI&quot;, Arial, Roboto, &quot;PingFang SC&quot;, MIUI, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, sans-serif; vertical-align: baseline; list-style-position: initial; list-style-image: initial; padding-inline-start: 2em; color: rgb(6, 6, 7); letter-spacing: 0.5px; background-color: rgb(255, 255, 255);\" class=\" list-paddingleft-2\"><li><p><span class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; font-style: inherit; font-variant: inherit; font-weight: 700; font-stretch: inherit; font-size: inherit; line-height: inherit; font-family: inherit; vertical-align: baseline;\">响应速度快</span>：在智能驾驶领域，端测AI可以在毫秒级别内处理车辆传感器收集到的数据，快速做出决策，避免潜在危险</p></li><li><p><span class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; font-style: inherit; font-variant: inherit; font-weight: 700; font-stretch: inherit; font-size: inherit; line-height: inherit; font-family: inherit; vertical-align: baseline;\">功耗低</span>：以智能手机为例，端测AI在人脸识别解锁过程中的功耗仅为传统方式的一半左右</p></li><li><p><span class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; font-style: inherit; font-variant: inherit; font-weight: 700; font-stretch: inherit; font-size: inherit; line-height: inherit; font-family: inherit; vertical-align: baseline;\">隐私保护强</span>：数据处理在本地完成，无需上传云端，有效保护用户隐私</p></li></ol><h3 class=\"\" style=\"margin: 1.14em 0px 0px; padding: 0px; border: 0px; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-stretch: inherit; font-size: 14px; line-height: 24px; font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, &quot;Segoe UI&quot;, Arial, Roboto, &quot;PingFang SC&quot;, MIUI, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, sans-serif; vertical-align: baseline; color: rgb(6, 6, 7); letter-spacing: 0.5px; background-color: rgb(255, 255, 255);\">应用场景</h3><ol start=\"1\" style=\"margin-top: 0.859em; padding: 0px; border: 0px; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-stretch: inherit; font-size: 14px; line-height: inherit; font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, &quot;Segoe UI&quot;, Arial, Roboto, &quot;PingFang SC&quot;, MIUI, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, sans-serif; vertical-align: baseline; list-style-position: initial; list-style-image: initial; padding-inline-start: 2em; color: rgb(6, 6, 7); letter-spacing: 0.5px; background-color: rgb(255, 255, 255);\" class=\" list-paddingleft-2\"><li><p><span class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; font-style: inherit; font-variant: inherit; font-weight: 700; font-stretch: inherit; font-size: inherit; line-height: inherit; font-family: inherit; vertical-align: baseline;\">智能手机</span>：如华为Mate70系列推出的AI隔空传送、AI消息随身等功能</p></li><li><p><span class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; font-style: inherit; font-variant: inherit; font-weight: 700; font-stretch: inherit; font-size: inherit; line-height: inherit; font-family: inherit; vertical-align: baseline;\">物联网设备</span>：如智能摄像头、智能手表等，可实现本地数据处理</p></li><li><p><span class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; font-style: inherit; font-variant: inherit; font-weight: 700; font-stretch: inherit; font-size: inherit; line-height: inherit; font-family: inherit; vertical-align: baseline;\">智能驾驶</span>：车辆传感器数据的快速处理和决策</p></li><li><p><span class=\"\" style=\"margin: 0px; padding: 0px; border: 0px; font-style: inherit; font-variant: inherit; font-weight: 700; font-stretch: inherit; font-size: inherit; line-height: inherit; font-family: inherit; vertical-align: baseline;\">办公与娱乐</span>：如OPPO Reno13系列的AI一键问屏、AI智能排版等功能</p></li></ol><p><br/></p>", "CreateTime": "1737436851", "UpdateTime": "0", "Table": [{"Level1": {"ID": "3326", "Name": "端侧模组", "ZSCode": "0", "Stocks": [{"StockID": "603236", "IsZz": "2", "IsHot": "0", "Reason": "公司主营业务是从事物联网领域无线通信模组及其解决方案的设计、研发、生产与销售服务，产品可满足不同智能终端市场的需求。", "prod_name": "移远通信", "Hot": 3002}, {"StockID": "002881", "IsZz": "2", "IsHot": "0", "Reason": "公司积极关注通信及端侧AI技术在C端应用场景的市场机会，不断提升公司在智能模组、高算力AI模组、端侧AI解决方案领域的品牌知名度", "prod_name": "美格智能", "Hot": 2981}, {"StockID": "300638", "IsZz": "2", "IsHot": "0", "Reason": "公司端侧模组搭载高性能处理器，支持多层级算力，如 SC208 采用最高 1.8GHz 的 8 核处理器，可高效处理各种数据和任务", "prod_name": "广和通", "Hot": 2451}, {"StockID": "688159", "IsZz": "2", "IsHot": "0", "Reason": "公司主营为物联网无线通信模块、物联网无线通信终端和物联网无线通信解决方案的研发、生产（外协加工方式实现）及销售", "prod_name": "有方科技", "Hot": 2249}]}, "Level2": []}, {"Level1": {"ID": "3327", "Name": "端侧芯片", "ZSCode": "0", "Stocks": [{"StockID": "603893", "IsZz": "2", "IsHot": "0", "Reason": "公司主要从事智能终端芯片及电源管理芯片的研发和销售，拥有超高清视频编解码、CPU/GPU 多核整合等 SoC设计技术", "prod_name": "瑞芯微  ", "Hot": 2156}, {"StockID": "300493", "IsZz": "2", "IsHot": "0", "Reason": "公司在智能穿戴领域的SOC芯片和近场扬声器件，有应用于客户的AR眼镜和AI眼镜产品", "prod_name": "润欣科技", "Hot": 1997}, {"StockID": "603068", "IsZz": "2", "IsHot": "0", "Reason": "博通集成与奥嘟比携手，将火山引擎豆包AI大模型融入玩具，搭载博通集成的BK7252N与BK7258芯片，推出玩具AI智能套件，为传统玩具注入AI新活力。", "prod_name": "博通集成", "Hot": 1676}, {"StockID": "300458", "IsZz": "2", "IsHot": "0", "Reason": "公司目前的主营业务为智能应用处理器SoC等的研发与设计；汤姆猫AI语音情感陪伴机器人搭载了全志科技R128高集成度无线音频芯片", "prod_name": "全志科技", "Hot": 1288}, {"StockID": "688608", "IsZz": "2", "IsHot": "0", "Reason": "公司称对自身的定位是无线超低功耗计算SoC芯片，端侧AI的发展和公司芯片的升级迭代路径是非常匹配的", "prod_name": "恒玄科技", "Hot": 698}, {"StockID": "688018", "IsZz": "2", "IsHot": "0", "Reason": "公司有多款物联网芯片产品系列，且收购了明栈信息科技 (M5Stack) 的股权，M5Stack 的产品组合主要包括物联网应用解决方案所需的控制器和其他硬件模块", "prod_name": "乐鑫科技", "Hot": 683}, {"StockID": "688332", "IsZz": "2", "IsHot": "0", "Reason": "公司主营业务为无线音频SoC芯片的研发、设计与销售，公司逐步形成以智能穿戴芯片等八大产品线为主的产品架构，产品可广泛运用于无线互联终端", "prod_name": "中科蓝讯", "Hot": 508}, {"StockID": "688099", "IsZz": "2", "IsHot": "0", "Reason": "公司多年来持续致力于端侧算力芯片的研发，目前已有超过15款芯片带NPU算力，算力范围1T~5T，产品覆盖公司除Wi-Fi芯片以外的全产品线系列", "prod_name": "晶晨股份", "Hot": 449}]}, "Level2": []}, {"Level1": {"ID": "3321", "Name": "AI玩具", "ZSCode": "801877", "Stocks": [{"StockID": "603236", "IsZz": "2", "IsHot": "0", "Reason": "公司推出了针对玩具市场的AI智能解决方案。", "prod_name": "移远通信", "Hot": 3002}, {"StockID": "300949", "IsZz": "2", "IsHot": "0", "Reason": "公司旗下品牌--洛嘉森友会出品的“森宝和朋友们&quot;系列AI玩偶首次亮相中东", "prod_name": "奥雅股份", "Hot": 2953}, {"StockID": "600128", "IsZz": "2", "IsHot": "0", "Reason": "公司控股子公司江苏弘 业永恒进出口有限公司推出的智能毛绒玩具中置入具有 AI 功能的模块，以使其具备与用户交流的能力。", "prod_name": "XD苏豪弘", "Hot": 2938}, {"StockID": "300638", "IsZz": "2", "IsHot": "0", "Reason": "广和通推出AI玩具大模型解决方案，该方案深度融合豆包等AI大模型、内置广和通Cat.1模组，助力智能玩具实现AI化升级。", "prod_name": "广和通", "Hot": 2451}, {"StockID": "002862", "IsZz": "2", "IsHot": "0", "Reason": "公司已推出基于植入AI的情感陪伴的玩具——AI魔法星，目前该玩具已在公司抖音等线上渠道预售。", "prod_name": "实丰文化", "Hot": 2332}, {"StockID": "002292", "IsZz": "2", "IsHot": "0", "Reason": "公司积极推动“IP+AI”产业化落地，推出初代AI毛绒产品“喜羊羊超能铃铛娃娃”；", "prod_name": "奥飞娱乐", "Hot": 2156}, {"StockID": "300822", "IsZz": "2", "IsHot": "0", "Reason": "公司推出的皮克方专注于猫狗类宠物的识别拍摄，自动采集原始宠物素材，智能匹配音乐智能完成拍摄、剪辑、成片，随时随地云陪伴宠物主。", "prod_name": "贝仕达克", "Hot": 1324}, {"StockID": "300458", "IsZz": "2", "IsHot": "0", "Reason": "公司研发XR871产品，可以为智能家电、智能玩具提供良好的WiFi联网和云连接应用。", "prod_name": "全志科技", "Hot": 1288}, {"StockID": "300459", "IsZz": "2", "IsHot": "0", "Reason": "公司汤姆猫AI语音情感陪伴机器人产品已正式开展售卖。", "prod_name": "汤姆猫", "Hot": 1037}, {"StockID": "688018", "IsZz": "2", "IsHot": "0", "Reason": "字节跳动的AI玩具“显眼包”中用的是乐鑫的芯片ESP32", "prod_name": "乐鑫科技", "Hot": 683}]}, "Level2": []}, {"Level1": {"ID": "3322", "Name": "AI眼镜", "ZSCode": "0", "Stocks": [{"StockID": "300622", "IsZz": "2", "IsHot": "0", "Reason": "目前公司在智能眼镜业务上已与星纪魅族、雷鸟创新、XREAL、界环、李未可、ROKID等品牌开展合作。", "prod_name": "博士眼镜", "Hot": 5073}, {"StockID": "002861", "IsZz": "2", "IsHot": "0", "Reason": "公司参与中国电子音响行业协会组织的《智能音频眼镜技术规范》《高清无线音频技术规范及测试方法》团体标准起草，开发各类智能眼镜、音频眼镜产品，并对骨传导音频技术投入了研发", "prod_name": "瀛通通讯", "Hot": 4060}, {"StockID": "002881", "IsZz": "2", "IsHot": "0", "Reason": "公司基于高算力AI模组，为某AR眼镜品牌客户开发的解决方案产品，目前正在研发中。", "prod_name": "美格智能", "Hot": 2981}, {"StockID": "001314", "IsZz": "2", "IsHot": "0", "Reason": "公司推出了AI眼镜全链路解决方案—SW3010", "prod_name": "亿道信息", "Hot": 2352}, {"StockID": "002045", "IsZz": "2", "IsHot": "0", "Reason": "公司重视AI硬件领域，在AI音箱，AI智慧屏、AR/VR（整机和声学模组）设备上已实现规模量产。也在积极推进AI耳机、AI眼镜的送样工作", "prod_name": "国光电器", "Hot": 1797}, {"StockID": "002577", "IsZz": "2", "IsHot": "0", "Reason": "公司此前推出智能音频眼镜主要为接打电话、音频播放等", "prod_name": "雷柏科技", "Hot": 1265}, {"StockID": "002273", "IsZz": "2", "IsHot": "0", "Reason": "公司在显示元件（反射光波导、衍射光波导等）、投影元件（光机）及其他可用于AR眼镜的光学零组件均有技术研发布局", "prod_name": "水晶光电", "Hot": 1182}, {"StockID": "688608", "IsZz": "2", "IsHot": "0", "Reason": "公司智能可穿戴主控芯片已广泛应用于TWS耳机、智能手表、智能眼镜等终端产品", "prod_name": "恒玄科技", "Hot": 698}, {"StockID": "603341", "IsZz": "2", "IsHot": "0", "Reason": "在AI眼镜领域，公司已与全球互联网头部客户持续合作两代智能眼镜产品，在AI技术的加持下，二代智能眼镜产品市场销售表现良好。", "prod_name": "龙旗科技", "Hot": 571}]}, "Level2": []}, {"Level1": {"ID": "3325", "Name": "智能穿戴", "ZSCode": "801405", "Stocks": [{"StockID": "002861", "IsZz": "2", "IsHot": "0", "Reason": "“首款具备实际应用功能的AI耳机——FIIL GS Links”，搭载中科蓝讯BT895x平台，支持豆包AI大模型，由知名声学ODM企业瀛通通讯共同开发生产。", "prod_name": "瀛通通讯", "Hot": 4060}, {"StockID": "000063", "IsZz": "2", "IsHot": "0", "Reason": "公司推出全球首款轻型双目屈光一体化GPT无线AR智能眼镜nubia Neo Air，用户可通过融合多个 AI 大模型能力的 GPT 模型实现即时问答", "prod_name": "中兴通讯", "Hot": 2783}, {"StockID": "003021", "IsZz": "2", "IsHot": "0", "Reason": "公司的微型传动系统可应用于 AR/VR 领域，为智能眼镜提供瞳距调节驱动系统", "prod_name": "兆威机电", "Hot": 1672}, {"StockID": "300433", "IsZz": "2", "IsHot": "0", "Reason": "公司是第一代智能眼镜的外观结构件核心供应商，并为北美 AI 眼镜客户提供导光模组、Mic 模组等功能模组和精密结构件", "prod_name": "蓝思科技", "Hot": 1589}, {"StockID": "002241", "IsZz": "2", "IsHot": "0", "Reason": "公司在2025年CES展会上推出Mulan2、Wood2、Euler 轻量化 PC-VR 头显、Comma 2 Touch、Comma 2 Listener等多种智能穿戴设备", "prod_name": "歌尔股份", "Hot": 1523}, {"StockID": "002273", "IsZz": "2", "IsHot": "0", "Reason": "在AR眼镜领域，公司布局多年并掌握衍射光波导、反射式光波导及折返式多条技术路径。", "prod_name": "水晶光电", "Hot": 1182}, {"StockID": "300115", "IsZz": "2", "IsHot": "0", "Reason": "公司2023年已为北美虚拟现实品牌AI眼镜提供结构件，目前重要客户的AI/AR眼镜正在开发当中。", "prod_name": "长盈精密", "Hot": 1174}]}, "Level2": []}, {"Level1": {"ID": "3324", "Name": "智能音箱", "ZSCode": "801129", "Stocks": [{"StockID": "002862", "IsZz": "2", "IsHot": "0", "Reason": "公司有研发及生产智能蓝牙音箱，如欠猪等玩具产品", "prod_name": "实丰文化", "Hot": 2332}, {"StockID": "002045", "IsZz": "2", "IsHot": "0", "Reason": "公司主要营收之一为智能音箱，并看好智能音箱的发展，认为智能音箱或成为真正的AI助手，为智能音箱的发展注入新动力", "prod_name": "国光电器", "Hot": 1797}, {"StockID": "002681", "IsZz": "2", "IsHot": "0", "Reason": "公司是新兴智能硬件领域垂直一体化解决方案供应商，较早布局智能音箱、智能穿戴、智能门锁等领域。", "prod_name": "奋达科技", "Hot": 1535}, {"StockID": "002241", "IsZz": "2", "IsHot": "0", "Reason": "公司的Focus 系列智能音箱可实现面部与手势识别功能，能够通过内置的算法和模型对用户的面部特征、手势动作进行分析和识别", "prod_name": "歌尔股份", "Hot": 1523}, {"StockID": "002351", "IsZz": "2", "IsHot": "0", "Reason": "公司已发布多款蓝牙智能音箱。其中台式蓝牙音箱包括R201T、R101BT、R1080BT等，便携式智能蓝牙音箱为M201、bun、M100、M10等。", "prod_name": "漫步者", "Hot": 529}, {"StockID": "688332", "IsZz": "2", "IsHot": "0", "Reason": "公司主要产品包括TWS蓝牙耳机芯片、非TWS蓝牙耳机芯片、蓝牙音箱芯片及智能穿戴芯片等。", "prod_name": "中科蓝讯", "Hot": 508}]}, "Level2": []}, {"Level1": {"ID": "3323", "Name": "AI手机", "ZSCode": "801828", "Stocks": [{"StockID": "000063", "IsZz": "2", "IsHot": "0", "Reason": "公司努比亚Z60 Ultra和红魔9 Pro两款旗舰手机已先后上市，分别在影像和游戏领域打造业界首个垂直AI大模型", "prod_name": "中兴通讯", "Hot": 2783}, {"StockID": "002230", "IsZz": "2", "IsHot": "0", "Reason": "华为mate70中的基于大模型的通话摘要功能、方言自由说功能以及全屋智能场景中的全屋广播等功能均由科大讯飞提供相关技术支持。", "prod_name": "科大讯飞", "Hot": 1239}, {"StockID": "002273", "IsZz": "2", "IsHot": "0", "Reason": "公司有为为三星S24和OPPO find X7的机型供应光学零组件产品。", "prod_name": "水晶光电", "Hot": 1182}, {"StockID": "603327", "IsZz": "2", "IsHot": "0", "Reason": "公司供货的三星S24系列手机、谷歌Pixel8系列手机等产品都具有AI功能。", "prod_name": "福蓉科技", "Hot": 939}, {"StockID": "300602", "IsZz": "2", "IsHot": "0", "Reason": "公司有向华为的Mate70系列手机提供相关散热及电磁屏蔽类产品。", "prod_name": "飞荣达", "Hot": 686}, {"StockID": "688036", "IsZz": "2", "IsHot": "0", "Reason": "公司旗下品牌发布多款搭载AI功能的手机，包括PHANTOM系列第二代折叠屏旗舰PHANTOM V Fold2。", "prod_name": "传音控股", "Hot": 454}]}, "Level2": []}, {"Level1": {"ID": "3328", "Name": "其他", "ZSCode": "0", "Stocks": [{"StockID": "300857", "IsZz": "2", "IsHot": "0", "Reason": "公司主营业务为消费电子领域物联网智能终端和数据存储设备等产品的研发、生产和销售。", "prod_name": "协创数据", "Hot": 1273}]}, "Level2": []}], "Stocks": [], "StockList": [{"StockID": "603236", "Tag": [{"ID": "3326", "Name": "端侧模组", "Reason": "公司主营业务是从事物联网领域无线通信模组及其解决方案的设计、研发、生产与销售服务，产品可满足不同智能终端市场的需求。"}, {"ID": "3321", "Name": "AI玩具", "Reason": "公司推出了针对玩具市场的AI智能解决方案。"}], "prod_name": "移远通信", "HotNum": 3002}, {"StockID": "688018", "Tag": [{"ID": "3327", "Name": "端侧芯片", "Reason": "公司有多款物联网芯片产品系列，且收购了明栈信息科技 (M5Stack) 的股权，M5Stack 的产品组合主要包括物联网应用解决方案所需的控制器和其他硬件模块"}, {"ID": "3321", "Name": "AI玩具", "Reason": "字节跳动的AI玩具“显眼包”中用的是乐鑫的芯片ESP32"}], "prod_name": "乐鑫科技", "HotNum": 683}, {"StockID": "002881", "Tag": [{"ID": "3326", "Name": "端侧模组", "Reason": "公司积极关注通信及端侧AI技术在C端应用场景的市场机会，不断提升公司在智能模组、高算力AI模组、端侧AI解决方案领域的品牌知名度"}, {"ID": "3322", "Name": "AI眼镜", "Reason": "公司基于高算力AI模组，为某AR眼镜品牌客户开发的解决方案产品，目前正在研发中。"}], "prod_name": "美格智能", "HotNum": 2981}, {"StockID": "300638", "Tag": [{"ID": "3326", "Name": "端侧模组", "Reason": "公司端侧模组搭载高性能处理器，支持多层级算力，如 SC208 采用最高 1.8GHz 的 8 核处理器，可高效处理各种数据和任务"}, {"ID": "3321", "Name": "AI玩具", "Reason": "广和通推出AI玩具大模型解决方案，该方案深度融合豆包等AI大模型、内置广和通Cat.1模组，助力智能玩具实现AI化升级。"}], "prod_name": "广和通", "HotNum": 2451}, {"StockID": "300458", "Tag": [{"ID": "3321", "Name": "AI玩具", "Reason": "公司研发XR871产品，可以为智能家电、智能玩具提供良好的WiFi联网和云连接应用。"}, {"ID": "3327", "Name": "端侧芯片", "Reason": "公司目前的主营业务为智能应用处理器SoC等的研发与设计；汤姆猫AI语音情感陪伴机器人搭载了全志科技R128高集成度无线音频芯片"}], "prod_name": "全志科技", "HotNum": 1288}, {"StockID": "603068", "Tag": [{"ID": "3327", "Name": "端侧芯片", "Reason": "博通集成与奥嘟比携手，将火山引擎豆包AI大模型融入玩具，搭载博通集成的BK7252N与BK7258芯片，推出玩具AI智能套件，为传统玩具注入AI新活力。"}], "prod_name": "博通集成", "HotNum": 1676}, {"StockID": "002862", "Tag": [{"ID": "3321", "Name": "AI玩具", "Reason": "公司已推出基于植入AI的情感陪伴的玩具——AI魔法星，目前该玩具已在公司抖音等线上渠道预售。"}, {"ID": "3324", "Name": "智能音箱", "Reason": "公司有研发及生产智能蓝牙音箱，如欠猪等玩具产品"}], "prod_name": "实丰文化", "HotNum": 2332}, {"StockID": "300949", "Tag": [{"ID": "3321", "Name": "AI玩具", "Reason": "公司旗下品牌--洛嘉森友会出品的“森宝和朋友们&quot;系列AI玩偶首次亮相中东"}], "prod_name": "奥雅股份", "HotNum": 2953}, {"StockID": "002292", "Tag": [{"ID": "3321", "Name": "AI玩具", "Reason": "公司积极推动“IP+AI”产业化落地，推出初代AI毛绒产品“喜羊羊超能铃铛娃娃”；"}], "prod_name": "奥飞娱乐", "HotNum": 2156}, {"StockID": "300459", "Tag": [{"ID": "3321", "Name": "AI玩具", "Reason": "公司汤姆猫AI语音情感陪伴机器人产品已正式开展售卖。"}], "prod_name": "汤姆猫", "HotNum": 1037}, {"StockID": "300822", "Tag": [{"ID": "3321", "Name": "AI玩具", "Reason": "公司推出的皮克方专注于猫狗类宠物的识别拍摄，自动采集原始宠物素材，智能匹配音乐智能完成拍摄、剪辑、成片，随时随地云陪伴宠物主。"}], "prod_name": "贝仕达克", "HotNum": 1324}, {"StockID": "002861", "Tag": [{"ID": "3322", "Name": "AI眼镜", "Reason": "公司参与中国电子音响行业协会组织的《智能音频眼镜技术规范》《高清无线音频技术规范及测试方法》团体标准起草，开发各类智能眼镜、音频眼镜产品，并对骨传导音频技术投入了研发"}, {"ID": "3325", "Name": "智能穿戴", "Reason": "“首款具备实际应用功能的AI耳机——FIIL GS Links”，搭载中科蓝讯BT895x平台，支持豆包AI大模型，由知名声学ODM企业瀛通通讯共同开发生产。"}], "prod_name": "瀛通通讯", "HotNum": 4060}, {"StockID": "603341", "Tag": [{"ID": "3322", "Name": "AI眼镜", "Reason": "在AI眼镜领域，公司已与全球互联网头部客户持续合作两代智能眼镜产品，在AI技术的加持下，二代智能眼镜产品市场销售表现良好。"}], "prod_name": "龙旗科技", "HotNum": 571}, {"StockID": "688608", "Tag": [{"ID": "3322", "Name": "AI眼镜", "Reason": "公司智能可穿戴主控芯片已广泛应用于TWS耳机、智能手表、智能眼镜等终端产品"}, {"ID": "3327", "Name": "端侧芯片", "Reason": "公司称对自身的定位是无线超低功耗计算SoC芯片，端侧AI的发展和公司芯片的升级迭代路径是非常匹配的"}], "prod_name": "恒玄科技", "HotNum": 698}, {"StockID": "300622", "Tag": [{"ID": "3322", "Name": "AI眼镜", "Reason": "目前公司在智能眼镜业务上已与星纪魅族、雷鸟创新、XREAL、界环、李未可、ROKID等品牌开展合作。"}], "prod_name": "博士眼镜", "HotNum": 5073}, {"StockID": "002577", "Tag": [{"ID": "3322", "Name": "AI眼镜", "Reason": "公司此前推出智能音频眼镜主要为接打电话、音频播放等"}], "prod_name": "雷柏科技", "HotNum": 1265}, {"StockID": "001314", "Tag": [{"ID": "3322", "Name": "AI眼镜", "Reason": "公司推出了AI眼镜全链路解决方案—SW3010"}], "prod_name": "亿道信息", "HotNum": 2352}, {"StockID": "002273", "Tag": [{"ID": "3322", "Name": "AI眼镜", "Reason": "公司在显示元件（反射光波导、衍射光波导等）、投影元件（光机）及其他可用于AR眼镜的光学零组件均有技术研发布局"}, {"ID": "3323", "Name": "AI手机", "Reason": "公司有为为三星S24和OPPO find X7的机型供应光学零组件产品。"}, {"ID": "3325", "Name": "智能穿戴", "Reason": "在AR眼镜领域，公司布局多年并掌握衍射光波导、反射式光波导及折返式多条技术路径。"}], "prod_name": "水晶光电", "HotNum": 1182}, {"StockID": "002045", "Tag": [{"ID": "3322", "Name": "AI眼镜", "Reason": "公司重视AI硬件领域，在AI音箱，AI智慧屏、AR/VR（整机和声学模组）设备上已实现规模量产。也在积极推进AI耳机、AI眼镜的送样工作"}, {"ID": "3324", "Name": "智能音箱", "Reason": "公司主要营收之一为智能音箱，并看好智能音箱的发展，认为智能音箱或成为真正的AI助手，为智能音箱的发展注入新动力"}], "prod_name": "国光电器", "HotNum": 1797}, {"StockID": "603327", "Tag": [{"ID": "3323", "Name": "AI手机", "Reason": "公司供货的三星S24系列手机、谷歌Pixel8系列手机等产品都具有AI功能。"}], "prod_name": "福蓉科技", "HotNum": 939}, {"StockID": "688036", "Tag": [{"ID": "3323", "Name": "AI手机", "Reason": "公司旗下品牌发布多款搭载AI功能的手机，包括PHANTOM系列第二代折叠屏旗舰PHANTOM V Fold2。"}], "prod_name": "传音控股", "HotNum": 454}, {"StockID": "300602", "Tag": [{"ID": "3323", "Name": "AI手机", "Reason": "公司有向华为的Mate70系列手机提供相关散热及电磁屏蔽类产品。"}], "prod_name": "飞荣达", "HotNum": 686}, {"StockID": "002230", "Tag": [{"ID": "3323", "Name": "AI手机", "Reason": "华为mate70中的基于大模型的通话摘要功能、方言自由说功能以及全屋智能场景中的全屋广播等功能均由科大讯飞提供相关技术支持。"}], "prod_name": "科大讯飞", "HotNum": 1239}, {"StockID": "688332", "Tag": [{"ID": "3324", "Name": "智能音箱", "Reason": "公司主要产品包括TWS蓝牙耳机芯片、非TWS蓝牙耳机芯片、蓝牙音箱芯片及智能穿戴芯片等。"}, {"ID": "3327", "Name": "端侧芯片", "Reason": "公司主营业务为无线音频SoC芯片的研发、设计与销售，公司逐步形成以智能穿戴芯片等八大产品线为主的产品架构，产品可广泛运用于无线互联终端"}], "prod_name": "中科蓝讯", "HotNum": 508}, {"StockID": "002241", "Tag": [{"ID": "3324", "Name": "智能音箱", "Reason": "公司的Focus 系列智能音箱可实现面部与手势识别功能，能够通过内置的算法和模型对用户的面部特征、手势动作进行分析和识别"}, {"ID": "3325", "Name": "智能穿戴", "Reason": "公司在2025年CES展会上推出Mulan2、Wood2、Euler 轻量化 PC-VR 头显、Comma 2 Touch、Comma 2 Listener等多种智能穿戴设备"}], "prod_name": "歌尔股份", "HotNum": 1523}, {"StockID": "002351", "Tag": [{"ID": "3324", "Name": "智能音箱", "Reason": "公司已发布多款蓝牙智能音箱。其中台式蓝牙音箱包括R201T、R101BT、R1080BT等，便携式智能蓝牙音箱为M201、bun、M100、M10等。"}], "prod_name": "漫步者", "HotNum": 529}, {"StockID": "002681", "Tag": [{"ID": "3324", "Name": "智能音箱", "Reason": "公司是新兴智能硬件领域垂直一体化解决方案供应商，较早布局智能音箱、智能穿戴、智能门锁等领域。"}], "prod_name": "奋达科技", "HotNum": 1535}, {"StockID": "003021", "Tag": [{"ID": "3325", "Name": "智能穿戴", "Reason": "公司的微型传动系统可应用于 AR/VR 领域，为智能眼镜提供瞳距调节驱动系统"}], "prod_name": "兆威机电", "HotNum": 1672}, {"StockID": "300857", "Tag": [{"ID": "3328", "Name": "其他", "Reason": "公司主营业务为消费电子领域物联网智能终端和数据存储设备等产品的研发、生产和销售。"}], "prod_name": "协创数据", "HotNum": 1273}, {"StockID": "000063", "Tag": [{"ID": "3325", "Name": "智能穿戴", "Reason": "公司推出全球首款轻型双目屈光一体化GPT无线AR智能眼镜nubia Neo Air，用户可通过融合多个 AI 大模型能力的 GPT 模型实现即时问答"}, {"ID": "3323", "Name": "AI手机", "Reason": "公司努比亚Z60 Ultra和红魔9 Pro两款旗舰手机已先后上市，分别在影像和游戏领域打造业界首个垂直AI大模型"}], "prod_name": "中兴通讯", "HotNum": 2783}, {"StockID": "300115", "Tag": [{"ID": "3325", "Name": "智能穿戴", "Reason": "公司2023年已为北美虚拟现实品牌AI眼镜提供结构件，目前重要客户的AI/AR眼镜正在开发当中。"}], "prod_name": "长盈精密", "HotNum": 1174}, {"StockID": "300433", "Tag": [{"ID": "3325", "Name": "智能穿戴", "Reason": "公司是第一代智能眼镜的外观结构件核心供应商，并为北美 AI 眼镜客户提供导光模组、Mic 模组等功能模组和精密结构件"}], "prod_name": "蓝思科技", "HotNum": 1589}, {"StockID": "603893", "Tag": [{"ID": "3327", "Name": "端侧芯片", "Reason": "公司主要从事智能终端芯片及电源管理芯片的研发和销售，拥有超高清视频编解码、CPU/GPU 多核整合等 SoC设计技术"}], "prod_name": "瑞芯微  ", "HotNum": 2156}, {"StockID": "600128", "Tag": [{"ID": "3321", "Name": "AI玩具", "Reason": "公司控股子公司江苏弘 业永恒进出口有限公司推出的智能毛绒玩具中置入具有 AI 功能的模块，以使其具备与用户交流的能力。"}], "prod_name": "XD苏豪弘", "HotNum": 2938}, {"StockID": "688159", "Tag": [{"ID": "3326", "Name": "端侧模组", "Reason": "公司主营为物联网无线通信模块、物联网无线通信终端和物联网无线通信解决方案的研发、生产（外协加工方式实现）及销售"}], "prod_name": "有方科技", "HotNum": 2249}, {"StockID": "300493", "Tag": [{"ID": "3327", "Name": "端侧芯片", "Reason": "公司在智能穿戴领域的SOC芯片和近场扬声器件，有应用于客户的AR眼镜和AI眼镜产品"}], "prod_name": "润欣科技", "HotNum": 1997}, {"StockID": "688099", "Tag": [{"ID": "3327", "Name": "端侧芯片", "Reason": "公司多年来持续致力于端侧算力芯片的研发，目前已有超过15款芯片带NPU算力，算力范围1T~5T，产品覆盖公司除Wi-Fi芯片以外的全产品线系列"}], "prod_name": "晶晨股份", "HotNum": 449}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 788, "ComNum": 309, "errcode": "0", "t": 0.010741999999999918}