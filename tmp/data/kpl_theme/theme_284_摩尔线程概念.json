{"ID": "284", "Name": "摩尔线程概念", "BriefIntro": "11月9日，科创板日报记者从知情人士获悉，国产GPU独角兽摩尔线程已完成股改，近期将启动上市辅导，首选目标为科创板。", "ClassLayer": "1", "Desc": "", "PlateSwitch": "2", "StkSwitch": "2", "Introduction": "<p>题材相关新闻</p><p><br/></p><p>2024年11月9日：《科创板日报》记者从知情人士获悉，国产GPU独角兽摩尔线程已完成股改，近期将启动上市辅导，首选目标为科创板</p><p><br/></p><p>题材相关介绍</p><p><br/></p><p><span style=\"font-size: 17px; letter-spacing: 0.5px; background-color: rgb(255, 255, 255); color: rgb(51, 51, 51); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, sans-serif;\">摩尔线程成立于2020年10月，法人代表为张建中，是拥有自主知识产权的全功能GPU创新企业。公司成立四年来，迭代了四代芯片，去年搭建了首个国产全功能GPU千卡智算集群，万卡智算集群正在攻关。</span></p><p style=\"box-sizing: inherit; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); margin-top: 2rem; border-width: 0px; border-style: initial; border-color: initial; vertical-align: baseline; background: rgb(255, 255, 255); color: rgb(51, 51, 51); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, sans-serif; margin-bottom: 32px; line-height: 1.75em;\"><span style=\"font-size: 17px;letter-spacing: 0.5px;\">根据国家知识产权局的数据，截至2024年10月，摩尔线程获得425项授权专利，位居国内GPU企业专利授权数量前列。</span></p><p style=\"box-sizing: inherit; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); margin-top: 2rem; border-width: 0px; border-style: initial; border-color: initial; vertical-align: baseline; background: rgb(255, 255, 255); color: rgb(51, 51, 51); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, sans-serif; margin-bottom: 32px; line-height: 1.75em;\"><span style=\"font-size: 17px;letter-spacing: 0.5px;\">摩尔线程核心团队来自英伟达，创始人张建中为原英伟达中国区总经理、英伟达全球副总裁。</span></p><p style=\"box-sizing: inherit; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); margin-top: 2rem; border-width: 0px; border-style: initial; border-color: initial; vertical-align: baseline; background: rgb(255, 255, 255); color: rgb(51, 51, 51); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, sans-serif; margin-bottom: 32px; line-height: 1.75em;\"><span style=\"font-size: 17px;letter-spacing: 0.5px;\"></span></p><p style=\"box-sizing: inherit; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); margin-top: 2rem; border-width: 0px; border-style: initial; border-color: initial; vertical-align: baseline; background: rgb(255, 255, 255); color: rgb(51, 51, 51); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, sans-serif; margin-bottom: 32px; line-height: 1.75em;\"><span style=\"font-size: 17px;letter-spacing: 0.5px;\">财联社创投通数据显示，成立至今，摩尔线程共经历六轮融资，背后聚集一众知名投资方，包括红杉中国、深创投、腾讯投资、中关村科学城等。</span><span style=\"font-size: 17px;letter-spacing: 0.5px;color: rgb(0, 122, 170);\"><strong style=\"box-sizing: inherit;-webkit-tap-highlight-color: rgba(0, 0, 0, 0);border-width: 0px;border-style: initial;border-color: initial;vertical-align: baseline;background: transparent;\">在2022年12月完成15亿元B轮融资时,其估值已达到240亿元，跻身独角兽公司之列。</strong></span></p><p style=\"box-sizing: inherit; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); margin-top: 2rem; border-width: 0px; border-style: initial; border-color: initial; vertical-align: baseline; background: rgb(255, 255, 255); color: rgb(51, 51, 51); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, sans-serif; margin-bottom: 32px; line-height: 1.75em;\"><span style=\"font-size: 17px;letter-spacing: 0.5px;\">当前算力需求还在不断增加，而对于GPU企业而言，面临挑战的同时也带来了新的机会点。</span></p><p style=\"box-sizing: inherit; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); margin-top: 2rem; border-width: 0px; border-style: initial; border-color: initial; vertical-align: baseline; background: rgb(255, 255, 255); color: rgb(51, 51, 51); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, sans-serif; margin-bottom: 32px; line-height: 1.75em;\"><span style=\"font-size: 17px;letter-spacing: 0.5px;\">2023年，摩尔线程推出了软硬一体的夸娥KUAE智算集群，可以扩展至千卡和万卡，以打造大模型和通用人工智能的先进算力基础设施。该集群已与智谱AI、智源研究院、无问芯穹、滴普科技、瑞莱智慧、实在智能等国内众多大模型企业，基于夸娥智算集群完成了不同参数量级的大模型训练与推理测试。</span></p><p style=\"box-sizing: inherit; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); margin-top: 2rem; border-width: 0px; border-style: initial; border-color: initial; vertical-align: baseline; background: rgb(255, 255, 255); color: rgb(51, 51, 51); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, sans-serif; margin-bottom: 32px; line-height: 1.75em;\"><span style=\"font-size: 17px;letter-spacing: 0.5px;\">在今年⾦融街论坛年会上，该公司就“摩尔线程国产全功能GPU万卡通用智算集群科研项目”，与浦发银行、邮储银行和北京银行进行现场签约。</span></p><p style=\"box-sizing: inherit; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); margin-top: 2rem; border-width: 0px; border-style: initial; border-color: initial; vertical-align: baseline; background: rgb(255, 255, 255); color: rgb(51, 51, 51); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, sans-serif; margin-bottom: 32px; line-height: 1.75em;\"><span style=\"font-size: 17px;letter-spacing: 0.5px;\">摩尔线程相关负责人告诉创投日报记者，公司将继续致力于持续优化GPU产品，以支持大规模大模型的训练和推理。</span></p><p style=\"box-sizing: inherit; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); margin-top: 2rem; border-width: 0px; border-style: initial; border-color: initial; vertical-align: baseline; background: rgb(255, 255, 255); color: rgb(51, 51, 51); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, sans-serif; margin-bottom: 32px; line-height: 1.75em;\"><span style=\"font-size: 17px;letter-spacing: 0.5px;\"><br/></span><br/></p><p><br/></p>", "CreateTime": "1731215574", "UpdateTime": "0", "Table": [], "Stocks": [{"StockID": "002402", "IsZz": "2", "IsHot": "1", "Reason": "摩尔线程为公司的参股公司，持有摩尔线程1.244%股份。", "prod_name": "和而泰", "Hot": 24883}, {"StockID": "300476", "IsZz": "2", "IsHot": "0", "Reason": "公司与摩尔线程有业务合作。", "prod_name": "胜宏科技", "Hot": 4565}, {"StockID": "000066", "IsZz": "2", "IsHot": "0", "Reason": "公司为摩尔线程 PES 完美体验系统联盟合作伙伴", "prod_name": "中国长城", "Hot": 2954}, {"StockID": "600797", "IsZz": "2", "IsHot": "0", "Reason": "子公司浙江网新图灵电子为摩尔线程分销商", "prod_name": "浙大网新", "Hot": 2752}, {"StockID": "002335", "IsZz": "2", "IsHot": "0", "Reason": "11月12日公司在互动平台表示：与摩尔线程在数据中心业务（包括液冷）、算力业务积极开展合作", "prod_name": "科华数据", "Hot": 2713}, {"StockID": "002065", "IsZz": "2", "IsHot": "0", "Reason": "互动易称携手摩尔线程计划投建新一代全功能国产GPU服务器生产运营总部基地", "prod_name": "东华软件", "Hot": 2454}, {"StockID": "688316", "IsZz": "2", "IsHot": "0", "Reason": "公司与与海光、摩尔线程等展开更深层的融合创新，推动了产业链的成熟。", "prod_name": "青云科技", "Hot": 2207}, {"StockID": "601360", "IsZz": "2", "IsHot": "0", "Reason": "360大模型产品与摩尔线程完成适配，基于摩尔线程夸娥（KUAE）千卡智算集群，360集团已成功完成了70亿和700亿参数的大模型分布式训练任务", "prod_name": "三六零  ", "Hot": 1980}, {"StockID": "300383", "IsZz": "2", "IsHot": "0", "Reason": "公司及子公司光环云数据有限公司与摩尔线程智能科技（北京）有限责任公司在北京签署战略合作框架协议。", "prod_name": "光环新网", "Hot": 1935}, {"StockID": "300657", "IsZz": "2", "IsHot": "0", "Reason": "公司与摩尔线程智能科技（北京）有限责任公司双方于 2023 年 6 月 18 日签署了《战略合作框架协议》。", "prod_name": "弘信电子", "Hot": 1674}, {"StockID": "301248", "IsZz": "2", "IsHot": "0", "Reason": "与摩尔线程、景嘉微等众多主流国产厂商成功开展互认证测试", "prod_name": "杰创智能", "Hot": 966}, {"StockID": "688368", "IsZz": "2", "IsHot": "0", "Reason": "公司DC/DC 产品意向客户包括摩尔线程", "prod_name": "晶丰明源", "Hot": 504}], "StockList": [{"StockID": "002402", "Tag": [], "prod_name": "和而泰", "HotNum": 24883}, {"StockID": "300657", "Tag": [], "prod_name": "弘信电子", "HotNum": 1674}, {"StockID": "002065", "Tag": [], "prod_name": "东华软件", "HotNum": 2454}, {"StockID": "688316", "Tag": [], "prod_name": "青云科技", "HotNum": 2207}, {"StockID": "300476", "Tag": [], "prod_name": "胜宏科技", "HotNum": 4565}, {"StockID": "300383", "Tag": [], "prod_name": "光环新网", "HotNum": 1935}, {"StockID": "600797", "Tag": [], "prod_name": "浙大网新", "HotNum": 2752}, {"StockID": "301248", "Tag": [], "prod_name": "杰创智能", "HotNum": 966}, {"StockID": "688368", "Tag": [], "prod_name": "晶丰明源", "HotNum": 504}, {"StockID": "000066", "Tag": [], "prod_name": "中国长城", "HotNum": 2954}, {"StockID": "002335", "Tag": [], "prod_name": "科华数据", "HotNum": 2713}, {"StockID": "601360", "Tag": [], "prod_name": "三六零  ", "HotNum": 1980}], "Power": 0, "Subscribe": 0, "ZT": {"002402": ["1", "10.00", "1751007768"]}, "IsGood": 0, "GoodNum": 500, "ComNum": 504, "errcode": "0", "t": 0.005216999999999999}