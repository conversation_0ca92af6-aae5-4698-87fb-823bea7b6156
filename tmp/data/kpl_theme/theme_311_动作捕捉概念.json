{"ID": "311", "Name": "动作捕捉概念", "BriefIntro": "2025年3月10日：智元发布首个通用具身基座大模型GO-1，GO-1开创性提出Vision-Language-Latent-Action（ViLLA）架构。其MoE中的Latent Planner（隐式规划器）借助大量跨本体和人类操作视频数据获得通用的动作理解能力，MoE中的Action Expert（动作专家）借助百万真机数据获得精细的动作执行能力。", "ClassLayer": "1", "Desc": "", "PlateSwitch": "1", "StkSwitch": "2", "Introduction": "<p>题材相关新闻</p><p>2025年3月10日：</p><p>智元发布首个通用具身基座大模型GO-1，GO-1开创性提出Vision-Language-Latent-Action（ViLLA）架构。该架构由VLM（多模态大模型）+ MoE（混合专家）组成，其中VLM借助海量互联网图文数据获得通用场景感知和语言理解能力，MoE中的Latent Planner（隐式规划器）借助大量跨本体和人类操作视频数据获得通用的动作理解能力，MoE中的Action Expert（动作专家）借助百万真机数据获得精细的动作执行能力。</p><p><br/></p><p>题材相关介绍</p><p>一、技术定义与核心原理</p><p>动作捕捉：是一种通过传感器、摄像头等设备记录运动物体关键部位（如人体关节）的空间位置和姿态数据，并将其转化为计算机可处理的三维运动轨迹的技术。</p><p>其核心流程包括：</p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans CN&quot;, sans-serif; background-color: rgb(252, 252, 252);\"></span></p><p>1、数据采集：在目标对象上部署标记点（Marker）或传感器，通过光学、惯性或电磁等方式捕捉运动信号。</p><p>2、数据处理：利用计算机视觉、惯性导航等算法将原始数据转换为骨骼动画或物理模型可驱动的坐标信息。</p><p>3、动作映射：将数字化动作应用于虚拟角色、机器人或分析系统，实现动作复现或行为分析</p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans CN&quot;, sans-serif; background-color: rgb(252, 252, 252);\"><br/></span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans CN&quot;, sans-serif; background-color: rgb(252, 252, 252);\">二、低成本方式获得高质量数据是人形机器人大规模量产前要解决的关键问题</span></p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans CN&quot;, sans-serif; background-color: rgb(252, 252, 252);\">数据是人形机器人AI/具身智能与传统 AI的最大区别之一，数据量提升是人形机器人通用性提高的重要途径。人形机器人追求的是能够像人类一样思考、运动，精确地模仿人类动作是实现这一目标的重要路径。高质量的人类运动训练数据能提升机器人的灵活性和协调性。相比于工业机器人仅具备处理单一或有限范围问题的能力，通过精确编程实现。人形机器人面向更广泛、更多样的泛化任务。泛化能力提升需要海量的3D数据进行训练。</span></p><p>数据精度直接影响到机器人准确复制人类动作的能力。即使是运动数据中的微小错误，也可能导致机器人误操作或损坏，尤其是在需要精细运动技能的任务以及与人的互动过程中。</p><p>因此，大规模、高质量、低成本的数据采集是当前人形机器人发展的关键。</p><p><span style=\"color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans CN&quot;, sans-serif; background-color: rgb(252, 252, 252);\"></span><br/></p><p>三、动作捕捉或是当下人形机器人最主流的数据采集方式</p><p>人形机器人数据采集方法包括遥控操作、动作捕捉和大模型三大类，而将动作捕捉与AI学习算法结合，正使得人形机器人越来越多地高精度模仿人类动作。</p><p>动作捕捉的基本原理是开发人员重复执行任务，对应他的每个动作将被动作捕捉技术仔细捕捉，远程操作过程中，人形机器人将将在开发人员旁边执行相同的操作，并通过记录的动作数据学习如何移动，因而动作捕捉采集的运动数据精度更高，才能能力更全面。</p><p>动作捕捉按技术原理主要分为光学式、惯性式。</p><p>（1）光学式基于光学原理完成物体捕捉和定位，通常借助整套光学摄像头完成动作捕捉。</p><p>（2）惯性动作捕捉：利用惯性传感器测量被测物体的加速度、方向、倾斜角度等。</p><p>人形机器人在运动过程中，更多涉及检测平衡、跌倒、抗扰的需求，因而采用惯性动作捕捉可实现更精准的数据采<br/></p><p><img src=\"https://appresi.longhuvip.com/uploadImg/xuetang/article/202503/1741664320920129.png\" title=\"1741664283493613.png\" alt=\"image.png\"/></p><p>动态捕捉企业除拥有传感器、整套动捕装备研发生产能力外，在软件SDK开发，以及大数据的积累也是企业的核心能力。</p>", "CreateTime": "1741661980", "UpdateTime": "0", "Table": [], "Stocks": [{"StockID": "002354", "IsZz": "2", "IsHot": "0", "Reason": "专注于AI虚拟数字人开发涉及的人物建模、动作捕捉、实时渲染、AI驱动、虚拟直播等技术研发。", "prod_name": "天娱数科", "Hot": 3190}, {"StockID": "002403", "IsZz": "2", "IsHot": "0", "Reason": "曼恒数字（公司参股2.88%）研发出DVS3D虚拟现实软件、IM沉浸式虚拟现实系统、3D-LED虚拟现实交互系统和G-Motion动作捕捉系统等多款首创性虚拟现实产品", "prod_name": "爱仕达", "Hot": 2497}, {"StockID": "000810", "IsZz": "2", "IsHot": "0", "Reason": "公司pancake 1 PRO 产品具备眼球追踪、手势识别等性能的创维，该新一代产品手柄嵌入精密摄像头，能够精准捕捉身体动作并进行传感", "prod_name": "创维数字", "Hot": 2156}, {"StockID": "002292", "IsZz": "2", "IsHot": "0", "Reason": "公司参股诺亦腾，诺亦腾是智能感知与沉浸式交互技术提供商，其产品包括动作捕捉系统、商用虚拟现实整体解决方案等，已被多家企业采购使用", "prod_name": "奥飞娱乐", "Hot": 2156}, {"StockID": "605118", "IsZz": "2", "IsHot": "0", "Reason": "公司光学镜头产品广泛应用于动作捕捉等新兴消费类电子领域", "prod_name": "力鼎光电", "Hot": 1799}, {"StockID": "300081", "IsZz": "2", "IsHot": "0", "Reason": "公司研发了一套基于UE5的动作捕捉虚拟现实拍摄系统，已经应用在动画番剧的生产中。公司目前正在研发基于AI+单目摄像头的动捕方式。", "prod_name": "恒信东方", "Hot": 1623}, {"StockID": "002995", "IsZz": "2", "IsHot": "0", "Reason": "目前公司持有世优科技7.6887%的股权", "prod_name": "天地在线", "Hot": 1343}, {"StockID": "300845", "IsZz": "2", "IsHot": "0", "Reason": "互动易称，公司采用先进的动作捕捉虚拟灭火综合体验系统和心肺复苏急救体验系统已在推广销售", "prod_name": "捷安高科", "Hot": 847}, {"StockID": "300296", "IsZz": "2", "IsHot": "0", "Reason": "子公司虚拟动点联合松延动力成立了“具身智能机器人联合实验室”，深耕光学动作捕捉领域", "prod_name": "利亚德", "Hot": 828}, {"StockID": "688400", "IsZz": "2", "IsHot": "0", "Reason": "全资子公司元客视界孵化了智能光场建模、智能运动捕捉、智能全景拍摄系统、智能 XR 拍摄系统与智能创作平台赋能行业", "prod_name": "凌云光  ", "Hot": 565}, {"StockID": "300182", "IsZz": "2", "IsHot": "0", "Reason": "参股世优科技，世优科技自主研发的入门级高性价比的全套动捕产品及软件算法提供手部、面部、全身动作捕捉", "prod_name": "捷成股份", "Hot": 496}, {"StockID": "300232", "IsZz": "2", "IsHot": "0", "Reason": "公司自研非穿戴式人体姿态动作捕捉系统（简称UAction），是一款面向人体动作实时捕捉的系统软件。", "prod_name": "洲明科技", "Hot": 445}], "StockList": [{"StockID": "300296", "Tag": [], "prod_name": "利亚德", "HotNum": 828}, {"StockID": "300081", "Tag": [], "prod_name": "恒信东方", "HotNum": 1623}, {"StockID": "688400", "Tag": [], "prod_name": "凌云光  ", "HotNum": 565}, {"StockID": "300232", "Tag": [], "prod_name": "洲明科技", "HotNum": 445}, {"StockID": "300182", "Tag": [], "prod_name": "捷成股份", "HotNum": 496}, {"StockID": "002995", "Tag": [], "prod_name": "天地在线", "HotNum": 1343}, {"StockID": "000810", "Tag": [], "prod_name": "创维数字", "HotNum": 2156}, {"StockID": "300845", "Tag": [], "prod_name": "捷安高科", "HotNum": 847}, {"StockID": "002403", "Tag": [], "prod_name": "爱仕达", "HotNum": 2497}, {"StockID": "002292", "Tag": [], "prod_name": "奥飞娱乐", "HotNum": 2156}, {"StockID": "002354", "Tag": [], "prod_name": "天娱数科", "HotNum": 3190}, {"StockID": "605118", "Tag": [], "prod_name": "力鼎光电", "HotNum": 1799}], "Power": 0, "Subscribe": 0, "ZT": [], "IsGood": 0, "GoodNum": 354, "ComNum": 583, "errcode": "0", "t": 0.011258999999999991}