import os
import json
from datetime import datetime
import re

# 读一个文件并加载为 json


def compress_reason_text(text):
    """
    压缩和清理 'Reason' 文本.
    - 移除日期
    - 移除常见的引导性短语
    - 清理多余的空格和标点
    """
    if not isinstance(text, str):
        return ""

    # 1. 移除日期和常见的引导词
    patterns_to_remove = [
        r'^\d{4}年\d{1,2}月\d{1,2}日',  # 移除日期
        r'^(互动易回复：|投资者关系记录表：|投资者互动：|公司官网简介：|据公司官网：|根据招股说明书：|公司在互动平台称：|据.*年报：|2022年年报显示：|公司2023年半年报：)',
        r'^(公司|其)'  # 移除句首的"公司"或"其"
    ]

    compressed_text = text
    for pattern in patterns_to_remove:
        compressed_text = re.sub(pattern, '', compressed_text).strip()

    # 2. 清理句首可能遗留的 "公司"
    if compressed_text.startswith('公司'):
        compressed_text = compressed_text[2:]

    # 3. 清理多余的空格和末尾的重复句号
    compressed_text = re.sub(r'\s+', ' ', compressed_text).strip()
    compressed_text = re.sub(r'。。$', '。', compressed_text)

    return compressed_text if compressed_text else text


def parse_and_format_json(data, compress=False):
    """
    解析JSON数据并格式化为Markdown。

    :param data: 输入的JSON数据。
    :param compress:布尔值，为True时，表格内容将被压缩。
    """
    # --- 基础信息提取 ---
    name = data.get("Name", "N/A")
    create_time_stamp = int(data.get("CreateTime", 0))
    if create_time_stamp == 0:
        create_time = "N/A"
    else:
        create_time = datetime.fromtimestamp(create_time_stamp).strftime('%Y-%m-%d %H:%M:%S')

    output = f"**Name:** {name}\n"
    output += f"**CreateTime:** {create_time}\n\n"

    # --- 表格生成 ---
    table_data = data.get("Table", [])
    md_table = "| 一级分类 | 二级分类 | 相关股票 |\n"
    md_table += "|:---|:---|:---|\n"

    for item in table_data:
        level1_info = item.get("Level1", {})
        level1_name = level1_info.get("Name", "")
        level1_stocks = level1_info.get("Stocks", [])
        level2_list = item.get("Level2", [])
        is_first_row_for_level1 = True
        # 处理直接隶属于一级分类的股票
        if level1_stocks:
            stocks_str = ""
            for stock in level1_stocks:
                reason = stock.get('Reason', 'N/A')
                if compress:
                    reason = compress_reason_text(reason)
                    separator = "; "
                else:
                    separator = "<br/><br/>"
                stocks_str += f"{stock.get('prod_name', 'N/A').strip()} ({stock.get('StockID', 'N/A')}): {reason}{separator}"
            level1_cell = f"{level1_name}" if is_first_row_for_level1 else ""
            md_table += f"| {level1_cell} | N/A | {stocks_str.rstrip('<br/><br/>; ')} |\n"
            is_first_row_for_level1 = False
        # 处理二级分类下的股票
        if level2_list:
            for l2_item in level2_list:
                level2_name = l2_item.get("Name", "")
                stocks = l2_item.get("Stocks", [])
                stocks_str = ""
                if not stocks:
                    stocks_str = "N/A"
                else:
                    for stock in stocks:
                        reason = stock.get('Reason', 'N/A')
                        if compress:
                            reason = compress_reason_text(reason)
                            separator = "; "
                        else:
                            separator = " "
                        stocks_str += f"{stock.get('prod_name', 'N/A').strip()} ({stock.get('StockID', 'N/A')}): {reason}{separator}"
                level1_cell = f"{level1_name}" if is_first_row_for_level1 else ""
                md_table += f"| {level1_cell} | {level2_name} | {stocks_str.rstrip('; ')} |\n"
                is_first_row_for_level1 = False
    output += "**Data Table:**\n" + md_table
    return output


def main():
    """
    主处理流程：遍历tmp/data/kpl_theme目录下所有json文件，解析并输出为md文件到kpl_theme_clean目录。
    """
    input_dir = 'data/kpl_theme'
    output_dir = 'data/kpl_theme_clean'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    files = [f for f in os.listdir(input_dir) if f.endswith('.json')]
    print(f"共检测到{len(files)}个json文件，开始处理...")
    for fname in files:
        in_path = os.path.join(input_dir, fname)
        out_path = os.path.join(output_dir, fname.replace('.json', '.md'))
        try:
            with open(in_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            md_content = parse_and_format_json(data, compress=True)
            with open(out_path, 'w', encoding='utf-8') as f:
                f.write(md_content)
            print(f"已处理: {fname} -> {out_path}")
        except Exception as e:
            print(f"处理{fname}时出错: {e}")
    print("全部处理完成！")

if __name__ == '__main__':
    main()
