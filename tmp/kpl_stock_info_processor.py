import logging
from typing import Dict, Any, List
import requests
import json
import re
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class KplStockInfoProcessor:
    """开盘啦股票信息处理类，基于报告中的股票代码获取实时信息"""
    
    def __init__(self):
        """初始化配置"""
        self.xq_a_token = "b9e815146f0656f1029972614b1333adbabcccea"
        self.headers = {
            "cookie": f"xq_a_token={self.xq_a_token};",
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 "
            "(KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
        }
        self.session = requests.Session()
        print("KplStockInfoProcessor 初始化完成")

    def _convert_timestamp(self, timestamp_ms: int) -> str:
        """时间戳转换为字符串时间"""
        timestamp_s = timestamp_ms / 1000
        datetime_obj = datetime.fromtimestamp(timestamp_s)
        return datetime_obj.strftime("%Y-%m-%d %H:%M:%S")

    def _get_stock_symbol(self, stock_code: str) -> str:
        """将股票代码转换为雪球格式的symbol"""
        # 如果是6位数字，需要添加交易所前缀
        if len(stock_code) == 6:
            # 根据股票代码判断交易所
            if stock_code.startswith(('600', '601', '603', '688', '900')):
                return f"SH{stock_code}"
            elif stock_code.startswith(('000', '002', '003', '300')):
                return f"SZ{stock_code}"
            else:
                # 默认上海
                return f"SH{stock_code}"
        return stock_code

    def get_stock_info(self, stock_code: str) -> Dict[str, Any]:
        """获取单个股票实时信息，返回kv字典格式"""
        symbol = self._get_stock_symbol(stock_code)
        self.session.get(url="https://xueqiu.com", headers=self.headers)
        url = f"https://stock.xueqiu.com/v5/stock/quote.json?symbol={symbol}&extend=detail"
        try:
            response = self.session.get(url, headers=self.headers, timeout=5)
            if response.status_code != 200:
                print(f"获取股票 {stock_code} 信息失败: HTTP {response.status_code}")
                return None
            json_data = response.json()
            if 'data' not in json_data or 'quote' not in json_data['data']:
                print(f"股票 {stock_code} 返回数据格式异常")
                return None
            quote_data = json_data['data']['quote']
            column_name_map = {
                "acc_unit_nav": "累计净值",
                "amount": "成交额",
                "amplitude": "振幅",
                "avg_price": "均价",
                "chg": "涨跌",
                "currency": "货币",
                "current": "现价",
                "current_year_percent": "今年以来涨幅",
                "dividend": "股息(TTM)",
                "dividend_yield": "股息率(TTM)",
                "eps": "每股收益",
                "exchange": "交易所",
                "float_market_capital": "流通值",
                "float_shares": "流通股",
                "found_date": "成立日期",
                "goodwill_in_net_assets": "净资产中的商誉",
                "high": "最高",
                "high52w": "52周最高",
                "iopv": "参考净值",
                "issue_date": "发行日期",
                "last_close": "昨收",
                "limit_down": "跌停",
                "limit_up": "涨停",
                "lot_size": "最小交易单位",
                "low": "最低",
                "low52w": "52周最低",
                "market_capital": "资产净值/总市值",
                "name": "名称",
                "nav_date": "净值日期",
                "navps": "每股净资产",
                "open": "今开",
                "pb": "市净率",
                "pe_forecast": "市盈率(动)",
                "pe_lyr": "市盈率(静)",
                "pe_ttm": "市盈率(TTM)",
                "percent": "涨幅",
                "premium_rate": "溢价率",
                "psr": "市销率",
                "symbol": "代码",
                "total_shares": "基金份额/总股本",
                "turnover_rate": "周转率",
                "unit_nav": "单位净值",
                "volume": "成交量",
                "time": "时间",
            }
            # 字段映射+只保留含中文字段
            zh_dict = {}
            for k, v in quote_data.items():
                zh_name = column_name_map.get(k, None)
                if zh_name and any('\u4e00' <= c <= '\u9fff' for c in zh_name):
                    zh_dict[zh_name] = v
            # 时间字段转换
            for tkey in ["时间", "发行日期"]:
                if tkey in zh_dict and zh_dict[tkey]:
                    try:
                        zh_dict[tkey] = self._convert_timestamp(int(zh_dict[tkey]))
                    except Exception:
                        pass
            # 附加原始代码和雪球代码
            zh_dict["原始代码"] = stock_code
            zh_dict["雪球代码"] = symbol
            return zh_dict
        except Exception as e:
            print(f"获取股票 {stock_code} 信息异常: {str(e)}")
            return None

    def extract_stock_codes_from_reports(self, reports_data: List[Dict[str, Any]]) -> List[str]:
        """从报告数据中提取所有股票代码并去重"""
        all_codes = []
        
        for report in reports_data:
            if 'code_list' in report and isinstance(report['code_list'], list):
                all_codes.extend(report['code_list'])
        
        # 去重并排序
        unique_codes = sorted(list(set(all_codes)))
        print(f"从报告中提取到 {len(all_codes)} 个股票代码，去重后 {len(unique_codes)} 个")
        
        return unique_codes

    def get_stocks_info_batch(self, stock_codes: List[str]) -> Dict[str, Any]:
        """批量获取股票信息"""
        print(f"开始批量获取股票信息 (共 {len(stock_codes)} 个股票)")
        
        results = {
            'success': [],
            'errors': [],
            'meta': {
                'total_codes': len(stock_codes),
                'process_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        success_count = 0
        error_count = 0
        batch_size = 10  # 每10个打印一次进度
        
        for i, stock_code in enumerate(stock_codes):
            # 每10个请求打印一次进度
            if i % batch_size == 0:
                print(f"进度: {i}/{len(stock_codes)} (代码: {stock_code})")
            
            stock_info = self.get_stock_info(stock_code)
            
            if stock_info:
                results['success'].append(stock_info)
                success_count += 1
            else:
                results['errors'].append({
                    'code': stock_code,
                    'error': '获取失败'
                })
                error_count += 1
            
            # 添加短暂延迟
            import time
            time.sleep(0.1)
        
        # 添加统计信息
        results['stats'] = {
            'success_count': success_count,
            'error_count': error_count,
            'total_processed': len(stock_codes)
        }
        
        print(f"批量获取股票信息完成: 成功 {success_count}，失败 {error_count}")
        
        return results

    def process_reports_and_get_stocks(self, reports_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理报告数据并获取相关股票信息"""
        print("开始处理报告数据并提取股票代码...")
        
        # 从报告中提取股票代码
        stock_codes = self.extract_stock_codes_from_reports(reports_data)
        
        if not stock_codes:
            print("未从报告中提取到股票代码")
            return {
                'stock_codes': [],
                'stock_info': {
                    'success': [],
                    'errors': [],
                    'stats': {'success_count': 0, 'error_count': 0, 'total_processed': 0}
                }
            }
        
        # 获取股票信息
        stock_info = self.get_stocks_info_batch(stock_codes)
        
        return {
            'stock_codes': stock_codes,
            'stock_info': stock_info
        }

def fun():
    reports_result = json.loads(datax_str)
    datax = [item for item in reports_result['success'] if len(item['Conts']) > 1]
    if not datax:
        return {"datax": [], "股票实时信息": []}

    # 2. 提取所有股票代码去重
    all_codes = set()
    for item in datax:
        if 'code_list' in item and isinstance(item['code_list'], list):
            all_codes.update(item['code_list'])
    all_codes = sorted(list(all_codes))
    if not all_codes:
        return {"datax": datax, "股票实时信息": []}

    # 3. 获取所有股票实时信息，组装成列表
    stock_api = KplStockInfoProcessor()
    stock_info_list = stock_api.get_stocks_info_batch(all_codes)['success']

    # 4. 返回结构
    return {"datax": datax, "股票实时信息": stock_info_list}

# 如果直接运行此脚本
if __name__ == "__main__":
    import argparse



    # 解析命令行参数
    parser = argparse.ArgumentParser(description='开盘啦股票信息处理工具')
    parser.add_argument('--reports-file', type=str, help='报告数据JSON文件路径')
    parser.add_argument('--stock-codes',default="SH600000", type=str, help='股票代码列表，用逗号分隔')
    parser.add_argument('--output', type=str, help='输出结果到JSON文件（可选）')
    args = parser.parse_args()
    
    # 初始化处理器
    processor = KplStockInfoProcessor()
    
    if args.reports_file:
        # 从报告文件读取数据
        try:
            with open(args.reports_file, 'r', encoding='utf-8') as f:
                reports_data = json.load(f)
            
            # 如果文件包含完整的结果结构，提取success部分
            if isinstance(reports_data, dict) and 'success' in reports_data:
                reports_data = reports_data['success']
            
            print(f"从文件加载了 {len(reports_data)} 个报告")
            
            # 处理报告并获取股票信息
            results = processor.process_reports_and_get_stocks(reports_data)
            
        except Exception as e:
            print(f"读取报告文件失败: {str(e)}")
            exit(1)
            
    elif args.stock_codes:
        # 直接处理股票代码列表
        stock_codes = [code.strip() for code in args.stock_codes.split(',')]
        print(f"处理股票代码列表: {stock_codes}")
        
        stock_info = processor.get_stocks_info_batch(stock_codes)
        results = {
            'stock_codes': stock_codes,
            'stock_info': stock_info
        }
        
    else:
        print("请提供 --reports-file 或 --stock-codes 参数")
        exit(1)
    
    # 打印统计信息
    print(f"\n股票信息获取完成:")
    print(f"- 股票代码数量: {len(results['stock_codes'])}")
    print(f"- 成功获取: {results['stock_info']['stats']['success_count']}")
    print(f"- 获取失败: {results['stock_info']['stats']['error_count']}")
    
    # 如果指定了输出文件，保存结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"- 结果已保存到: {args.output}")
    else:
        # 打印前3个股票信息
        if results['stock_info']['success']:
            print("\n前3个股票信息示例:")
            for i, stock in enumerate(results['stock_info']['success'][:3]):
                print(f"\n[{i+1}] {stock.get('名称', 'N/A')} ({stock.get('代码', 'N/A')})")
                print(f"    现价: {stock.get('现价', 'N/A')}")
                print(f"    涨跌: {stock.get('涨跌', 'N/A')} ({stock.get('涨幅', 'N/A')})")
                print(f"    成交量: {stock.get('成交量', 'N/A')}")
                print(f"    成交额: {stock.get('成交额', 'N/A')}") 