from typing import Dict, Any, List
import requests
import json
import re
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class KplReportProcessor:
    """开盘啦研报处理类，专注于获取和清洗研报数据"""
    
    def __init__(self):
        """初始化API基础配置"""
        self.base_url = "https://apparticle.longhuvip.com/w1/api/index.php"
        self.headers = {
            "PhoneOSNew": "1",
            "DeviceID": "625d4c1d-cda0-3e23-9bf9-27cbce5c49db",
            "VerSion": "********",
            "Token": "9d8a4a57fbb0421c5f7837ceb1f6d811",
            "UserID": "2511251",
            "apiv": "w38"
        }
        logger.debug("KplReportProcessor 初始化完成")

    def get_report_detail(self, report_id: str) -> Dict[str, Any]:
        """获取单个研报详情"""
        params = {
            "a": "GetInfo",
            "c": "ForumsTuyere",
            "TuyID": report_id,
            **self.headers
        }
        
        # try:
        response = requests.get(self.base_url, params=params)
        response.raise_for_status()
        data = response.json()

        # 检查返回数据中是否存在Msg字段
        if 'Msg' not in data:
            logger.error(f"研报ID {report_id} 返回数据异常，缺少Msg字段")
            return None
            # raise ValueError(f"研报ID {report_id} 返回数据异常，缺少Msg字段")

        # 添加文章URL
        data['Msg']['art_url'] = f'https://apppage.longhuvip.com/w41/share/summary.html?id={report_id}'

        return data
            
        # except requests.RequestException as e:
        #     logger.error(f"请求研报详情失败: {str(e)}")
        #     raise
        # except json.JSONDecodeError as e:
        #     logger.error(f"解析研报详情JSON失败: {str(e)}")
        #     raise
        # except Exception as e:
        #     logger.error(f"获取研报详情时发生未知错误: {str(e)}")
        #     raise
    
    def _extract_stock_codes(self, content: str) -> List[str]:
        """从研报内容中提取股票代码"""
        # 使用正则表达式提取 data-id 属性值，这是股票代码
        pattern = r'data-id="(\d+)"'
        matches = re.findall(pattern, content)
        return matches
    
    def _format_datetime(self, timestamp) -> str:
        """将时间戳转换为标准日期格式"""
        if isinstance(timestamp, (int, float)):
            # 如果是时间戳
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(timestamp, str):
            try:
                # 如果是字符串格式，尝试解析
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                return timestamp
        else:
            return str(timestamp)
    
    def clean_report_data(self, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗研报数据，只保留需要的字段"""
        if not report_data or 'Msg' not in report_data:
            return None
        
        msg = report_data['Msg']
        
        # 提取需要的字段
        cleaned_data = {
            'Id': msg.get('ID'),
            'Title': msg.get('Title'),
            'CreateTime': self._format_datetime(msg.get('CreateTime')),
            'Label': msg.get('Label'),
            'Conts': msg.get('Conts', '')
        }
        
        # 从内容中提取股票代码
        cleaned_data['code_list'] = self._extract_stock_codes(cleaned_data['Conts'])
        
        return cleaned_data
    
    def get_report_details_clean(self, start_id: int, end_id: int) -> Dict[str, Any]:
        """批量获取并清洗研报详情数据"""
        logger.info(f"开始批量获取研报详情 (ID范围: {start_id}-{end_id})")
        
        results = {
            'success': [],
            'errors': [],
            'meta': {
                'start_id': start_id,
                'end_id': end_id,
                'process_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        success_count = 0
        error_count = 0
        consecutive_failures = 0
        max_consecutive_failures = 30  # 连续失败30次后停止
        
        for report_id in range(start_id, end_id + 1):
            try:
                logger.info(f"正在获取研报ID: {report_id}")
                report_data = self.get_report_detail(str(report_id))
                
                # 清洗数据
                cleaned_data = self.clean_report_data(report_data)
                
                if cleaned_data:
                    results['success'].append(cleaned_data)
                    success_count += 1
                    consecutive_failures = 0  # 重置连续失败计数
                    logger.info(f"成功获取并清洗研报ID: {report_id}")
                else:
                    logger.warning(f"研报ID {report_id} 返回数据异常")
                    results['errors'].append({
                        'id': report_id,
                        'error': '返回数据异常或清洗失败'
                    })
                    error_count += 1
                    consecutive_failures += 1
                
            except Exception as e:
                logger.error(f"获取研报ID {report_id} 失败: {str(e)}")
                results['errors'].append({
                    'id': report_id,
                    'error': str(e)
                })
                error_count += 1
                consecutive_failures += 1
            
            # 检查连续失败次数
            if consecutive_failures >= max_consecutive_failures:
                logger.warning(f"连续失败 {consecutive_failures} 次，可能已到达文章尽头，停止遍历")
                break
            
            # 添加短暂延迟以避免请求过于频繁
            import time
            time.sleep(0.5)
        
        # 添加统计信息
        results['stats'] = {
            'success_count': success_count,
            'error_count': error_count,
            'total_processed': (report_id - start_id + 1)
        }
        
        logger.info(f"批量获取研报详情完成: 成功 {success_count}，失败 {error_count}")
        
        return results

# 如果直接运行此脚本
if __name__ == "__main__":
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='开盘啦研报数据处理工具')
    parser.add_argument('--start', type=int, default=212800, help='起始研报ID')
    parser.add_argument('--end', type=int, default=212810, help='结束研报ID')
    parser.add_argument('--output', type=str, help='输出结果到JSON文件（可选）')
    args = parser.parse_args()
    
    # 初始化处理器并获取清洗后的数据
    processor = KplReportProcessor()
    results = processor.get_report_details_clean(args.start, args.end)
    data_json  = results['success']
    data_json= [item for item  in data_json if len(item['Conts'])>1]
    data_json_str = json.dumps(data_json, ensure_ascii=False,)
    
    # 打印统计信息
    print(f"\n研报数据获取完成:")
    print(f"- 成功获取: {results['stats']['success_count']}")
    print(f"- 获取失败: {results['stats']['error_count']}")
    print(f"- 总计处理: {results['stats']['total_processed']}")
    
    # 如果指定了输出文件，保存结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"- 结果已保存到: {args.output}")
    else:
        # 打印前5个结果
        if results['success']:
            print("\n前5个研报数据示例:")
            for i, report in enumerate(results['success'][:5]):
                print(f"\n[{i+1}] ID: {report['Id']} - {report['Title']}")
                print(f"    发布时间: {report['CreateTime']}")
                print(f"    标签: {report['Label']}")
                print(f"    内容: {report['Conts']}")
                print(f"    提及股票: {', '.join(report['code_list']) if report['code_list'] else '无'}")
