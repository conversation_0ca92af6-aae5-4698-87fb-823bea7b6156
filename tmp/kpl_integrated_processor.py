from typing import Dict, Any, List
import json
from datetime import datetime

# 导入其他模块
from kpl_report_processor_simple import KplReportProcessorSimple
from kpl_stock_info_processor import KplStockInfoProcessor

def get_reports_with_realtime_stocks(start_id: int, end_id: int) -> Dict[str, Any]:
    # 1. 获取报告数据
    report_api = KplReportProcessorSimple()
    reports_result = report_api.get_report_details_clean(start_id, end_id)
    datax_str =''
    reports_result =json.loads(datax_str)
    datax = [item for item in reports_result['success'] if len(item['Conts']) > 1]
    if not datax:
        return {"datax": [], "股票实时信息": []}

    # 2. 提取所有股票代码去重
    all_codes = set()
    for item in datax:
        if 'code_list' in item and isinstance(item['code_list'], list):
            all_codes.update(item['code_list'])
    all_codes = sorted(list(all_codes))
    if not all_codes:
        return {"datax": datax, "股票实时信息": []}

    # 3. 获取所有股票实时信息，组装成列表
    stock_api = KplStockInfoProcessor()
    stock_info_list = stock_api.get_stocks_info_batch(all_codes)['success']

    # 4. 返回结构
    return {"datax": datax, "股票实时信息": stock_info_list}

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='报告+股票实时信息一体化处理')
    parser.add_argument('--start', type=int, default=212800, help='起始报告ID')
    parser.add_argument('--end', type=int, default=212810, help='结束报告ID')
    parser.add_argument('--output', type=str, help='输出结果到JSON文件（可选）')
    args = parser.parse_args()

    results = get_reports_with_realtime_stocks(args.start, args.end)
    print(f"共获取{len(results['datax'])}条报告数据, {len(results['股票实时信息'])}条股票实时信息")
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"已保存到: {args.output}")
    else:
        # 打印前2条示例
        for i, item in enumerate(results['datax'][:2]):
            print(f"\n[{i+1}] {item['Title']}")
        for i, stock in enumerate(results['股票实时信息'][:2]):
            print(f"\n股票示例[{i+1}]: {stock.get('雪球代码', 'N/A')} {stock.get('data', [])}") 