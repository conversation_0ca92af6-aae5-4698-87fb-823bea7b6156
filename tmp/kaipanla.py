from typing import Dict, Any
import requests
import json
import os
from datetime import datetime

import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class KaiPanLaAPI:
    """开盘啦 API 封装类"""
    
    def __init__(self):
        # API基础配置
        self.base_url = "https://applhb.longhuvip.com/w1/api/index.php"
        self.headers = {
            "PhoneOSNew": "1",
            "DeviceID": "625d4c1d-cda0-3e23-9bf9-27cbce5c49db",
            "VerSion": "********",
            "Token": "9d8a4a57fbb0421c5f7837ceb1f6d811",
            "UserID": "2511251",
            "apiv": "w38"
        }
        # 确保data目录存在
        self.theme_data_dir = os.path.join('data', 'kpl_theme')
        if not os.path.exists(self.theme_data_dir):
            os.makedirs(self.theme_data_dir)
        logger.debug("KaiPanLaAPI 初始化完成")

    def _save_to_file(self, data: Dict[str, Any], filename: str, theme_data_dir=None) -> None:
        """保存数据到本地文件"""
        if theme_data_dir is None:
            filepath = os.path.join(self.theme_data_dir, filename)
        else:
            filepath = os.path.join(theme_data_dir, filename)
        # filepath = os.path.join(self.theme_data_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.debug(f"数据已保存到文件: {filepath}")

    def _load_from_file(self, filename: str, theme_data_dir=None) -> Dict[str, Any]:
        """从本地文件加载数据"""
        if theme_data_dir is None:
            filepath = os.path.join(self.theme_data_dir, filename)
        else:
            filepath = os.path.join(theme_data_dir, filename)
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None

    def get_user_themes(self) -> Dict[str, Any]:
        """获取用户自选题材列表"""
        params = {
            "a": "USTList",
            "c": "ThemeSubscribe",
            **self.headers
        }
        logger.debug(f"请求自选题材列表: {self.base_url} - params: {params}")
        response = requests.get(self.base_url, params=params)
        data = response.json()
        logger.debug(f"自选题材列表响应: {data}")
        return data

    def add_user_theme(self, tid: str) -> Dict[str, Any]:
        """添加自选题材"""
        params = {
            "a": "USAddTheme",
            "c": "ThemeSubscribe",
            "TID": tid,
            **self.headers
        }
        logger.debug(f"请求添加自选题材: {self.base_url} - params: {params}")
        response = requests.get(self.base_url, params=params)
        data = response.json()
        logger.debug(f"添加自选题材响应: {data}")
        return data

    def remove_user_theme(self, tid: str) -> Dict[str, Any]:
        """删除自选题材"""
        params = {
            "a": "USRemoveTheme",
            "c": "ThemeSubscribe",
            "TID": tid,
            **self.headers
        }
        logger.debug(f"请求删除自选题材: {self.base_url} - params: {params}")
        response = requests.get(self.base_url, params=params)
        data = response.json()
        logger.debug(f"删除自选题材响应: {data}")
        return data

    async def get_theme_detail(self, theme_id: str) -> Dict[str, Any]:
        """获取题材详情"""
        # 本地没有，从API获取
        params = {
            "a": "InfoGet",
            "c": "Theme",
            "ID": theme_id,
            **self.headers
        }
        logger.debug(f"请求题材详情: {self.base_url} - params: {params}")
        response = requests.get(self.base_url, params=params)
        data = response.json()
        logger.debug(f"题材详情响应: {data}")
        return data

    def get_theme_comments(self, theme_id: str, index: int = 0) -> Dict[str, Any]:
        """获取题材评论"""
        params = {
            "a": "Get",
            "c": "Comments",
            "st": "20",
            "Tsort": "9",
            "Index": index,
            "Type": "4",
            "StockID": theme_id,
            **self.headers
        }
        logger.debug(f"请求题材评论: {self.base_url} - params: {params}")
        response = requests.get(self.base_url, params=params)
        data = response.json()
        logger.debug(f"题材评论响应: {data}")
        return data

    def get_stock_reports(self, stock_code: str) -> Dict[str, Any]:
        """获取股票研报列表"""
        # 先从本地文件获取
        filename = f'stock_reports_{stock_code}.json'
        data = self._load_from_file(filename)
        if data:
            return {"List": data}

        # 本地没有，从API获取
        params = {
            "a": "GetByStock",
            "c": "ForumsTuyere",
            "Code": stock_code,
            **self.headers
        }
        logger.debug(f"请求股票研报列表: {self.base_url} - params: {params}")
        response = requests.get(self.base_url, params=params)
        data = response.json()
        logger.debug(f"股票研报列表响应: {data}")

        # 保存到本地文件
        if data.get("List"):
            self._save_to_file(data["List"], filename)
        return data

    def get_report_detail(self, report_id: str) -> Dict[str, Any]:
        """获取研报详情"""
        # 确保report目录存在
        report_data_dir = os.path.join('data', 'kpl_report')
        if not os.path.exists(report_data_dir):
            os.makedirs(report_data_dir)
        
        # 先从本地文件获取
        filename = f'report_{report_id}.json'
        
        # 检测文件是否存在
        if os.path.exists(os.path.join(report_data_dir, filename)):
            data = self._load_from_file(filename, report_data_dir)
            if data:
                logger.debug(f"从本地文件加载研报详情: {os.path.join(report_data_dir, filename)}")
                return data

        # 本地没有，从API获取
        params = {
            "a": "GetInfo",
            "c": "ForumsTuyere",
            "TuyID": report_id,
            **self.headers
        }
        base_url = "https://apparticle.longhuvip.com/w1/api/index.php"
        logger.debug(f"请求研报详情: {base_url} - params: {params}")
        
        try:
            response = requests.get(base_url, params=params)
            response.raise_for_status()  # 检查HTTP错误
            data = response.json()
            logger.debug(f"研报详情响应: {data}")

            # 检查返回数据中是否存在Msg字段
            if 'Msg' not in data:
                logger.error(f"研报ID {report_id} 返回数据异常，缺少Msg字段")
                raise ValueError(f"研报ID {report_id} 返回数据异常，缺少Msg字段")

            # 处理CreateTime时间转换
            msg_data = data.get('Msg', {})
            create_time = msg_data.get('CreateTime')
            data['Msg']['art_url']=f'https://apppage.longhuvip.com/w41/share/summary.html?id={report_id}'
            
            if create_time:
                try:
                    # 假设CreateTime是时间戳格式，转换为可读时间
                    if isinstance(create_time, (int, float)):
                        # 如果是时间戳
                        dt = datetime.fromtimestamp(create_time)
                    else:
                        # 如果是字符串格式，尝试解析
                        dt = datetime.fromisoformat(create_time.replace('Z', '+00:00'))
                    
                    # 格式化为 YYYY-MM-DD_HH-MM 格式
                    time_str = dt.strftime('%Y-%m-%d_%H-%M')
                    filename = f'report_{report_id}_{time_str}.json'
                except Exception as e:
                    logger.warning(f"时间转换失败，使用原始文件名: {str(e)}")
                    filename = f'report_{report_id}.json'
            else:
                logger.warning(f"研报ID {report_id} 缺少CreateTime字段")
                filename = f'report_{report_id}.json'

            # 保存到本地文件
            self._save_to_file(data, filename, report_data_dir)
            logger.info(f"研报详情已保存到本地: {os.path.join(report_data_dir, filename)}")
            
            return data
            
        except requests.RequestException as e:
            logger.error(f"请求研报详情失败: {str(e)}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"解析研报详情JSON失败: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"获取研报详情时发生未知错误: {str(e)}")
            raise

    def get_theme_intro(self, theme_id: str) -> Dict[str, Any]:
        """获取题材详情介绍"""
        params = {
            "a": "InfoIntr",
            "c": "Theme",
            "ID": theme_id,
            **self.headers
        }
        logger.debug(f"请求题材详情介绍: {self.base_url} - params: {params}")
        response = requests.get(self.base_url, params=params)
        data = response.json()
        logger.debug(f"题材详情介绍响应: {data}")
        return data

    async def download_all_theme_details(self, start_id: int = 1, end_id: int = 300):
        """下载指定范围内的所有题材详情"""
        logger.info(f"开始下载题材详情 (ID范围: {start_id}-{end_id})")
        success_count = 0
        empty_name_count = 0
        error_count = 0
        skip_count = 0
        
        # 获取已存在的文件列表
        existing_files = set(os.listdir(self.theme_data_dir))
        
        for theme_id in range(start_id, end_id + 1):
            try:
                # 检查是否已经下载过（通过匹配文件名前缀）
                theme_prefix = f'theme_{theme_id}_'
                existing_file = next((f for f in existing_files if f.startswith(theme_prefix)), None)
                
                if existing_file:
                    logger.info(f"题材ID {theme_id} 已存在文件: {existing_file}，跳过下载")
                    skip_count += 1
                    continue
                
                logger.info(f"正在下载题材ID: {theme_id}")
                data = await self.get_theme_detail(str(theme_id))
                
                # 检查返回数据中的Name字段
                theme_name = data.get('Name', '').strip()
                if not theme_name:
                    logger.info(f"题材ID {theme_id} 的Name为空，跳过保存")
                    empty_name_count += 1
                    continue
                
                # 构建文件名：theme_ID_Name.json
                # 替换文件名中的非法字符
                safe_name = "".join(c for c in theme_name if c.isalnum() or c in ('_', '-'))
                filename = f'theme_{theme_id}_{safe_name}.json'
                filepath = os.path.join(self.theme_data_dir, filename)
                
                # 保存到文件
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                logger.info(f"成功保存题材: {theme_id} - {theme_name}")
                success_count += 1
                
                # 添加短暂延迟以避免请求过于频繁
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"下载题材ID {theme_id} 失败: {str(e)}")
                error_count += 1
        
        logger.info(f"题材详情下载完成:")
        logger.info(f"- 成功保存: {success_count}")
        logger.info(f"- 已存在跳过: {skip_count}")
        logger.info(f"- Name为空: {empty_name_count}")
        logger.info(f"- 下载失败: {error_count}")
        logger.info(f"- 总计处理: {end_id - start_id + 1}")

    def get_report_details(self, start_id: int, end_id: int) -> Dict[str, Any]:
        """批量获取研报详情"""
        logger.info(f"开始批量获取研报详情 (ID范围: {start_id}-{end_id})")
        success_count = 0
        error_count = 0
        skip_count = 0
        consecutive_failures = 0
        max_consecutive_failures = 30
        
        results = {
            'success': [],
            'errors': [],
            'skipped': []
        }
        
        for report_id in range(start_id, end_id + 1):
            try:
                logger.info(f"正在获取研报ID: {report_id}")
                data = self.get_report_detail(str(report_id))
                
                # 检查是否成功获取到数据
                if data and 'Msg' in data:
                    results['success'].append({
                        'id': report_id,
                        'data': data
                    })
                    success_count += 1
                    consecutive_failures = 0  # 重置连续失败计数
                    logger.info(f"成功获取研报ID: {report_id}")
                else:
                    logger.warning(f"研报ID {report_id} 返回数据异常")
                    results['errors'].append({
                        'id': report_id,
                        'error': '返回数据异常'
                    })
                    error_count += 1
                    consecutive_failures += 1
                
            except Exception as e:
                logger.error(f"获取研报ID {report_id} 失败: {str(e)}")
                results['errors'].append({
                    'id': report_id,
                    'error': str(e)
                })
                error_count += 1
                consecutive_failures += 1
            
            # 检查连续失败次数
            if consecutive_failures >= max_consecutive_failures:
                logger.warning(f"连续失败 {consecutive_failures} 次，可能已到达文章尽头，停止遍历")
                break
            
            # 添加短暂延迟以避免请求过于频繁
            import time
            time.sleep(0.5)
        
        logger.info(f"批量获取研报详情完成:")
        logger.info(f"- 成功获取: {success_count}")
        logger.info(f"- 获取失败: {error_count}")
        logger.info(f"- 连续失败次数: {consecutive_failures}")
        logger.info(f"- 总计处理: {end_id - start_id + 1}")
        
        return results

    def update_latest_reports(self, batch_size: int = 500) -> Dict[str, Any]:
        """自动更新最新文章"""
        report_data_dir = os.path.join('data', 'kpl_report')
        
        # 确保目录存在
        if not os.path.exists(report_data_dir):
            os.makedirs(report_data_dir)
            logger.info(f"创建研报目录: {report_data_dir}")
            # 如果目录不存在，从ID 1开始
            start_id = 1
        else:
            # 获取目录中最大的文章ID
            try:
                files = os.listdir(report_data_dir)
                report_ids = []
                
                for file in files:
                    if file.startswith('report_') and file.endswith('.json'):
                        # 提取ID，支持带时间戳的文件名
                        parts = file.replace('.json', '').split('_')
                        if len(parts) >= 2:
                            try:
                                report_id = int(parts[1])
                                report_ids.append(report_id)
                            except ValueError:
                                continue
                
                if report_ids:
                    max_id = max(report_ids)
                    start_id = max_id + 1
                    logger.info(f"发现最大文章ID: {max_id}，从 {start_id} 开始更新")
                else:
                    start_id = 1
                    logger.info("未发现现有文章，从ID 1开始")
                    
            except Exception as e:
                logger.error(f"获取最大文章ID失败: {str(e)}")
                start_id = 1
        
        end_id = start_id + batch_size - 1
        logger.info(f"开始自动更新最新文章 (ID范围: {start_id}-{end_id})")
        
        # 调用批量获取方法
        results = self.get_report_details(start_id, end_id)
        
        # 添加更新统计信息
        results['update_info'] = {
            'start_id': start_id,
            'end_id': end_id,
            'batch_size': batch_size,
            'update_time': datetime.now().isoformat()
        }
        
        return results

if __name__ == "__main__":
    import asyncio

    api = KaiPanLaAPI()
    
    # 测试单个研报获取
    # api.get_report_detail('2127640')
    
    # 测试批量获取研报
    # results = api.get_report_details(2127640, 2127650)
    # print(f"批量获取结果: {len(results['success'])} 成功, {len(results['errors'])} 失败")
    
    # 测试自动更新最新文章
    results = api.update_latest_reports(batch_size=500)
    print(f"自动更新结果: {len(results['success'])} 成功, {len(results['errors'])} 失败")
    if 'update_info' in results:
        print(f"更新信息: {results['update_info']}")