import logging

class LoggerManager:
    """日志管理类
    
    用于统一管理和配置日志功能
    """
    
    @staticmethod
    def setup_logger(name: str, log_file: str = None) -> logging.Logger:
        """设置并获取logger实例
        
        Args:
            name: logger名称
            log_file: 日志文件路径，默认为None
            
        Returns:
            配置好的logger实例
        """
        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 创建logger实例
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        
        # 如果指定了日志文件，添加文件处理器
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        return logger