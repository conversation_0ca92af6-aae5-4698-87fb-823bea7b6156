from minio import Minio
from minio.error import S3Error


# Minio文件上传工具类
class MinioFileUploader:
    def __init__(self, endpoint, access_key, secret_key, secure=False):
        self.minio_client = Minio(
            endpoint,
            access_key=access_key,
            secret_key=secret_key,
            secure=secure
        )

    def upload_file(self, bucket_name, object_name, file_path):
        try:
            # 确保桶存在
            if not self.minio_client.bucket_exists(bucket_name):
                self.minio_client.make_bucket(bucket_name)

            # 上传文件
            self.minio_client.fput_object(bucket_name, object_name, file_path)
            print(f"Successfully uploaded {object_name}")

            return object_name
        except S3Error as e:
            print(f"Error occurred: {e}")


# 示例使用
if __name__ == "__main__":
    uploader = MinioFileUploader(
        endpoint="192.168.34.46:9000",
        access_key="minioadmin",
        secret_key="minioadmin",
        secure=False
    )

    bucket_name = "wenlv"
    object_name = "museum/collection_img"  # 例如 "images/example.jpg"
    file_path = "F:/MongoDB/museum/商青铜虎形饰.jpg"

    share_url = uploader.upload_file(bucket_name, object_name, file_path)