import functools
from DrissionPage import ChromiumPage
from DrissionPage import WebPage
from DrissionPage._configs.chromium_options import ChromiumOptions
from DrissionPage._elements.chromium_element import ChromiumElement
from DrissionPage._pages.chromium_tab import ChromiumTab
import json
import time
import re
import os
import uuid
from typing import List, Dict, Optional, Union, Any
from mysql_tools import MySQLDBTool
from minio_tools import MinioFileUploader
from img_download import ImageDownloader
from logger import LoggerManager

# 配置日志
logger = LoggerManager.setup_logger('XiechengResCrawler', 'xiecheng_res_crawler.log')


def log_execution(func):
    """函数执行日志装饰器"""

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger.info(f'开始执行: {func.__name__}')
        try:
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            logger.error(f'执行 {func.__name__} 时发生错误: {str(e)}')
            raise

    return wrapper


class XiechengServerResCrawler:
    """携程餐厅爬虫类

    该类用于爬取携程旅游网站的餐厅信息，包括餐厅基本信息、价格、菜系等数据。

    Attributes:
        browser (ChromiumPage): 浏览器实例
        base_url (str): 携程旅游网站基础URL
        page (WebPage): 网页实例
        WAIT_TIME (dict): 等待时间配置
        MINIO_CONFIG (dict): MinIO配置信息
        is_server_mode (bool): 是否为服务器模式
    """

    def __init__(self, base_url: str, chrome_path: Optional[str] = None, is_server_mode: bool = True, 
                 mysql_config: Optional[Dict[str, Union[str, int]]] = None,
                 minio_config: Optional[Dict[str, Any]] = None):
        """初始化爬虫实例

        Args:
            base_url: 携程旅游网站基础URL
            chrome_path: Chrome可执行文件路径，服务器环境需指定
            is_server_mode: 是否为服务器模式，True为服务器模式，False为本地模式
            mysql_config: MySQL数据库配置
            minio_config: MinIO配置
        """
        self.chrome_path = chrome_path
        self.base_url = base_url
        self.is_server_mode = is_server_mode

        # 等待时间配置
        self.WAIT_TIME = {
            'SHORT': 0.05,
            'MEDIUM': 2,
            'LONG': 5,
            'EXTRA_LONG': 10
        }

        # MinIO配置
        self.MINIO_CONFIG = minio_config or {
            'endpoint': '*************:9000',
            'access_key': 'minioadmin',
            'secret_key': 'minioadmin_scyd@lab1234',
            'secure': False
        }
        
        # MySQL配置
        self.MYSQL_CONFIG = mysql_config or {
            'username': 'root',
            'password': '123456',
            'database': 'wenlv'
        }

        # 调整为相对路径，方便服务器部署
        self.save_path = os.path.join(os.path.dirname(__file__), "data")
        os.makedirs(self.save_path, exist_ok=True)
        self.downloader = None

        # 初始化图片下载器和MinIO上传器
        self.image_downloader = ImageDownloader('', self.save_path, '')
        self.minio_uploader = MinioFileUploader(**self.MINIO_CONFIG)

        # 初始化浏览器配置
        self._init_browser(chrome_path)

    @log_execution
    def _init_browser(self, chrome_path: Optional[str] = None) -> None:
        """初始化浏览器配置

        Args:
            chrome_path: Chrome可执行文件路径
        """
        # 创建浏览器配置对象
        co = ChromiumOptions()

        # 根据模式设置不同的浏览器配置
        if self.is_server_mode:
            # 服务器环境优化配置
            co.headless(True)  # 启用无头模式
            co.set_argument('--incognito')  # 隐身模式
            co.set_argument('--no-sandbox')  # 禁用沙盒模式
            co.set_argument('--disable-dev-shm-usage')
            co.set_argument('--disable-gpu')
        else:
            # 本地模式，可以看到浏览过程
            co.headless(False)  # 禁用无头模式
            co.set_argument('--incognito')  # 隐身模式，减少缓存影响
            # 添加调试选项，方便本地开发调试
            co.set_argument('--auto-open-devtools-for-tabs')  # 自动打开开发者工具
            
        # 通用设置
        co.set_argument('--disable-extensions')  # 禁用扩展
        co.set_argument('--disable-infobars')  # 禁用信息栏
        
        # 如果指定了Chrome路径，则设置
        if chrome_path:
            co.set_paths(browser_path=chrome_path)

        # 初始化浏览器实例
        self.browser = ChromiumPage(co)
        self.page = self.browser  # 使用同一个浏览器实例

    def click_page(self, initial_page: int, page: ChromiumTab) -> None:
        """点击翻页按钮

        Args:
            initial_page: 目标页码
            page: 当前页面实例
        """
        try:
            input_element = page.ele('#gopagetext')
            time.sleep(self.WAIT_TIME['MEDIUM'])
            input_element.input(str(initial_page))
            time.sleep(self.WAIT_TIME['MEDIUM'])
            button_element = page.ele('.gopage')
            button_element.click()
            time.sleep(self.WAIT_TIME['MEDIUM'])
        except Exception as e:
            logger.error(f'翻页失败: {str(e)}')

    @log_execution
    def download_and_upload_image(self, img_url: str, hotel_name: str, img_name: str) -> str:
        """下载并上传图片到MinIO"""
        try:
            local_dir = os.path.join(self.save_path, "Xiecheng_res", hotel_name)
            if not os.path.exists(local_dir):
                os.makedirs(local_dir)
            path = os.path.join(local_dir)
            save_path = os.path.join(local_dir, f"{img_name}.jpg")

            if os.path.exists(save_path):
                logger.info(f'文件已存在，跳过下载: {save_path}')
                image_info = self.downloader.get_image_info(save_path) if self.downloader else {}
                logger.info(f'图片信息: {image_info}')
            else:
                if self.downloader is None:
                    self.downloader = ImageDownloader(img_url, path, img_name)
                else:
                    self.downloader.img_url = img_url
                    self.downloader.save_path = path
                    self.downloader.img_name = img_name

                try:
                    self.page.download(img_url, path, img_name, 'jpg', show_msg=True)
                except Exception as e:
                    logger.warning(f'使用备选方法下载图片: {str(e)}')
                    self.downloader.download()

            # 上传到MinIO
            object_name = f"crawl_pic/Xiecheng_res/{hotel_name}/{img_name}.jpg"
            self.minio_uploader.upload_file('trip-data', object_name, save_path)
            return f'trip-data/{object_name}'

        except Exception as e:
            logger.error(f'图片处理失败: {str(e)}')
            return ''

    @log_execution
    def get_restaurant_details(self, element: str) -> Dict:
        """获取餐厅详细信息

        Args:
            element: 餐厅页面URL

        Returns:
            餐厅详细信息字典
        """
        try:
            self.browser.get(element)
            time.sleep(self.WAIT_TIME['MEDIUM'])

            name = self._get_element_text('.dest_toptitle detail_tt', chain_selectors=['.f_left', 'tag:h1'])
            div1 = self.browser.ele('.s_sight_infor').ele('.s_sight_in_list s_sight_noline cf').eles('tag:li')

            # 获取基本信息
            details = {
                'url': element,
                'name': name,
                'cuisine_type': self._get_div_element_text(div1, 1, chain_selectors=['.s_sight_con', 'tag:a']),
                'address': self._get_div_element_text(div1, 3, chain_selectors=['.s_sight_con']),
                'intro': self._get_element_text('.normalbox', chain_selectors=['.detailcon', '.gs_list_load']),
                'Delicacies': self._get_element_text('.normalbox',
                                                     chain_selectors=['.detailcon', '.text_style', 'tag:p']),
                'business_hours': self._get_div_element_text(div1, 4, chain_selectors=['.s_sight_con']),
                'avg_price': self._get_div_element_text(div1, 0, chain_selectors=['.s_sight_con', '.price']) + '元/人',
                'rating': self._get_element_text('.detailtop_r_info', chain_selectors=['.score', 'tag:b']) + '分',
                'phone': self._get_div_element_text(div1, 2, chain_selectors=['.s_sight_con']),
            }

            # 获取图片信息
            dic_img = self._get_all_images(name)
            details.update({'img_oss': dic_img['img_oss'], 'img_ori': dic_img['img_ori']})

            return details

        except Exception as e:
            logger.error(f'获取餐厅详情失败: {str(e)}')
            if 'disconnected' in str(e):
                if self.browser is not None:
                    self.browser.quit()
                self._init_browser(self.chrome_path)
            return {}

    def _save_to_database(self, mysql_tool: MySQLDBTool, data: Dict) -> None:
        """保存数据到数据库

        Args:
            mysql_tool: MySQL工具实例
            data: 待保存的数据
        """
        # 处理列表和字典类型的数据为字符串
        processed_data = {}
        for key, value in data.items():
            if isinstance(value, (list, dict)):
                processed_data[key] = json.dumps(value, ensure_ascii=False)
            else:
                processed_data[key] = value

        # 插入数据
        inserted_id = mysql_tool.insert_one('ods_trip_origin_restaurants_ctrip', processed_data)
        if inserted_id:
            logger.info(f"数据插入成功，ID: {inserted_id}")
        else:
            logger.error("数据插入失败")

    @log_execution
    def crawl(self, province_name: str = '湖北', city_name: str = '赤壁', city_url: str = '') -> None:
        """执行爬虫主程序

        爬取指定省份的所有城市餐厅信息，并存储到MySQL数据库中。

        Args:
            province_name: 省份名称，默认为'湖北'
            city_name: 城市名称，默认为'赤壁'
            city_url: 城市URL，如果提供则直接爬取该URL的餐厅信息
        """
        try:
            mysql_tool = MySQLDBTool(**self.MYSQL_CONFIG)

            self.browser.get(self.base_url)
            time.sleep(self.WAIT_TIME['MEDIUM'])

            logger.info(f'正在获取地址为{city_url} 的餐厅列表...')
            restaurant_urls = self._get_restaurant_urls(city_url)

            # 遍历餐厅URL
            logger.info(f'找到餐厅数量: {len(restaurant_urls)}')
            for idx, restaurant_url in enumerate(restaurant_urls):
                try:
                    logger.info(f'正在处理第 {idx+1}/{len(restaurant_urls)} 个餐厅: {restaurant_url}')
                    restaurant_data = self.get_restaurant_details(restaurant_url)
                    if not restaurant_data:
                        logger.warning(f'无法获取餐厅信息，跳过: {restaurant_url}')
                        continue
                        
                    restaurant_data.update({
                        'province_name': province_name,
                        'city_name': city_name
                    })

                    # 处理数据并存入数据库
                    self._save_to_database(mysql_tool, restaurant_data)
                    logger.info(f'成功爬取餐厅: {restaurant_data.get("name", "未知")}')

                except Exception as e:
                    logger.error(f'处理餐厅数据失败: {str(e)}')
                    continue

        except Exception as e:
            logger.error(f'爬虫运行失败: {str(e)}')

    @log_execution
    def _get_restaurant_urls(self, city_url: str) -> List[str]:
        """获取餐厅URL列表

        Args:
            city_url: 城市页面URL

        Returns:
            餐厅URL列表
        """
        try:
            self.browser.get(city_url)
            time.sleep(self.WAIT_TIME['MEDIUM'])

            col_list = self.browser.ele('.entry-container').eles('.entry-item')
            # 点击打开餐厅列表
            col_list[2].click()  # 第三个选项是餐厅
            time.sleep(self.WAIT_TIME['MEDIUM'])
            # 获取新打开的页面的tab，tabs[0]为新打开网页
            tabs = self.browser.get_tabs()
            page = tabs[0]
            time.sleep(self.WAIT_TIME['MEDIUM'])
            logger.info(f'已获取网页信息 {page}')

            more_res_url = page.ele('.des_wide f_right').ele('.rbox').ele('.f_14').attr('href')
            logger.info(f'更多 {more_res_url}')
            page.get(more_res_url)

            res_url = page.ele('.des_wide f_right').ele('tag:a').attr('href')
            logger.info(f'更多餐厅 {res_url}')
            page.get(res_url)

            # 起始页数，目标页数 - 可配置化
            initial_page = 1
            target_page = 11
            restaurant_url_list = []

            max_res_page = int(page.ele('#gopagetext').next().text)
            logger.info(f'总页数: {max_res_page}')
            
            while initial_page < target_page and initial_page <= max_res_page:
                logger.info(f'正在抓取第 {initial_page} 页')
                res_list = page.ele('.normalbox').eles('.list_mod2')
                time.sleep(self.WAIT_TIME['MEDIUM'])

                # 提取链接并添加到结果列表
                page_urls = []
                for ele in res_list:
                    try:
                        url = ele.ele('.rdetailbox').ele('tag:dt').ele('tag:a').attr('href')
                        page_urls.append(url)
                    except Exception as e:
                        logger.warning(f'提取餐厅链接失败: {str(e)}')
                
                logger.info(f'第 {initial_page} 页获取到 {len(page_urls)} 个餐厅链接')
                restaurant_url_list.extend(page_urls)

                # 翻页
                initial_page += 1
                if initial_page <= max_res_page and initial_page < target_page:
                    self.click_page(initial_page, page)
                    time.sleep(self.WAIT_TIME['MEDIUM'])
            
            page.close()
            logger.info(f'总共获取到 {len(restaurant_url_list)} 个餐厅链接')
            return restaurant_url_list

        except Exception as e:
            logger.error(f'获取餐厅URL列表失败: {str(e)}')
            return []

    @log_execution
    def _get_element_text(self, selector: str, default: str = '', chain_selectors: List[str] = None) -> str:
        """获取元素文本

        Args:
            selector: 基础CSS选择器
            default: 默认返回值
            chain_selectors: 链式选择器列表，用于多层级元素选择

        Returns:
            元素文本或默认值
        """
        try:
            element = self.browser.ele(selector)
            time.sleep(self.WAIT_TIME['SHORT'])
            if chain_selectors:
                for chain_selector in chain_selectors:
                    element = element.ele(chain_selector)
                    time.sleep(self.WAIT_TIME['SHORT'])
            return element.text
        except Exception:
            return default

    @log_execution
    def _get_div_element_text(self, div_elements: List[ChromiumElement], index: int, chain_selectors: List[str] = None,
                              default: str = '') -> str:
        """获取div元素列表中指定索引元素的文本

        Args:
            div_elements: 已获取的元素列表
            index: 元素列表中的索引
            chain_selectors: 链式选择器列表，用于多层级元素选择
            default: 默认返回值

        Returns:
            指定元素的文本或默认值
        """
        try:
            if not div_elements or index >= len(div_elements):
                return default
                
            # 获取指定索引的元素
            element = div_elements[index]

            # 链式处理子元素
            if chain_selectors:
                for chain_selector in chain_selectors:
                    element = element.ele(chain_selector)

            return element.text
        except Exception as e:
            logger.warning(f'获取div元素文本失败 - 索引: {index}, 链式选择器: {chain_selectors}, 错误: {str(e)}')
            return default

    @log_execution
    def _get_all_images(self, name: str) -> Dict:
        """获取酒店所有图片信息"""
        result = {
            'img_oss': [],
            'img_ori': []
        }

        try:
            img_click = self.browser.ele('#detailCarousel')
            img_click.click()
            time.sleep(self.WAIT_TIME['MEDIUM'])

            tabs = self.browser.get_tabs()
            new_page = tabs[0]
            time.sleep(self.WAIT_TIME['SHORT'])
            
            try:
                img_url_list = new_page.ele(
                    ".photolistbox").ele(
                    ".photowrap").eles("tag:li")[:5]

                for index, item in enumerate(img_url_list):
                    item.click()
                    time.sleep(self.WAIT_TIME['SHORT'])

                img_url_li = new_page.ele(
                    ".showimgbg cursor_left").eles("tag:img")
                
                for img in img_url_li:
                    img_url = img.attr('src')
                    if img_url.startswith('//'):
                        img_url = 'https:' + img_url
                    # 提取图片ID
                    img_id = str(uuid.uuid4())[:8]  # 默认使用UUID
                    if '/images/' in img_url:
                        img_parts = img_url.split('/images/')[1]
                        match = re.search(r'([^/]+)_[A-Z]_', img_parts)
                        if match:
                            img_id = match.group(1)

                    result['img_ori'].append(img_url)
                    stored_url = self.download_and_upload_image(
                        img_url,
                        name,
                        f"{name}_{img_id}"
                    )
                    time.sleep(self.WAIT_TIME['SHORT'])
                    if stored_url:
                        result['img_oss'].append(stored_url)
                
            except Exception as e:
                logger.error(f'处理图片详情页失败: {str(e)}')
            
            new_page.close()

        except Exception as e:
            logger.error(f'获取更多图片失败: {str(e)}')

        return result

    @log_execution
    def batch_crawl_city(self, target_province: str = '北京', target_city: str = None) -> None:
        """批量爬取指定省份或城市的景点信息

        Args:
            target_province: 目标省份名称，如果为None则处理北京
            target_city: 目标城市名称，如果为None则处理指定省份的所有城市
        """
        # 读取CSV文件
        csv_file = os.path.join(os.path.dirname(__file__), 'city_urls.csv')
        if not os.path.exists(csv_file):
            logger.error(f'CSV文件不存在: {csv_file}')
            return

        # 处理计数
        processed_cities = 0
        
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            import csv
            reader = csv.DictReader(f)
            for row in reader:
                province = row['省份']
                city = row['城市']

                # 检查是否匹配目标省份或城市
                if target_province and province != target_province:
                    continue
                if target_city and city != target_city:
                    continue

                # 解析URL列表
                urls = row['URLs'].strip().split()  # 修改为正确的列名 'URLs'
                if not urls:
                    logger.warning(f'未找到URL: {province} - {city}')
                    continue

                logger.info(f'处理URL列表: {urls}')  # 添加URL列表日志

                city_url = urls[0]  # 使用第一个URL
                logger.info(f'开始处理: {province} - {city} - {city_url}')

                # 获取城市下所有景点URL
                logger.info(f'正在获取城市 {city} 的餐厅URL列表...')
                self.crawl(province, city, city_url)
                processed_cities += 1
                
        logger.info(f'批量爬取完成，共处理 {processed_cities} 个城市')

    @log_execution
    def _clean_temp_files(self) -> None:
        """清理临时文件和目录"""
        try:
            # 清理data/xiecheng目录
            temp_dir = os.path.join(os.path.dirname(__file__), "data", "xiecheng")
            if os.path.exists(temp_dir):
                for root, dirs, files in os.walk(temp_dir, topdown=False):
                    for name in files:
                        try:
                            os.remove(os.path.join(root, name))
                        except Exception as e:
                            logger.error(f'删除文件失败 {name}: {str(e)}')
                    for name in dirs:
                        try:
                            os.rmdir(os.path.join(root, name))
                        except Exception as e:
                            logger.error(f'删除目录失败 {name}: {str(e)}')
                logger.info(f'已清理临时目录: {temp_dir}')
        except Exception as e:
            logger.error(f'清理临时文件失败: {str(e)}')

    def crawl_single_restaurant(self, restaurant_url: str) -> Dict:
        """爬取单个餐厅信息
        
        Args:
            restaurant_url: 餐厅URL
            
        Returns:
            餐厅信息字典
        """
        try:
            restaurant_data = self.get_restaurant_details(restaurant_url)
            if restaurant_data:
                logger.info(f'成功爬取餐厅: {restaurant_data.get("name", "未知")}')
                return restaurant_data
            else:
                logger.error(f'获取餐厅信息失败: {restaurant_url}')
                return {}
        except Exception as e:
            logger.error(f'爬取单个餐厅失败: {str(e)}')
            return {}


def main():
    # 使用示例
    # 1. 确定是本地模式还是服务器模式
    is_server_mode = False  # 默认本地模式进行调试
    
    # 2. 设置相应的Chrome路径
    chrome_path = None
    if is_server_mode:
        chrome_path = "/usr/bin/google-chrome"  # 使用which chrome查询服务器Chrome位置
    
    # 3. 数据库和MinIO配置
    mysql_config = {
        'username': 'root',
        'password': '123456',
        'database': 'wenlv'
    }
    
    minio_config = {
        'endpoint': '*************:9000',
        'access_key': 'minioadmin',
        'secret_key': 'minioadmin_scyd@lab1234',
        'secure': False
    }
    
    # 4. 初始化爬虫
    crawler = XiechengServerResCrawler(
        base_url="https://you.ctrip.com/",
        chrome_path=chrome_path,
        is_server_mode=is_server_mode,
        mysql_config=mysql_config,
        minio_config=minio_config
    )

    try:
        # 5. 选择运行模式
        if is_server_mode:
            # 服务器批量采集
            for target_province_element in ['河北']:
                for target_city_element in ['邯郸']:
                    crawler.batch_crawl_city(target_province=target_province_element, target_city=target_city_element)
        else:
            # 本地调试模式 - 测试单个餐厅采集
            restaurant_url = "https://you.ctrip.com/food/beijing1/10525888.html"  # 示例URL，需替换为实际测试URL
            restaurant_data = crawler.crawl_single_restaurant(restaurant_url)
            print(f"采集结果: {restaurant_data}")
            
            # 如果需要保存到数据库可以取消下面的注释
            # mysql_tool = MySQLDBTool(**crawler.MYSQL_CONFIG)
            # crawler._save_to_database(mysql_tool, restaurant_data)
            # logger.info(f'成功爬取餐厅并保存: {restaurant_data["name"]}')
            
            # 也可以测试批量爬取少量数据
            # crawler.batch_crawl_city(target_province="北京", target_city="北京")

    finally:
        # 6. 清理并关闭浏览器
        crawler._clean_temp_files()
        crawler.browser.quit()


if __name__ == "__main__":
    main()
